lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@expo-google-fonts/space-grotesk':
        specifier: ^0.2.2
        version: 0.2.3
      '@expo/metro-runtime':
        specifier: ~5.0.4
        version: 5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      '@fortawesome/fontawesome-svg-core':
        specifier: ^6.7.2
        version: 6.7.2
      '@fortawesome/free-brands-svg-icons':
        specifier: ^6.6.0
        version: 6.7.2
      '@fortawesome/free-regular-svg-icons':
        specifier: ^6.6.0
        version: 6.7.2
      '@fortawesome/free-solid-svg-icons':
        specifier: ^6.7.2
        version: 6.7.2
      '@fortawesome/react-native-fontawesome':
        specifier: ^0.3.2
        version: 0.3.2(@fortawesome/fontawesome-svg-core@6.7.2)(react-native-svg@15.11.2(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      '@react-native-async-storage/async-storage':
        specifier: 2.1.2
        version: 2.1.2(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      '@react-native-community/datetimepicker':
        specifier: ^8.3.0
        version: 8.3.0(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      '@react-native-community/netinfo':
        specifier: ^11.4.1
        version: 11.4.1(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      '@react-native-google-signin/google-signin':
        specifier: ^13.2.0
        version: 13.2.0(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-dom@19.0.0(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      '@react-native-picker/picker':
        specifier: ^2.11.0
        version: 2.11.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      '@react-navigation/bottom-tabs':
        specifier: ^7.3.13
        version: 7.3.13(@react-navigation/native@7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-screens@4.10.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      '@react-navigation/native':
        specifier: ^7.1.9
        version: 7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      '@react-navigation/native-stack':
        specifier: ^7.3.13
        version: 7.3.13(@react-navigation/native@7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-screens@4.10.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      '@sentry/react-native':
        specifier: ~6.10.0
        version: 6.10.0(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      '@shopify/flash-list':
        specifier: 1.7.6
        version: 1.7.6(@babel/runtime@8.0.0-alpha.17)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      '@shopify/react-native-skia':
        specifier: 2.0.0-next.4
        version: 2.0.0-next.4(react-native-reanimated@3.17.5(@babel/core@7.26.9)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      '@supabase/supabase-js':
        specifier: ^2.49.5-next.5
        version: 2.49.5-next.5
      aes-js:
        specifier: ^3.1.2
        version: 3.1.2
      apisauce:
        specifier: 3.0.1
        version: 3.0.1
      aws-amplify:
        specifier: ^6.14.4
        version: 6.14.4
      date-fns:
        specifier: ^2.30.0
        version: 2.30.0
      denque:
        specifier: ^2.1.0
        version: 2.1.0
      expo:
        specifier: ^53.0.9
        version: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      expo-auth-session:
        specifier: ^6.1.5
        version: 6.1.5(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      expo-build-properties:
        specifier: ~0.14.6
        version: 0.14.6(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))
      expo-font:
        specifier: ~13.3.1
        version: 13.3.1(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react@19.0.0)
      expo-linking:
        specifier: ~7.1.5
        version: 7.1.5(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      expo-localization:
        specifier: ~16.1.5
        version: 16.1.5(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react@19.0.0)
      expo-secure-store:
        specifier: ^14.2.3
        version: 14.2.3(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))
      expo-splash-screen:
        specifier: ~0.30.8
        version: 0.30.8(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))
      expo-status-bar:
        specifier: ~2.2.3
        version: 2.2.3(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      expo-web-browser:
        specifier: ^14.1.6
        version: 14.1.6(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      fast-fuzzy:
        specifier: ^1.12.0
        version: 1.12.0
      i18n-js:
        specifier: ^4.4.3
        version: 4.5.1
      mobx:
        specifier: 6.10.2
        version: 6.10.2
      mobx-react-lite:
        specifier: 4.0.5
        version: 4.0.5(mobx@6.10.2)(react-dom@19.0.0(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      mobx-state-tree:
        specifier: 5.3.0
        version: 5.3.0(mobx@6.10.2)
      react:
        specifier: 19.0.0
        version: 19.0.0
      react-dom:
        specifier: 19.0.0
        version: 19.0.0(react@19.0.0)
      react-native:
        specifier: ~0.79.2
        version: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-native-elements:
        specifier: ^3.4.3
        version: 3.4.3(react-native-safe-area-context@5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-vector-icons@10.2.0)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-email-link:
        specifier: ^1.16.1
        version: 1.16.1(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-gesture-handler:
        specifier: ~2.24.0
        version: 2.24.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-get-random-values:
        specifier: ^1.11.0
        version: 1.11.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      react-native-onboarding-swiper:
        specifier: ^1.3.0
        version: 1.3.0(prop-types@15.8.1)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-prompt-android:
        specifier: ^1.1.0
        version: 1.1.0
      react-native-reanimated:
        specifier: ~3.17.5
        version: 3.17.5(@babel/core@7.26.9)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-safe-area-context:
        specifier: ~5.4.0
        version: 5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-screens:
        specifier: ~4.10.0
        version: 4.10.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-svg:
        specifier: 15.11.2
        version: 15.11.2(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-url-polyfill:
        specifier: ^2.0.0
        version: 2.0.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      react-native-web:
        specifier: ~0.20.0
        version: 0.20.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)
      react-native-webview:
        specifier: ~13.13.5
        version: 13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      victory-native:
        specifier: ^41.12.0
        version: 41.16.1(@shopify/react-native-skia@2.0.0-next.4(react-native-reanimated@3.17.5(@babel/core@7.26.9)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(@types/react@19.0.14)(react-native-gesture-handler@2.24.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-reanimated@3.17.5(@babel/core@7.26.9)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
    devDependencies:
      '@babel/core':
        specifier: ^7.20.0
        version: 7.26.9
      '@babel/plugin-transform-arrow-functions':
        specifier: ^7.0.0
        version: 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-shorthand-properties':
        specifier: ^7.0.0
        version: 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-template-literals':
        specifier: ^7.0.0
        version: 7.26.8(@babel/core@7.26.9)
      '@babel/preset-env':
        specifier: ^7.20.0
        version: 7.26.9(@babel/core@7.26.9)
      '@babel/runtime':
        specifier: 8.0.0-alpha.17
        version: 8.0.0-alpha.17
      '@testing-library/react-native':
        specifier: ^12.5.2
        version: 12.9.0(jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3)))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react-test-renderer@19.0.0(react@19.0.0))(react@19.0.0)
      '@types/i18n-js':
        specifier: 3.8.2
        version: 3.8.2
      '@types/jest':
        specifier: ^29.2.1
        version: 29.5.14
      '@types/react':
        specifier: ~19.0.0
        version: 19.0.14
      '@types/react-native-onboarding-swiper':
        specifier: ^1.1.9
        version: 1.1.9(@babel/core@7.26.9)(react@19.0.0)
      '@typescript-eslint/eslint-plugin':
        specifier: ^5.59.0
        version: 5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.17.0)(typescript@5.8.3))(eslint@8.17.0)(typescript@5.8.3)
      '@typescript-eslint/parser':
        specifier: ^5.59.0
        version: 5.62.0(eslint@8.17.0)(typescript@5.8.3)
      babel-jest:
        specifier: ^29.2.1
        version: 29.7.0(@babel/core@7.26.9)
      eslint:
        specifier: 8.17.0
        version: 8.17.0
      eslint-config-prettier:
        specifier: 8.5.0
        version: 8.5.0(eslint@8.17.0)
      eslint-config-standard:
        specifier: 17.0.0
        version: 17.0.0(eslint-plugin-import@2.26.0(@typescript-eslint/parser@5.62.0(eslint@8.17.0)(typescript@5.8.3))(eslint@8.17.0))(eslint-plugin-n@15.7.0(eslint@8.17.0))(eslint-plugin-promise@6.0.0(eslint@8.17.0))(eslint@8.17.0)
      eslint-plugin-import:
        specifier: 2.26.0
        version: 2.26.0(@typescript-eslint/parser@5.62.0(eslint@8.17.0)(typescript@5.8.3))(eslint@8.17.0)
      eslint-plugin-n:
        specifier: ^15.0.0
        version: 15.7.0(eslint@8.17.0)
      eslint-plugin-promise:
        specifier: 6.0.0
        version: 6.0.0(eslint@8.17.0)
      eslint-plugin-react:
        specifier: 7.30.0
        version: 7.30.0(eslint@8.17.0)
      eslint-plugin-react-native:
        specifier: 4.0.0
        version: 4.0.0(eslint@8.17.0)
      eslint-plugin-reactotron:
        specifier: ^0.1.2
        version: 0.1.6(reactotron-core-client@2.9.7)
      jest:
        specifier: ^29.2.1
        version: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))
      jest-expo:
        specifier: ~53.0.5
        version: 53.0.5(@babel/core@7.26.9)(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3)))(react-dom@19.0.0(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)(webpack@5.98.0)
      patch-package:
        specifier: ^8.0.0
        version: 8.0.0
      postinstall-prepare:
        specifier: 1.0.1
        version: 1.0.1
      prettier:
        specifier: 2.8.8
        version: 2.8.8
      react-native-vector-icons:
        specifier: ^10.2.0
        version: 10.2.0
      react-test-renderer:
        specifier: 19.0.0
        version: 19.0.0(react@19.0.0)
      reactotron-core-client:
        specifier: ^2.9.7
        version: 2.9.7
      reactotron-mst:
        specifier: ^3.1.11
        version: 3.1.11(mobx-state-tree@5.3.0(mobx@6.10.2))(mobx@6.10.2)(reactotron-core-client@2.9.7)
      reactotron-react-js:
        specifier: ^3.3.16
        version: 3.3.16
      reactotron-react-native:
        specifier: ^5.1.13
        version: 5.1.13(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      ts-jest:
        specifier: ^29.1.1
        version: 29.2.6(@babel/core@7.26.9)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.26.9))(jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3)))(typescript@5.8.3)
      ts-node:
        specifier: ^10.9.2
        version: 10.9.2(@types/node@22.13.5)(typescript@5.8.3)
      typescript:
        specifier: ~5.8.3
        version: 5.8.3

packages:

  '@0no-co/graphql.web@1.1.1':
    resolution: {integrity: sha512-F2i3xdycesw78QCOBHmpTn7eaD2iNXGwB2gkfwxcOfBbeauYpr8RBSyJOkDrFtKtVRMclg8Sg3n1ip0ACyUuag==}
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0
    peerDependenciesMeta:
      graphql:
        optional: true

  '@ampproject/remapping@2.3.0':
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==}
    engines: {node: '>=6.0.0'}

  '@aws-amplify/analytics@7.0.80':
    resolution: {integrity: sha512-KJ22bNnlW+mIAYl+2nM3ZTXhM6svAHUnT0Oi1yzGAwDO0wGYiIUEtV4Vx+21iOZrS3RDrJSEtdynQzfD0eQpLg==}
    peerDependencies:
      '@aws-amplify/core': ^6.1.0

  '@aws-amplify/api-graphql@4.7.15':
    resolution: {integrity: sha512-6lU3Cw7ExvEHFh2jwEgSIA5ws+KyqahmP00u0++W59Lmcg46Y+TJZ27HUlIZkc/QbOk3/HN9M6YopdRa0LqeCg==}

  '@aws-amplify/api-rest@4.1.4':
    resolution: {integrity: sha512-3ajXDy+7XTZriiFR+vqtsnQ5tf4RskkEMpUp++eT+R+pBvudqlAkqe4lB0ABXB1/r1+kXJqDHWEgaXT3DZQZJQ==}
    peerDependencies:
      '@aws-amplify/core': ^6.1.0

  '@aws-amplify/api@6.3.11':
    resolution: {integrity: sha512-Osp264EBrrp6Rp87Df+y7ngv33kriOY2LxQU40lccUfk3CpFN2nrMbIPZ3JGH0n265L1CGJw3XjpeCLmO3s8ng==}
    peerDependencies:
      '@aws-amplify/core': ^6.1.0

  '@aws-amplify/auth@6.12.4':
    resolution: {integrity: sha512-3qi3dKJUdYVDS3nCO2YeIK4Djj0vzDEjv2ZpfraYNVBUGXj4GnL7wSUm7r1VD3SYqnHgzoAIdW/73P5ormEMfw==}
    peerDependencies:
      '@aws-amplify/core': ^6.1.0

  '@aws-amplify/core@6.11.4':
    resolution: {integrity: sha512-qkLyu3GxmJNH6gk9WwKEkOjQ0uUMyOuYqJgf97M6DV6btfoqBFY0T18wd+JEqjKm8awk4liRObU4b9/FQPuS9w==}

  '@aws-amplify/data-schema-types@1.2.0':
    resolution: {integrity: sha512-1hy2r7jl3hQ5J/CGjhmPhFPcdGSakfme1ZLjlTMJZILfYifZLSlGRKNCelMb3J5N9203hyeT5XDi5yR47JL1TQ==}

  '@aws-amplify/data-schema@1.18.0':
    resolution: {integrity: sha512-/beeCfX9SvDaa+5mv/bk/oqa7SIRk/9sFflN4b1ZhwDwlOsF5if6Gg3TTNEKCtsCwyBkKO6r5AIDb3cg4TIpuw==}

  '@aws-amplify/datastore@5.0.82':
    resolution: {integrity: sha512-vdCUcpeTN7Cxuke16pZGQg4/zV0z3zs91AsLTkJ6HiOMnFDrqbWdDo+3FB5Mrw0Z6QnBKTUxeZ00K0vn6rgzlQ==}
    peerDependencies:
      '@aws-amplify/core': ^6.1.0

  '@aws-amplify/notifications@2.0.80':
    resolution: {integrity: sha512-6ePdufP1gwb//OvuyD/YsnXUvDRXd1sI3+R+Mu+R34IxL34hS1ovEJcM7aAEodvC9bc0jfcyGVoHLxFaRu2nYw==}
    peerDependencies:
      '@aws-amplify/core': ^6.1.0

  '@aws-amplify/storage@6.8.4':
    resolution: {integrity: sha512-aplukG1r4dAnKQmvTwc6zS9Oi3CuljRDbyxqTVKPv3oIqgZguTaWyfL6Ta+2XTj5fK816uLL7Rg15Lq1qvU0Ag==}
    peerDependencies:
      '@aws-amplify/core': ^6.1.0

  '@aws-crypto/crc32@5.2.0':
    resolution: {integrity: sha512-nLbCWqQNgUiwwtFsen1AdzAtvuLRsQS8rYgMuxCrdKf9kOssamGLuPwyTY9wyYblNr9+1XM8v6zoDTPPSIeANg==}
    engines: {node: '>=16.0.0'}

  '@aws-crypto/sha256-browser@5.2.0':
    resolution: {integrity: sha512-AXfN/lGotSQwu6HNcEsIASo7kWXZ5HYWvfOmSNKDsEqC4OashTp8alTmaz+F7TC2L083SFv5RdB+qU3Vs1kZqw==}

  '@aws-crypto/sha256-js@5.2.0':
    resolution: {integrity: sha512-FFQQyu7edu4ufvIZ+OadFpHHOt+eSTBaYaki44c+akjg7qZg9oOQeLlk77F6tSYqjDAFClrHJk9tMf0HdVyOvA==}
    engines: {node: '>=16.0.0'}

  '@aws-crypto/supports-web-crypto@5.2.0':
    resolution: {integrity: sha512-iAvUotm021kM33eCdNfwIN//F77/IADDSs58i+MDaOqFrVjZo9bAal0NK7HurRuWLLpF1iLX7gbWrjHjeo+YFg==}

  '@aws-crypto/util@5.2.0':
    resolution: {integrity: sha512-4RkU9EsI6ZpBve5fseQlGNUWKMa1RLPQ1dnjnQoe07ldfIzcsGb5hC5W0Dm7u423KWzawlrpbjXBrXCEv9zazQ==}

  '@aws-sdk/client-firehose@3.621.0':
    resolution: {integrity: sha512-XAjAkXdb35PDvBYph609Fxn4g00HYH/U6N4+KjF9gLQrdTU+wkjf3D9YD02DZNbApJVcu4eIxWh/8M25YkW02A==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-kinesis@3.621.0':
    resolution: {integrity: sha512-53Omt/beFmTQPjQNpMuPMk5nMzYVsXCRiO+MeqygZEKYG1fWw/UGluCWVbi7WjClOHacsW8lQcsqIRvkPDFNag==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-personalize-events@3.621.0':
    resolution: {integrity: sha512-qkVkqYvOe3WVuVNL/gRITGYFfHJCx2ijGFK7H3hNUJH3P4AwskmouAd1pWf+3cbGedRnj2is7iw7E602LeJIHA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sso-oidc@3.621.0':
    resolution: {integrity: sha512-mMjk3mFUwV2Y68POf1BQMTF+F6qxt5tPu6daEUCNGC9Cenk3h2YXQQoS4/eSyYzuBiYk3vx49VgleRvdvkg8rg==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.621.0

  '@aws-sdk/client-sso@3.621.0':
    resolution: {integrity: sha512-xpKfikN4u0BaUYZA9FGUMkkDmfoIP0Q03+A86WjqDWhcOoqNA1DkHsE4kZ+r064ifkPUfcNuUvlkVTEoBZoFjA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/client-sts@3.621.0':
    resolution: {integrity: sha512-707uiuReSt+nAx6d0c21xLjLm2lxeKc7padxjv92CIrIocnQSlJPxSCM7r5zBhwiahJA6MNQwmTl2xznU67KgA==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/core@3.621.0':
    resolution: {integrity: sha512-CtOwWmDdEiINkGXD93iGfXjN0WmCp9l45cDWHHGa8lRgEDyhuL7bwd/pH5aSzj0j8SiQBG2k0S7DHbd5RaqvbQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-env@3.620.1':
    resolution: {integrity: sha512-ExuILJ2qLW5ZO+rgkNRj0xiAipKT16Rk77buvPP8csR7kkCflT/gXTyzRe/uzIiETTxM7tr8xuO9MP/DQXqkfg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-http@3.621.0':
    resolution: {integrity: sha512-/jc2tEsdkT1QQAI5Dvoci50DbSxtJrevemwFsm0B73pwCcOQZ5ZwwSdVqGsPutzYzUVx3bcXg3LRL7jLACqRIg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-ini@3.621.0':
    resolution: {integrity: sha512-0EWVnSc+JQn5HLnF5Xv405M8n4zfdx9gyGdpnCmAmFqEDHA8LmBdxJdpUk1Ovp/I5oPANhjojxabIW5f1uU0RA==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.621.0

  '@aws-sdk/credential-provider-node@3.621.0':
    resolution: {integrity: sha512-4JqpccUgz5Snanpt2+53hbOBbJQrSFq7E1sAAbgY6BKVQUsW5qyXqnjvSF32kDeKa5JpBl3bBWLZl04IadcPHw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-process@3.620.1':
    resolution: {integrity: sha512-hWqFMidqLAkaV9G460+1at6qa9vySbjQKKc04p59OT7lZ5cO5VH5S4aI05e+m4j364MBROjjk2ugNvfNf/8ILg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-sso@3.621.0':
    resolution: {integrity: sha512-Kza0jcFeA/GEL6xJlzR2KFf1PfZKMFnxfGzJzl5yN7EjoGdMijl34KaRyVnfRjnCWcsUpBWKNIDk9WZVMY9yiw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/credential-provider-web-identity@3.621.0':
    resolution: {integrity: sha512-w7ASSyfNvcx7+bYGep3VBgC3K6vEdLmlpjT7nSIHxxQf+WSdvy+HynwJosrpZax0sK5q0D1Jpn/5q+r5lwwW6w==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sts': ^3.621.0

  '@aws-sdk/middleware-host-header@3.620.0':
    resolution: {integrity: sha512-VMtPEZwqYrII/oUkffYsNWY9PZ9xpNJpMgmyU0rlDQ25O1c0Hk3fJmZRe6pEkAJ0omD7kLrqGl1DUjQVxpd/Rg==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-logger@3.609.0':
    resolution: {integrity: sha512-S62U2dy4jMDhDFDK5gZ4VxFdWzCtLzwbYyFZx2uvPYTECkepLUfzLic2BHg2Qvtu4QjX+oGE3P/7fwaGIsGNuQ==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-recursion-detection@3.620.0':
    resolution: {integrity: sha512-nh91S7aGK3e/o1ck64sA/CyoFw+gAYj2BDOnoNa6ouyCrVJED96ZXWbhye/fz9SgmNUZR2g7GdVpiLpMKZoI5w==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/middleware-user-agent@3.620.0':
    resolution: {integrity: sha512-bvS6etn+KsuL32ubY5D3xNof1qkenpbJXf/ugGXbg0n98DvDFQ/F+SMLxHgbnER5dsKYchNnhmtI6/FC3HFu/A==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/region-config-resolver@3.614.0':
    resolution: {integrity: sha512-vDCeMXvic/LU0KFIUjpC3RiSTIkkvESsEfbVHiHH0YINfl8HnEqR5rj+L8+phsCeVg2+LmYwYxd5NRz4PHxt5g==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/token-providers@3.614.0':
    resolution: {integrity: sha512-okItqyY6L9IHdxqs+Z116y5/nda7rHxLvROxtAJdLavWTYDydxrZstImNgGWTeVdmc0xX2gJCI77UYUTQWnhRw==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      '@aws-sdk/client-sso-oidc': ^3.614.0

  '@aws-sdk/types@3.387.0':
    resolution: {integrity: sha512-YTjFabNwjTF+6yl88f0/tWff018qmmgMmjlw45s6sdVKueWxdxV68U7gepNLF2nhaQPZa6FDOBoA51NaviVs0Q==}
    engines: {node: '>=14.0.0'}

  '@aws-sdk/types@3.398.0':
    resolution: {integrity: sha512-r44fkS+vsEgKCuEuTV+TIk0t0m5ZlXHNjSDYEUvzLStbbfUFiNus/YG4UCa0wOk9R7VuQI67badsvvPeVPCGDQ==}
    engines: {node: '>=14.0.0'}

  '@aws-sdk/types@3.609.0':
    resolution: {integrity: sha512-+Tqnh9w0h2LcrUsdXyT1F8mNhXz+tVYBtP19LpeEGntmvHwa2XzvLUCWpoIAIVsHp5+HdB2X9Sn0KAtmbFXc2Q==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/types@3.734.0':
    resolution: {integrity: sha512-o11tSPTT70nAkGV1fN9wm/hAIiLPyWX6SuGf+9JyTp7S/rC2cFWhR26MvA69nplcjNaXVzB0f+QFrLXXjOqCrg==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/util-endpoints@3.614.0':
    resolution: {integrity: sha512-wK2cdrXHH4oz4IomV/yrGkftU9A+ITB6nFL+rxxyO78is2ifHJpFdV4aqk4LSkXYPi6CXWNru/Dqc7yiKXgJPw==}
    engines: {node: '>=16.0.0'}

  '@aws-sdk/util-locate-window@3.723.0':
    resolution: {integrity: sha512-Yf2CS10BqK688DRsrKI/EO6B8ff5J86NXe4C+VCysK7UOgN0l1zOTeTukZ3H8Q9tYYX3oaF1961o8vRkFm7Nmw==}
    engines: {node: '>=18.0.0'}

  '@aws-sdk/util-user-agent-browser@3.609.0':
    resolution: {integrity: sha512-fojPU+mNahzQ0YHYBsx0ZIhmMA96H+ZIZ665ObU9tl+SGdbLneVZVikGve+NmHTQwHzwkFsZYYnVKAkreJLAtA==}

  '@aws-sdk/util-user-agent-node@3.614.0':
    resolution: {integrity: sha512-15ElZT88peoHnq5TEoEtZwoXTXRxNrk60TZNdpl/TUBJ5oNJ9Dqb5Z4ryb8ofN6nm9aFf59GVAerFDz8iUoHBA==}
    engines: {node: '>=16.0.0'}
    peerDependencies:
      aws-crt: '>=1.0.0'
    peerDependenciesMeta:
      aws-crt:
        optional: true

  '@babel/code-frame@7.10.4':
    resolution: {integrity: sha512-vG6SvB6oYEhvgisZNFRmRCUkLz11c7rp+tbNTynGqc6mS1d5ATd/sGyV6W0KZZnXRKMTzZDRgQT3Ou9jhpAfUg==}

  '@babel/code-frame@7.26.2':
    resolution: {integrity: sha512-RJlIHRueQgwWitWgF8OdFYGZX328Ax5BCemNGlqHfplnRT9ESi8JkFlvaVYbS+UubVY6dpv87Fs2u5M29iNFVQ==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.26.8':
    resolution: {integrity: sha512-oH5UPLMWR3L2wEFLnFJ1TZXqHufiTKAiLfqw5zkhS4dKXLJ10yVztfil/twG8EDTA4F/tvVNw9nOl4ZMslB8rQ==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.26.9':
    resolution: {integrity: sha512-lWBYIrF7qK5+GjY5Uy+/hEgp8OJWOD/rpy74GplYRhEauvbHDeFB8t5hPOZxCZ0Oxf4Cc36tK51/l3ymJysrKw==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.26.9':
    resolution: {integrity: sha512-kEWdzjOAUMW4hAyrzJ0ZaTOu9OmpyDIQicIh0zg0EEcEkYXZb2TjtBhnHi2ViX7PKwZqF4xwqfAm299/QMP3lg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.25.9':
    resolution: {integrity: sha512-gv7320KBUFJz1RnylIg5WWYPRXKZ884AGkYpgpWW02TH66Dl+HaC1t1CKd0z3R4b6hdYEcmrNZHUmfCP+1u3/g==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.26.5':
    resolution: {integrity: sha512-IXuyn5EkouFJscIDuFF5EsiSolseme1s0CZB+QxVugqJLYmKdxI1VfIBOst0SUu4rnk2Z7kqTwmoO1lp3HIfnA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.26.9':
    resolution: {integrity: sha512-ubbUqCofvxPRurw5L8WTsCLSkQiVpov4Qx0WMA+jUN+nXBK8ADPlJO1grkFw5CWKC5+sZSOfuGMdX1aI1iT9Sg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.26.3':
    resolution: {integrity: sha512-G7ZRb40uUgdKOQqPLjfD12ZmGA54PzqDFUv2BKImnC9QIfGhIHKvVML0oN8IUiDq4iRqpq74ABpvOaerfWdong==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.6.3':
    resolution: {integrity: sha512-HK7Bi+Hj6H+VTHA3ZvBis7V/6hu9QuTrnMXNybfUf2iiuU/N97I8VjB+KbhFF8Rld/Lx5MzoCwPCpPjfK+n8Cg==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-member-expression-to-functions@7.25.9':
    resolution: {integrity: sha512-wbfdZ9w5vk0C0oyHqAJbc62+vet5prjj01jjJ8sKn3j9h3MQQlflEdXYvuqRWjHnM12coDEqiC1IRCi0U/EKwQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.25.9':
    resolution: {integrity: sha512-tnUA4RsrmflIM6W6RFTLFSXITtl0wKjgpnLgXyowocVPrbYrLUXSBXDgTs8BlbmIzIdlBySRQjINYs2BAkiLtw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.26.0':
    resolution: {integrity: sha512-xO+xu6B5K2czEnQye6BHA7DolFFmS3LB7stHZFaOLb1pAwO1HWLS8fXA+eh0A2yIvltPVmx3eNNDBJA2SLHXFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.25.9':
    resolution: {integrity: sha512-FIpuNaz5ow8VyrYcnXQTDRGvV6tTjkNtCK/RYNDXGSLlUD6cBuQTSw43CShGxjvfBTfcUA/r6UhUCbtYqkhcuQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.26.5':
    resolution: {integrity: sha512-RS+jZcRdZdRFzMyr+wcsaqOmld1/EqTghfaBGQQd/WnRdzdlvSZ//kF7U8VQTxf1ynZ4cjUcYgjVGx13ewNPMg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.25.9':
    resolution: {integrity: sha512-IZtukuUeBbhgOcaW2s06OXTzVNJR0ybm4W5xC1opWFFJMZbwRj5LCk+ByYH7WdZPZTt8KnFwA8pvjN2yqcPlgw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.26.5':
    resolution: {integrity: sha512-bJ6iIVdYX1YooY2X7w1q6VITt+LnUILtNk7zT78ykuwStx8BauCzxvFqFaHjOpW1bVnSUM1PN1f0p5P21wHxvg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    resolution: {integrity: sha512-K4Du3BFa3gvyhzgPcntrkDgZzQaq6uozzcpGbOO1OEJaI+EJdqWIMTLgFgQf6lrfiDFo5FU+BxKepI9RmZqahA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.25.9':
    resolution: {integrity: sha512-4A/SCr/2KLd5jrtOMFzaKjVtAei3+2r/NChoBNoZ3EyP/+GlhoaEGoWOZUmFmoITP7zOJyHIMm+DYRd8o3PvHA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.25.9':
    resolution: {integrity: sha512-Ed61U6XJc3CVRfkERJWDz4dJwKe7iLmmJsbOGu9wSloNSFttHV0I8g6UAgb7qnK5ly5bGLPd4oXZlxCdANBOWQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.25.9':
    resolution: {integrity: sha512-e/zv1co8pp55dNdEcCynfj9X7nyUKUXoUEwfXqaZt0omVOmDe9oOTdKStH4GmAw6zxMFs50ZayuMfHDKlO7Tfw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.25.9':
    resolution: {integrity: sha512-ETzz9UTjQSTmw39GboatdymDq4XIQbR8ySgVrylRhPOFpsd+JrKHIuF0de7GCWmem+T4uC5z7EZguod7Wj4A4g==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.26.9':
    resolution: {integrity: sha512-Mz/4+y8udxBKdmzt/UjPACs4G3j5SshJJEFFKxlCGPydG4JAHXxjWjAwjd09tf6oINvl1VfMJo+nB7H2YKQ0dA==}
    engines: {node: '>=6.9.0'}

  '@babel/highlight@7.25.9':
    resolution: {integrity: sha512-llL88JShoCsth8fF8R4SJnIn+WLvR6ccFxu1H3FlMhDontdcmZWf2HgIZ7AIqV3Xcck1idlohrN4EUBQz6klbw==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.26.9':
    resolution: {integrity: sha512-81NWa1njQblgZbQHxWHpxxCzNsa3ZwvFqpUg7P+NNUU6f3UU2jBEg4OlF/J6rl8+PQGh1q6/zWScd001YwcA5A==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9':
    resolution: {integrity: sha512-ZkRyVkThtxQ/J6nv3JFYv1RYY+JT5BvU0y3k5bWrmuG4woXypRa4PXmm9RhOwodRkYFWqC0C0cqcJ4OqR7kW+g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9':
    resolution: {integrity: sha512-MrGRLZxLD/Zjj0gdU15dfs+HH/OXvnw/U4jJD8vpcP2CJQapPEv1IWwjc/qMg7ItBlPwSv1hRBbb7LeuANdcnw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9':
    resolution: {integrity: sha512-2qUwwfAFpJLZqxd02YW9btUCZHl+RFvdDkNfZwaIJrvB8Tesjsk8pEQkTvGwZXLqXUx/2oyY3ySRhm6HOXuCug==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.25.9':
    resolution: {integrity: sha512-6xWgLZTJXwilVjlnV7ospI3xi+sl8lN8rXXbBD6vYn3UYDlGsag8wrZkKcSI8G6KgqKP7vNFaDgeDnfAABq61g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.9':
    resolution: {integrity: sha512-aLnMXYPnzwwqhYSCyXfKkIkYgJ8zv9RK+roo9DkTXz38ynIhd9XCbN08s3MGvqL2MYGVUGdRQLL/JqBIeJhJBg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-proposal-decorators@7.25.9':
    resolution: {integrity: sha512-smkNLL/O1ezy9Nhy4CNosc4Va+1wo5w4gzSZeLe6y6dM4mmHfYOCPolXQPHQxonZCF+ZyebxN9vqOolkYrSn5g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-export-default-from@7.25.9':
    resolution: {integrity: sha512-ykqgwNfSnNOB+C8fV5X4mG3AVmvu+WVxcaU9xHHtBb7PCrPeweMmPjGsn8eMaeJg6SJuoUuZENeeSWaarWqonQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2':
    resolution: {integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-async-generators@7.8.4':
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-bigint@7.8.3':
    resolution: {integrity: sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-properties@7.12.13':
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-static-block@7.14.5':
    resolution: {integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-decorators@7.25.9':
    resolution: {integrity: sha512-ryzI0McXUPJnRCvMo4lumIKZUzhYUO/ScI+Mz4YVaTLt04DHNSjEUjKVvbzQjZFLuod/cYEc07mJWhzl6v4DPg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-dynamic-import@7.8.3':
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-export-default-from@7.25.9':
    resolution: {integrity: sha512-9MhJ/SMTsVqsd69GyQg89lYR4o9T+oDGv5F6IsigxxqFVOyR/IflDLYP8WDI1l8fkhNGGktqkvL5qwNCtGEpgQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-flow@7.26.0':
    resolution: {integrity: sha512-B+O2DnPc0iG+YXFqOxv2WNuNU97ToWjOomUQ78DouOENWUaM5sVrmet9mcomUGQFwpJd//gvUagXBSdzO1fRKg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.26.0':
    resolution: {integrity: sha512-QCWT5Hh830hK5EQa7XzuqIkQU9tT/whqbDz7kuaZMHFl1inRRg7JnuAEOQ0Ur0QUl0NufCk1msK2BeY79Aj/eg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.26.0':
    resolution: {integrity: sha512-e2dttdsJ1ZTpi3B9UYGLw41hifAubg19AtCu/2I/F1QNVclOBr1dYpTdmdyZ84Xiz43BS/tCUkMAZNLv12Pi+A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-meta@7.10.4':
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-json-strings@7.8.3':
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-jsx@7.25.9':
    resolution: {integrity: sha512-ld6oezHQMZsZfp6pWtbjaNDF2tiiCYYDqQszHt5VV437lewP9aSi2Of99CK0D0XB21k7FLgnLcmQKyKzynfeAA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4':
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3':
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-numeric-separator@7.10.4':
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-object-rest-spread@7.8.3':
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-catch-binding@7.8.3':
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-chaining@7.8.3':
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-private-property-in-object@7.14.5':
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-top-level-await@7.14.5':
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-typescript@7.25.9':
    resolution: {integrity: sha512-hjMgRy5hb8uJJjUcdWunWVcoi9bGpJp8p5Ol1229PoN6aytsLwNMgmdftO23wnCLMfVmTwZDWMPNq/D1SY60JQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6':
    resolution: {integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-arrow-functions@7.25.9':
    resolution: {integrity: sha512-6jmooXYIwn9ca5/RylZADJ+EnSxVUS5sjeJ9UPk6RWRzXCmOJCy6dqItPJFpw2cuCangPK4OYr5uhGKcmrm5Qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-generator-functions@7.26.8':
    resolution: {integrity: sha512-He9Ej2X7tNf2zdKMAGOsmg2MrFc+hfoAhd3po4cWfo/NWjzEAKa0oQruj1ROVUdl0e6fb6/kE/G3SSxE0lRJOg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.25.9':
    resolution: {integrity: sha512-NT7Ejn7Z/LjUH0Gv5KsBCxh7BH3fbLTV0ptHvpeMvrt3cPThHfJfst9Wrb7S8EvJ7vRTFI7z+VAvFVEQn/m5zQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.26.5':
    resolution: {integrity: sha512-chuTSY+hq09+/f5lMj8ZSYgCFpppV2CbYrhNFJ1BFoXpiWPnnAb7R0MqrafCpN8E1+YRrtM1MXZHJdIx8B6rMQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.25.9':
    resolution: {integrity: sha512-1F05O7AYjymAtqbsFETboN1NvBdcnzMerO+zlMyJBEz6WkMdejvGWw9p05iTSjC85RLlBseHHQpYaM4gzJkBGg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-properties@7.25.9':
    resolution: {integrity: sha512-bbMAII8GRSkcd0h0b4X+36GksxuheLFjP65ul9w6C3KgAamI3JqErNgSrosX6ZPj+Mpim5VvEbawXxJCyEUV3Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-static-block@7.26.0':
    resolution: {integrity: sha512-6J2APTs7BDDm+UMqP1useWqhcRAXo0WIoVj26N7kPFB6S73Lgvyka4KTZYIxtgYXiN5HTyRObA72N2iu628iTQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-transform-classes@7.25.9':
    resolution: {integrity: sha512-mD8APIXmseE7oZvZgGABDyM34GUmK45Um2TXiBUt7PnuAxrgoSVf123qUzPxEr/+/BHrRn5NMZCdE2m/1F8DGg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.25.9':
    resolution: {integrity: sha512-HnBegGqXZR12xbcTHlJ9HGxw1OniltT26J5YpfruGqtUHlz/xKf/G2ak9e+t0rVqrjXa9WOhvYPz1ERfMj23AA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.25.9':
    resolution: {integrity: sha512-WkCGb/3ZxXepmMiX101nnGiU+1CAdut8oHyEOHxkKuS1qKpU2SMXE2uSvfz8PBuLd49V6LEsbtyPhWC7fnkgvQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.25.9':
    resolution: {integrity: sha512-t7ZQ7g5trIgSRYhI9pIJtRl64KHotutUJsh4Eze5l7olJv+mRSg4/MmbZ0tv1eeqRbdvo/+trvJD/Oc5DmW2cA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.25.9':
    resolution: {integrity: sha512-LZxhJ6dvBb/f3x8xwWIuyiAHy56nrRG3PeYTpBkkzkYRRQ6tJLu68lEF5VIqMUZiAV7a8+Tb78nEoMCMcqjXBw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9':
    resolution: {integrity: sha512-0UfuJS0EsXbRvKnwcLjFtJy/Sxc5J5jhLHnFhy7u4zih97Hz6tJkLU+O+FMMrNZrosUPxDi6sYxJ/EA8jDiAog==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-dynamic-import@7.25.9':
    resolution: {integrity: sha512-GCggjexbmSLaFhqsojeugBpeaRIgWNTcgKVq/0qIteFEqY2A+b9QidYadrWlnbWQUrW5fn+mCvf3tr7OeBFTyg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.26.3':
    resolution: {integrity: sha512-7CAHcQ58z2chuXPWblnn1K6rLDnDWieghSOEmqQsrBenH0P9InCUtOJYD89pvngljmZlJcz3fcmgYsXFNGa1ZQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-export-namespace-from@7.25.9':
    resolution: {integrity: sha512-2NsEz+CxzJIVOPx2o9UsW1rXLqtChtLoVnwYHHiB04wS5sgn7mrV45fWMBX0Kk+ub9uXytVYfNP2HjbVbCB3Ww==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-flow-strip-types@7.26.5':
    resolution: {integrity: sha512-eGK26RsbIkYUns3Y8qKl362juDDYK+wEdPGHGrhzUl6CewZFo55VZ7hg+CyMFU4dd5QQakBN86nBMpRsFpRvbQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.26.9':
    resolution: {integrity: sha512-Hry8AusVm8LW5BVFgiyUReuoGzPUpdHQQqJY5bZnbbf+ngOHWuCuYFKw/BqaaWlvEUrF91HMhDtEaI1hZzNbLg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.25.9':
    resolution: {integrity: sha512-8lP+Yxjv14Vc5MuWBpJsoUCd3hD6V9DgBon2FVYL4jJgbnVQ9fTgYmonchzZJOVNgzEgbxp4OwAf6xz6M/14XA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-json-strings@7.25.9':
    resolution: {integrity: sha512-xoTMk0WXceiiIvsaquQQUaLLXSW1KJ159KP87VilruQm0LNNGxWzahxSS6T6i4Zg3ezp4vA4zuwiNUR53qmQAw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.25.9':
    resolution: {integrity: sha512-9N7+2lFziW8W9pBl2TzaNht3+pgMIRP74zizeCSrtnSKVdUl8mAjjOP2OOVQAfZ881P2cNjDj1uAMEdeD50nuQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-logical-assignment-operators@7.25.9':
    resolution: {integrity: sha512-wI4wRAzGko551Y8eVf6iOY9EouIDTtPb0ByZx+ktDGHwv6bHFimrgJM/2T021txPZ2s4c7bqvHbd+vXG6K948Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.25.9':
    resolution: {integrity: sha512-PYazBVfofCQkkMzh2P6IdIUaCEWni3iYEerAsRWuVd8+jlM1S9S9cz1dF9hIzyoZ8IA3+OwVYIp9v9e+GbgZhA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.25.9':
    resolution: {integrity: sha512-g5T11tnI36jVClQlMlt4qKDLlWnG5pP9CSM4GhdRciTNMRgkfpo5cR6b4rGIOYPgRRuFAvwjPQ/Yk+ql4dyhbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.26.3':
    resolution: {integrity: sha512-MgR55l4q9KddUDITEzEFYn5ZsGDXMSsU9E+kh7fjRXTIC3RHqfCo8RPRbyReYJh44HQ/yomFkqbOFohXvDCiIQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.25.9':
    resolution: {integrity: sha512-hyss7iIlH/zLHaehT+xwiymtPOpsiwIIRlCAOwBB04ta5Tt+lNItADdlXw3jAWZ96VJ2jlhl/c+PNIQPKNfvcA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.25.9':
    resolution: {integrity: sha512-bS9MVObUgE7ww36HEfwe6g9WakQ0KF07mQF74uuXdkoziUPfKyu/nIm663kz//e5O1nPInPFx36z7WJmJ4yNEw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9':
    resolution: {integrity: sha512-oqB6WHdKTGl3q/ItQhpLSnWWOpjUJLsOCLVyeFgeTktkBSCiurvPOsyt93gibI9CmuKvTUEtWmG5VhZD+5T/KA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.25.9':
    resolution: {integrity: sha512-U/3p8X1yCSoKyUj2eOBIx3FOn6pElFOKvAAGf8HTtItuPyB+ZeOqfn+mvTtg9ZlOAjsPdK3ayQEjqHjU/yLeVQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-nullish-coalescing-operator@7.26.6':
    resolution: {integrity: sha512-CKW8Vu+uUZneQCPtXmSBUC6NCAUdya26hWCElAWh5mVSlSRsmiCPUUDKb3Z0szng1hiAJa098Hkhg9o4SE35Qw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-numeric-separator@7.25.9':
    resolution: {integrity: sha512-TlprrJ1GBZ3r6s96Yq8gEQv82s8/5HnCVHtEJScUj90thHQbwe+E5MLhi2bbNHBEJuzrvltXSru+BUxHDoog7Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-rest-spread@7.25.9':
    resolution: {integrity: sha512-fSaXafEE9CVHPweLYw4J0emp1t8zYTXyzN3UuG+lylqkvYd7RMrsOQ8TYx5RF231be0vqtFC6jnx3UmpJmKBYg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.25.9':
    resolution: {integrity: sha512-Kj/Gh+Rw2RNLbCK1VAWj2U48yxxqL2x0k10nPtSdRa0O2xnHXalD0s+o1A6a0W43gJ00ANo38jxkQreckOzv5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-catch-binding@7.25.9':
    resolution: {integrity: sha512-qM/6m6hQZzDcZF3onzIhZeDHDO43bkNNlOX0i8n3lR6zLbu0GN2d8qfM/IERJZYauhAHSLHy39NF0Ctdvcid7g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-chaining@7.25.9':
    resolution: {integrity: sha512-6AvV0FsLULbpnXeBjrY4dmWF8F7gf8QnvTEoO/wX/5xm/xE1Xo8oPuD3MPS+KS9f9XBEAWN7X1aWr4z9HdOr7A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.25.9':
    resolution: {integrity: sha512-wzz6MKwpnshBAiRmn4jR8LYz/g8Ksg0o80XmwZDlordjwEk9SxBzTWC7F5ef1jhbrbOW2DJ5J6ayRukrJmnr0g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-methods@7.25.9':
    resolution: {integrity: sha512-D/JUozNpQLAPUVusvqMxyvjzllRaF8/nSrP1s2YGQT/W4LHK4xxsMcHjhOGTS01mp9Hda8nswb+FblLdJornQw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-property-in-object@7.25.9':
    resolution: {integrity: sha512-Evf3kcMqzXA3xfYJmZ9Pg1OvKdtqsDMSWBDzZOPLvHiTt36E75jLDQo5w1gtRU95Q4E5PDttrTf25Fw8d/uWLw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.25.9':
    resolution: {integrity: sha512-IvIUeV5KrS/VPavfSM/Iu+RE6llrHrYIKY1yfCzyO/lMXHQ+p7uGhonmGVisv6tSBSVgWzMBohTcvkC9vQcQFA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-display-name@7.25.9':
    resolution: {integrity: sha512-KJfMlYIUxQB1CJfO3e0+h0ZHWOTLCPP115Awhaz8U0Zpq36Gl/cXlpoyMRnUWlhNUBAzldnCiAZNvCDj7CrKxQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-development@7.25.9':
    resolution: {integrity: sha512-9mj6rm7XVYs4mdLIpbZnHOYdpW42uoiBCTVowg7sP1thUOiANgMb4UtpRivR0pp5iL+ocvUv7X4mZgFRpJEzGw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-self@7.25.9':
    resolution: {integrity: sha512-y8quW6p0WHkEhmErnfe58r7x0A70uKphQm8Sp8cV7tjNQwK56sNVK0M73LK3WuYmsuyrftut4xAkjjgU0twaMg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx-source@7.25.9':
    resolution: {integrity: sha512-+iqjT8xmXhhYv4/uiYd8FNQsraMFZIfxVSqxxVSZP0WbbSAWvBXAul0m/zu+7Vv4O/3WtApy9pmaTMiumEZgfg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-jsx@7.25.9':
    resolution: {integrity: sha512-s5XwpQYCqGerXl+Pu6VDL3x0j2d82eiV77UJ8a2mDHAW7j9SWRqQ2y1fNo1Z74CdcYipl5Z41zvjj4Nfzq36rw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-react-pure-annotations@7.25.9':
    resolution: {integrity: sha512-KQ/Takk3T8Qzj5TppkS1be588lkbTp5uj7w6a0LeQaTMSckU/wK0oJ/pih+T690tkgI5jfmg2TqDJvd41Sj1Cg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.25.9':
    resolution: {integrity: sha512-vwDcDNsgMPDGP0nMqzahDWE5/MLcX8sv96+wfX7as7LoF/kr97Bo/7fI00lXY4wUXYfVmwIIyG80fGZ1uvt2qg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regexp-modifiers@7.26.0':
    resolution: {integrity: sha512-vN6saax7lrA2yA/Pak3sCxuD6F5InBjn9IcrIKQPjpsLvuHYLVroTxjdlVRHjjBWxKOqIwpTXDkOssYT4BFdRw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-reserved-words@7.25.9':
    resolution: {integrity: sha512-7DL7DKYjn5Su++4RXu8puKZm2XBPHyjWLUidaPEkCUBbE7IPcsrkRHggAOOKydH1dASWdcUBxrkOGNxUv5P3Jg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-runtime@7.26.9':
    resolution: {integrity: sha512-Jf+8y9wXQbbxvVYTM8gO5oEF2POdNji0NMltEkG7FtmzD9PVz7/lxpqSdTvwsjTMU5HIHuDVNf2SOxLkWi+wPQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.25.9':
    resolution: {integrity: sha512-MUv6t0FhO5qHnS/W8XCbHmiRWOphNufpE1IVxhK5kuN3Td9FT1x4rx4K42s3RYdMXCXpfWkGSbCSd0Z64xA7Ng==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.25.9':
    resolution: {integrity: sha512-oNknIB0TbURU5pqJFVbOOFspVlrpVwo2H1+HUIsVDvp5VauGGDP1ZEvO8Nn5xyMEs3dakajOxlmkNW7kNgSm6A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.25.9':
    resolution: {integrity: sha512-WqBUSgeVwucYDP9U/xNRQam7xV8W5Zf+6Eo7T2SRVUFlhRiMNFdFz58u0KZmCVVqs2i7SHgpRnAhzRNmKfi2uA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.26.8':
    resolution: {integrity: sha512-OmGDL5/J0CJPJZTHZbi2XpO0tyT2Ia7fzpW5GURwdtp2X3fMmN8au/ej6peC/T33/+CRiIpA8Krse8hFGVmT5Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.26.7':
    resolution: {integrity: sha512-jfoTXXZTgGg36BmhqT3cAYK5qkmqvJpvNrPhaK/52Vgjhw4Rq29s9UqpWWV0D6yuRmgiFH/BUVlkl96zJWqnaw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typescript@7.26.8':
    resolution: {integrity: sha512-bME5J9AC8ChwA7aEPJ6zym3w7aObZULHhbNLU0bKUhKsAkylkzUdq+0kdymh9rzi8nlNFl2bmldFBCKNJBUpuw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.25.9':
    resolution: {integrity: sha512-s5EDrE6bW97LtxOcGj1Khcx5AaXwiMmi4toFWRDP9/y0Woo6pXC+iyPu/KuhKtfSrNFd7jJB+/fkOtZy6aIC6Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-property-regex@7.25.9':
    resolution: {integrity: sha512-Jt2d8Ga+QwRluxRQ307Vlxa6dMrYEMZCgGxoPR8V52rxPyldHu3hdlHspxaqYmE7oID5+kB+UKUB/eWS+DkkWg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.25.9':
    resolution: {integrity: sha512-yoxstj7Rg9dlNn9UQxzk4fcNivwv4nUYz7fYXBaKxvw/lnmPuOm/ikoELygbYq68Bls3D/D+NBPHiLwZdZZ4HA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-sets-regex@7.25.9':
    resolution: {integrity: sha512-8BYqO3GeVNHtx69fdPshN3fnzUNLrWdHhk/icSwigksJGczKSizZ+Z6SBCxTs723Fr5VSNorTIK7a+R2tISvwQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/preset-env@7.26.9':
    resolution: {integrity: sha512-vX3qPGE8sEKEAZCWk05k3cpTAE3/nOYca++JA+Rd0z2NCNzabmYvEiSShKzm10zdquOIAVXsy2Ei/DTW34KlKQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.6-no-external-plugins':
    resolution: {integrity: sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0

  '@babel/preset-react@7.26.3':
    resolution: {integrity: sha512-Nl03d6T9ky516DGK2YMxrTqvnpUW63TnJMOMonj+Zae0JiPC5BC9xPMSL6L8fiSpA5vP88qfygavVQvnLp+6Cw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-typescript@7.26.0':
    resolution: {integrity: sha512-NMk1IGZ5I/oHhoXEElcm+xUnL/szL6xflkFZmoEU9xj1qSJXpiS7rsspYo92B4DRCDvZn2erT5LdsCeXAKNCkg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/runtime@7.26.9':
    resolution: {integrity: sha512-aA63XwOkcl4xxQa3HjPMqOP6LiK0ZDv3mUPYEFXkpHbaFjtGggE1A61FjFzJnB+p7/oy2gA8E+rcBNl/zC1tMg==}
    engines: {node: '>=6.9.0'}

  '@babel/runtime@8.0.0-alpha.17':
    resolution: {integrity: sha512-jeV3fYCLTbEwor7EBzOxhZbW+bxHJpm0V0xhaHGfWQwjsHENO2RBHVxFRTG2zfczCgOpz6TqP7EXVSUaooex6g==}
    engines: {node: ^18.20.0 || ^20.17.0 || >=22.8.0}

  '@babel/template@7.26.9':
    resolution: {integrity: sha512-qyRplbeIpNZhmzOysF/wFMuP9sctmh2cFzRAZOn1YapxBsE1i9bJIY586R/WBLfLcmcBlM8ROBiQURnnNy+zfA==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.26.9':
    resolution: {integrity: sha512-ZYW7L+pL8ahU5fXmNbPF+iZFHCv5scFak7MZ9bwaRPLUhHh7QQEMjZUg0HevihoqCM5iSYHN61EyCoZvqC+bxg==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.26.9':
    resolution: {integrity: sha512-Y3IR1cRnOxOCDvMmNiym7XpXQ93iGDDPHx+Zj+NM+rg0fBaShfQLkg+hKPaZCEvg5N/LeCo4+Rj/i3FuJsIQaw==}
    engines: {node: '>=6.9.0'}

  '@bcoe/v8-coverage@0.2.3':
    resolution: {integrity: sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==}

  '@cspotcode/source-map-support@0.8.1':
    resolution: {integrity: sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==}
    engines: {node: '>=12'}

  '@egjs/hammerjs@2.0.17':
    resolution: {integrity: sha512-XQsZgjm2EcVUiZQf11UBJQfmZeEmOW8DpI1gsFeln6w0ae0ii4dMQEQ0kjl6DspdWX1aGY1/loyXnP0JS06e/A==}
    engines: {node: '>=0.8.0'}

  '@eslint-community/eslint-utils@4.4.1':
    resolution: {integrity: sha512-s3O3waFUrMV8P/XaF/+ZTp1X9XBZW1a4B97ZnjQF2KYWaFD2A8KyFBsrsfSjEmjn3RGWAIuvlneuZm3CUK3jbA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  '@eslint-community/regexpp@4.12.1':
    resolution: {integrity: sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ==}
    engines: {node: ^12.0.0 || ^14.0.0 || >=16.0.0}

  '@eslint/eslintrc@1.4.1':
    resolution: {integrity: sha512-XXrH9Uarn0stsyldqDYq8r++mROmWRI1xKMXa640Bb//SY1+ECYX6VzT6Lcx5frD0V30XieqJ0oX9I2Xj5aoMA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@expo-google-fonts/space-grotesk@0.2.3':
    resolution: {integrity: sha512-UYEMIrzegR02pauH7gVMI7j6cUroTtJug6dH/aQFjMNz0UwZe6GUcrEtJDmsUHJjEZdxbYgHhaiIwswWVo0CMA==}

  '@expo/cli@0.24.13':
    resolution: {integrity: sha512-2LSdbvYs+WmUljnplQXMCUyNzyX4H+F4l8uExfA1hud25Bl5kyaGrx1jjtgNxMTXmfmMjvgBdK798R50imEhkA==}
    hasBin: true

  '@expo/code-signing-certificates@0.0.5':
    resolution: {integrity: sha512-BNhXkY1bblxKZpltzAx98G2Egj9g1Q+JRcvR7E99DOj862FTCX+ZPsAUtPTr7aHxwtrL7+fL3r0JSmM9kBm+Bw==}

  '@expo/config-plugins@10.0.2':
    resolution: {integrity: sha512-TzUn3pPdpwCS0yYaSlZOClgDmCX8N4I2lfgitX5oStqmvpPtB+vqtdyqsVM02fQ2tlJIAqwBW+NHaHqqy8Jv7g==}

  '@expo/config-types@53.0.4':
    resolution: {integrity: sha512-0s+9vFx83WIToEr0Iwy4CcmiUXa5BgwBmEjylBB2eojX5XAMm9mJvw9KpjAb8m7zq2G0Q6bRbeufkzgbipuNQg==}

  '@expo/config@11.0.10':
    resolution: {integrity: sha512-8S8Krr/c5lnl0eF03tA2UGY9rGBhZcbWKz2UWw5dpL/+zstwUmog8oyuuC8aRcn7GiTQLlbBkxcMeT8sOGlhbA==}

  '@expo/devcert@1.1.4':
    resolution: {integrity: sha512-fqBODr8c72+gBSX5Ty3SIzaY4bXainlpab78+vEYEKL3fXmsOswMLf0+KE36mUEAa36BYabX7K3EiXOXX5OPMw==}

  '@expo/env@1.0.5':
    resolution: {integrity: sha512-dtEZ4CAMaVrFu2+tezhU3FoGWtbzQl50xV+rNJE5lYVRjUflWiZkVHlHkWUlPAwDPifLy4TuissVfScGGPWR5g==}

  '@expo/fingerprint@0.12.4':
    resolution: {integrity: sha512-HOJVvjiQYVHIouCOfFf4JRrQvBDIV/12GVG2iwbw1iGwmpQVkPgEXa9lN0f2yuS4J3QXHs73wr9jvuCjMmJlfw==}
    hasBin: true

  '@expo/image-utils@0.7.4':
    resolution: {integrity: sha512-LcZ82EJy/t/a1avwIboeZbO6hlw8CvsIRh2k6SWPcAOvW0RqynyKFzUJsvnjWlhUzfBEn4oI7y/Pu5Xkw3KkkA==}

  '@expo/json-file@9.1.4':
    resolution: {integrity: sha512-7Bv86X27fPERGhw8aJEZvRcH9sk+9BenDnEmrI3ZpywKodYSBgc8lX9Y32faNVQ/p0YbDK9zdJ0BfAKNAOyi0A==}

  '@expo/metro-config@0.20.14':
    resolution: {integrity: sha512-tYDDubuZycK+NX00XN7BMu73kBur/evOPcKfxc+UBeFfgN2EifOITtdwSUDdRsbtJ2OnXwMY1HfRUG3Lq3l4cw==}

  '@expo/metro-runtime@5.0.4':
    resolution: {integrity: sha512-r694MeO+7Vi8IwOsDIDzH/Q5RPMt1kUDYbiTJwnO15nIqiDwlE8HU55UlRhffKZy6s5FmxQsZ8HA+T8DqUW8cQ==}
    peerDependencies:
      react-native: '*'

  '@expo/osascript@2.2.4':
    resolution: {integrity: sha512-Q+Oyj+1pdRiHHpev9YjqfMZzByFH8UhKvSszxa0acTveijjDhQgWrq4e9T/cchBHi0GWZpGczWyiyJkk1wM1dg==}
    engines: {node: '>=12'}

  '@expo/package-manager@1.8.4':
    resolution: {integrity: sha512-8H8tLga/NS3iS7QaX/NneRPqbObnHvVCfMCo0ShudreOFmvmgqhYjRlkZTRstSyFqefai8ONaT4VmnLHneRYYg==}

  '@expo/plist@0.3.4':
    resolution: {integrity: sha512-MhBLaUJNe9FQDDU2xhSNS4SAolr6K2wuyi4+A79vYuXLkAoICsbTwcGEQJN5jPY6D9izO/jsXh5k0h+mIWQMdw==}

  '@expo/prebuild-config@9.0.6':
    resolution: {integrity: sha512-HDTdlMkTQZ95rd6EpvuLM+xkZV03yGLc38FqI37qKFLJtUN1WnYVaWsuXKoljd1OrVEVsHe6CfqKwaPZ52D56Q==}

  '@expo/sdk-runtime-versions@1.0.0':
    resolution: {integrity: sha512-Doz2bfiPndXYFPMRwPyGa1k5QaKDVpY806UJj570epIiMzWaYyCtobasyfC++qfIXVb5Ocy7r3tP9d62hAQ7IQ==}

  '@expo/spawn-async@1.7.2':
    resolution: {integrity: sha512-QdWi16+CHB9JYP7gma19OVVg0BFkvU8zNj9GjWorYI8Iv8FUxjOCcYRuAmX4s/h91e4e7BPsskc8cSrZYho9Ew==}
    engines: {node: '>=12'}

  '@expo/vector-icons@14.0.4':
    resolution: {integrity: sha512-+yKshcbpDfbV4zoXOgHxCwh7lkE9VVTT5T03OUlBsqfze1PLy6Hi4jp1vSb1GVbY6eskvMIivGVc9SKzIv0oEQ==}

  '@expo/ws-tunnel@1.0.6':
    resolution: {integrity: sha512-nDRbLmSrJar7abvUjp3smDwH8HcbZcoOEa5jVPUv9/9CajgmWw20JNRwTuBRzWIWIkEJDkz20GoNA+tSwUqk0Q==}

  '@expo/xcpretty@4.3.2':
    resolution: {integrity: sha512-ReZxZ8pdnoI3tP/dNnJdnmAk7uLT4FjsKDGW7YeDdvdOMz2XCQSmSCM9IWlrXuWtMF9zeSB6WJtEhCQ41gQOfw==}
    hasBin: true

  '@fortawesome/fontawesome-common-types@6.7.2':
    resolution: {integrity: sha512-Zs+YeHUC5fkt7Mg1l6XTniei3k4bwG/yo3iFUtZWd/pMx9g3fdvkSK9E0FOC+++phXOka78uJcYb8JaFkW52Xg==}
    engines: {node: '>=6'}

  '@fortawesome/fontawesome-svg-core@6.7.2':
    resolution: {integrity: sha512-yxtOBWDrdi5DD5o1pmVdq3WMCvnobT0LU6R8RyyVXPvFRd2o79/0NCuQoCjNTeZz9EzA9xS3JxNWfv54RIHFEA==}
    engines: {node: '>=6'}

  '@fortawesome/free-brands-svg-icons@6.7.2':
    resolution: {integrity: sha512-zu0evbcRTgjKfrr77/2XX+bU+kuGfjm0LbajJHVIgBWNIDzrhpRxiCPNT8DW5AdmSsq7Mcf9D1bH0aSeSUSM+Q==}
    engines: {node: '>=6'}

  '@fortawesome/free-regular-svg-icons@6.7.2':
    resolution: {integrity: sha512-7Z/ur0gvCMW8G93dXIQOkQqHo2M5HLhYrRVC0//fakJXxcF1VmMPsxnG6Ee8qEylA8b8Q3peQXWMNZ62lYF28g==}
    engines: {node: '>=6'}

  '@fortawesome/free-solid-svg-icons@6.7.2':
    resolution: {integrity: sha512-GsBrnOzU8uj0LECDfD5zomZJIjrPhIlWU82AHwa2s40FKH+kcxQaBvBo3Z4TxyZHIyX8XTDxsyA33/Vx9eFuQA==}
    engines: {node: '>=6'}

  '@fortawesome/react-native-fontawesome@0.3.2':
    resolution: {integrity: sha512-CiWfJWSZHRg12VXlaeFnaa5yJVPOrjsSFEvF6ntz3cnjg4oN3cvauL+JATacMCl0v9xzib32qC1WZAvvGkfB4w==}
    peerDependencies:
      '@fortawesome/fontawesome-svg-core': ~1 || ~6
      react-native: '>= 0.67'
      react-native-svg: '>= 11.x'

  '@humanwhocodes/config-array@0.9.5':
    resolution: {integrity: sha512-ObyMyWxZiCu/yTisA7uzx81s40xR2fD5Cg/2Kq7G02ajkNubJf6BopgDTmDyc3U7sXpNKM8cYOw7s7Tyr+DnCw==}
    engines: {node: '>=10.10.0'}
    deprecated: Use @eslint/config-array instead

  '@humanwhocodes/object-schema@1.2.1':
    resolution: {integrity: sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==}
    deprecated: Use @eslint/object-schema instead

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@isaacs/fs-minipass@4.0.1':
    resolution: {integrity: sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w==}
    engines: {node: '>=18.0.0'}

  '@isaacs/ttlcache@1.4.1':
    resolution: {integrity: sha512-RQgQ4uQ+pLbqXfOmieB91ejmLwvSgv9nLx6sT6sD83s7umBypgg+OIBOBbEUiJXrfpnp9j0mRhYYdzp9uqq3lA==}
    engines: {node: '>=12'}

  '@istanbuljs/load-nyc-config@1.1.0':
    resolution: {integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==}
    engines: {node: '>=8'}

  '@istanbuljs/schema@0.1.3':
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==}
    engines: {node: '>=8'}

  '@jest/console@29.7.0':
    resolution: {integrity: sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/core@29.7.0':
    resolution: {integrity: sha512-n7aeXWKMnGtDA48y8TLWJPJmLmmZ642Ceo78cYWEpiD7FzDgmNDV/GCVRorPABdXLJZ/9wzzgZAlHjXjxDHGsg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/create-cache-key-function@29.7.0':
    resolution: {integrity: sha512-4QqS3LY5PBmTRHj9sAg1HLoPzqAI0uOX6wI/TRqHIcOxlFidy6YEmCQJk6FSZjNLGCeubDMfmkWL+qaLKhSGQA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/environment@29.7.0':
    resolution: {integrity: sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/expect-utils@29.7.0':
    resolution: {integrity: sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/expect@29.7.0':
    resolution: {integrity: sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/fake-timers@29.7.0':
    resolution: {integrity: sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/globals@29.7.0':
    resolution: {integrity: sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/reporters@29.7.0':
    resolution: {integrity: sha512-DApq0KJbJOEzAFYjHADNNxAE3KbhxQB1y5Kplb5Waqw6zVbuWatSnMjE5gs8FUgEPmNsnZA3NCWl9NG0ia04Pg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  '@jest/schemas@29.6.3':
    resolution: {integrity: sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/source-map@29.6.3':
    resolution: {integrity: sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/test-result@29.7.0':
    resolution: {integrity: sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/test-sequencer@29.7.0':
    resolution: {integrity: sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/transform@29.7.0':
    resolution: {integrity: sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jest/types@29.6.3':
    resolution: {integrity: sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  '@jridgewell/gen-mapping@0.3.8':
    resolution: {integrity: sha512-imAbBGkb+ebQyxKgzv5Hu2nmROxoDOXHh80evxdoXNOrvAnVx7zimzc1Oo5h9RlfV4vPXaE2iM5pOFbvOCClWA==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.2':
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.2.1':
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.6':
    resolution: {integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==}

  '@jridgewell/sourcemap-codec@1.5.0':
    resolution: {integrity: sha512-gv3ZRaISU3fjPAgNsriBRqGWQL6quFx04YMPW/zD8XMLsU32mhCCbfbO6KZFLjvYpCZ8zyDEgqsgf+PwPaM7GQ==}

  '@jridgewell/trace-mapping@0.3.25':
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==}

  '@jridgewell/trace-mapping@0.3.9':
    resolution: {integrity: sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==}

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@react-native-async-storage/async-storage@2.1.2':
    resolution: {integrity: sha512-dvlNq4AlGWC+ehtH12p65+17V0Dx7IecOWl6WanF2ja38O1Dcjjvn7jVzkUHJ5oWkQBlyASurTPlTHgKXyYiow==}
    peerDependencies:
      react-native: ^0.0.0-0 || >=0.65 <1.0

  '@react-native-community/datetimepicker@8.3.0':
    resolution: {integrity: sha512-K/KgaJbLtjMpx4PaG4efrVIcSe6+DbLufeX1lwPB5YY8i3sq9dOh6WcAcMTLbaRTUpurebQTkl7puHPFm9GalA==}
    peerDependencies:
      expo: '>=50.0.0'
      react: '*'
      react-native: '*'
      react-native-windows: '*'
    peerDependenciesMeta:
      expo:
        optional: true
      react-native-windows:
        optional: true

  '@react-native-community/netinfo@11.4.1':
    resolution: {integrity: sha512-B0BYAkghz3Q2V09BF88RA601XursIEA111tnc2JOaN7axJWmNefmfjZqw/KdSxKZp7CZUuPpjBmz/WCR9uaHYg==}
    peerDependencies:
      react-native: '>=0.59'

  '@react-native-google-signin/google-signin@13.2.0':
    resolution: {integrity: sha512-1D7eWPE7itfPmNwLkBylaoiIXAvHQNalt+AfKWcgLp45ZvPh6D5wJEhbs03PJtwO7j+Nc7Z9+DjZ2auIlWzZjw==}
    peerDependencies:
      expo: '>=50.0.0'
      react: '*'
      react-dom: '*'
      react-native: '*'
    peerDependenciesMeta:
      expo:
        optional: true
      react-dom:
        optional: true

  '@react-native-picker/picker@2.11.0':
    resolution: {integrity: sha512-QuZU6gbxmOID5zZgd/H90NgBnbJ3VV6qVzp6c7/dDrmWdX8S0X5YFYgDcQFjE3dRen9wB9FWnj2VVdPU64adSg==}
    peerDependencies:
      react: '*'
      react-native: '*'

  '@react-native/assets-registry@0.79.2':
    resolution: {integrity: sha512-5h2Z7/+/HL/0h88s0JHOdRCW4CXMCJoROxqzHqxdrjGL6EBD1DdaB4ZqkCOEVSW4Vjhir5Qb97C8i/MPWEYPtg==}
    engines: {node: '>=18'}

  '@react-native/babel-plugin-codegen@0.79.2':
    resolution: {integrity: sha512-d+NB7Uosn2ZWd4O4+7ZkB6q1a+0z2opD/4+Bzhk/Tv6fc5FrSftK2Noqxvo3/bhbdGFVPxf0yvLE8et4W17x/Q==}
    engines: {node: '>=18'}

  '@react-native/babel-preset@0.79.2':
    resolution: {integrity: sha512-/HNu869oUq4FUXizpiNWrIhucsYZqu0/0spudJEzk9SEKar0EjVDP7zkg/sKK+KccNypDQGW7nFXT8onzvQ3og==}
    engines: {node: '>=18'}
    peerDependencies:
      '@babel/core': '*'

  '@react-native/codegen@0.79.2':
    resolution: {integrity: sha512-8JTlGLuLi1p8Jx2N/enwwEd7/2CfrqJpv90Cp77QLRX3VHF2hdyavRIxAmXMwN95k+Me7CUuPtqn2X3IBXOWYg==}
    engines: {node: '>=18'}
    peerDependencies:
      '@babel/core': '*'

  '@react-native/community-cli-plugin@0.79.2':
    resolution: {integrity: sha512-E+YEY2dL+68HyR2iahsZdyBKBUi9QyPyaN9vsnda1jNgCjNpSPk2yAF5cXsho+zKK5ZQna3JSeE1Kbi2IfGJbw==}
    engines: {node: '>=18'}
    peerDependencies:
      '@react-native-community/cli': '*'
    peerDependenciesMeta:
      '@react-native-community/cli':
        optional: true

  '@react-native/debugger-frontend@0.79.2':
    resolution: {integrity: sha512-cGmC7X6kju76DopSBNc+PRAEetbd7TWF9J9o84hOp/xL3ahxR2kuxJy0oJX8Eg8oehhGGEXTuMKHzNa3rDBeSg==}
    engines: {node: '>=18'}

  '@react-native/dev-middleware@0.79.2':
    resolution: {integrity: sha512-9q4CpkklsAs1L0Bw8XYCoqqyBSrfRALGEw4/r0EkR38Y/6fVfNfdsjSns0pTLO6h0VpxswK34L/hm4uK3MoLHw==}
    engines: {node: '>=18'}

  '@react-native/gradle-plugin@0.79.2':
    resolution: {integrity: sha512-6MJFemrwR0bOT0QM+2BxX9k3/pvZQNmJ3Js5pF/6owsA0cUDiCO57otiEU8Fz+UywWEzn1FoQfOfQ8vt2GYmoA==}
    engines: {node: '>=18'}

  '@react-native/js-polyfills@0.79.2':
    resolution: {integrity: sha512-IaY87Ckd4GTPMkO1/Fe8fC1IgIx3vc3q9Tyt/6qS3Mtk9nC0x9q4kSR5t+HHq0/MuvGtu8HpdxXGy5wLaM+zUw==}
    engines: {node: '>=18'}

  '@react-native/normalize-colors@0.74.89':
    resolution: {integrity: sha512-qoMMXddVKVhZ8PA1AbUCk83trpd6N+1nF2A6k1i6LsQObyS92fELuk8kU/lQs6M7BsMHwqyLCpQJ1uFgNvIQXg==}

  '@react-native/normalize-colors@0.79.2':
    resolution: {integrity: sha512-+b+GNrupWrWw1okHnEENz63j7NSMqhKeFMOyzYLBwKcprG8fqJQhDIGXfizKdxeIa5NnGSAevKL1Ev1zJ56X8w==}

  '@react-native/virtualized-lists@0.79.2':
    resolution: {integrity: sha512-9G6ROJeP+rdw9Bvr5ruOlag11ET7j1z/En1riFFNo6W3xZvJY+alCuH1ttm12y9+zBm4n8jwCk4lGhjYaV4dKw==}
    engines: {node: '>=18'}
    peerDependencies:
      '@types/react': ^19.0.0
      react: '*'
      react-native: '*'
    peerDependenciesMeta:
      '@types/react':
        optional: true

  '@react-navigation/bottom-tabs@7.3.13':
    resolution: {integrity: sha512-J3MWXBJc3y6hefZNRqdj/JD4nzIDLzZL5GIYj89pR6oRf2Iibz9t1qV7yzxEc1KOaNDkXVZ/5U16PArEJFfykQ==}
    peerDependencies:
      '@react-navigation/native': ^7.1.9
      react: '>= 18.2.0'
      react-native: '*'
      react-native-safe-area-context: '>= 4.0.0'
      react-native-screens: '>= 4.0.0'

  '@react-navigation/core@7.9.2':
    resolution: {integrity: sha512-lqCyKMWWaSwGK4VV3wRXXEKvl5IKrVH207Kp77TLCnITnd4KQIdgjzzJ/Pr62ugki3VTAErq1vg0yRlcXciCbg==}
    peerDependencies:
      react: '>= 18.2.0'

  '@react-navigation/elements@2.4.2':
    resolution: {integrity: sha512-cudKLsRtOB+i8iDzfBKypdqiHsDy1ruqCfYAtwKEclDmLsxu3/90YXoBtoPyFNyIpsn3GtsJzZsrYWQh78xSWg==}
    peerDependencies:
      '@react-native-masked-view/masked-view': '>= 0.2.0'
      '@react-navigation/native': ^7.1.9
      react: '>= 18.2.0'
      react-native: '*'
      react-native-safe-area-context: '>= 4.0.0'
    peerDependenciesMeta:
      '@react-native-masked-view/masked-view':
        optional: true

  '@react-navigation/native-stack@7.3.13':
    resolution: {integrity: sha512-udH+HumX0PmaT6QQTqjU3ciiCwifBGtnw1+6B1bVEDw83q80WHotlMitaf8Enbuf7oWrxwB+Eow4tV5MJXgQtQ==}
    peerDependencies:
      '@react-navigation/native': ^7.1.9
      react: '>= 18.2.0'
      react-native: '*'
      react-native-safe-area-context: '>= 4.0.0'
      react-native-screens: '>= 4.0.0'

  '@react-navigation/native@7.1.9':
    resolution: {integrity: sha512-/A0oBwZIeD23o4jsnB0fEyKmKS+l4LAbJP/ioVvsGEubGp+sc5ouQNranOh7JwR0R1eX0MjcsLKprEwB+nztdw==}
    peerDependencies:
      react: '>= 18.2.0'
      react-native: '*'

  '@react-navigation/routers@7.3.7':
    resolution: {integrity: sha512-5ffgrefOs2zWqcCVX+OKn+RDx0puopQtxqetegFrTfWQ6pGXdY/5v4kBpPwaOFrNEeE/LPbHt9IJaJuvyhB7RA==}

  '@sentry-internal/browser-utils@8.54.0':
    resolution: {integrity: sha512-DKWCqb4YQosKn6aD45fhKyzhkdG7N6goGFDeyTaJFREJDFVDXiNDsYZu30nJ6BxMM7uQIaARhPAC5BXfoED3pQ==}
    engines: {node: '>=14.18'}

  '@sentry-internal/feedback@8.54.0':
    resolution: {integrity: sha512-nQqRacOXoElpE0L0ADxUUII0I3A94niqG9Z4Fmsw6057QvyrV/LvTiMQBop6r5qLjwMqK+T33iR4/NQI5RhsXQ==}
    engines: {node: '>=14.18'}

  '@sentry-internal/replay-canvas@8.54.0':
    resolution: {integrity: sha512-K/On3OAUBeq/TV2n+1EvObKC+WMV9npVXpVyJqCCyn8HYMm8FUGzuxeajzm0mlW4wDTPCQor6mK9/IgOquUzCw==}
    engines: {node: '>=14.18'}

  '@sentry-internal/replay@8.54.0':
    resolution: {integrity: sha512-8xuBe06IaYIGJec53wUC12tY2q4z2Z0RPS2s1sLtbA00EvK1YDGuXp96IDD+HB9mnDMrQ/jW5f97g9TvPsPQUg==}
    engines: {node: '>=14.18'}

  '@sentry/babel-plugin-component-annotate@3.2.2':
    resolution: {integrity: sha512-D+SKQ266ra/wo87s9+UI/rKQi3qhGPCR8eSCDe0VJudhjHsqyNU+JJ5lnIGCgmZaWFTXgdBP/gdr1Iz1zqGs4Q==}
    engines: {node: '>= 14'}

  '@sentry/browser@8.54.0':
    resolution: {integrity: sha512-BgUtvxFHin0fS0CmJVKTLXXZcke0Av729IVfi+2fJ4COX8HO7/HAP02RKaSQGmL2HmvWYTfNZ7529AnUtrM4Rg==}
    engines: {node: '>=14.18'}

  '@sentry/cli-darwin@2.42.4':
    resolution: {integrity: sha512-PZV4Y97VDWBR4rIt0HkJfXaBXlebIN2s/FDzC3iHINZE5OG62CDFsnC4/lbGlf2/UZLDaGGIK7mYwSHhTvN+HQ==}
    engines: {node: '>=10'}
    os: [darwin]

  '@sentry/cli-linux-arm64@2.42.4':
    resolution: {integrity: sha512-Ex8vRnryyzC/9e43daEmEqPS+9uirY/l6Hw2lAvhBblFaL7PTWNx52H+8GnYGd9Zy2H3rWNyBDYfHwnErg38zA==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux, freebsd]

  '@sentry/cli-linux-arm@2.42.4':
    resolution: {integrity: sha512-lBn0oeeg62h68/4Eo6zbPq99Idz5t0VRV48rEU/WKeM4MtQCvG/iGGQ3lBFW2yNiUBzXZIK9poXLEcgbwmcRVw==}
    engines: {node: '>=10'}
    cpu: [arm]
    os: [linux, freebsd]

  '@sentry/cli-linux-i686@2.42.4':
    resolution: {integrity: sha512-IBJg0aHjsLCL4LvcFa3cXIjA+4t5kPqBT9y+PoDu4goIFxYD8zl7mbUdGJutvJafTk8Akf4ss4JJXQBjg019zA==}
    engines: {node: '>=10'}
    cpu: [x86, ia32]
    os: [linux, freebsd]

  '@sentry/cli-linux-x64@2.42.4':
    resolution: {integrity: sha512-gXI5OEiOSNiAEz7VCE6AZcAgHJ47mlgal3+NmbE8XcHmFOnyDws9FNie6PJAy8KZjXi3nqoBP9JVAbnmOix3uA==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux, freebsd]

  '@sentry/cli-win32-i686@2.42.4':
    resolution: {integrity: sha512-vZuR3UPHKqOMniyrijrrsNwn9usaRysXq78F6WV0cL0ZyPLAmY+KBnTDSFk1Oig2pURnzaTm+RtcZu2fc8mlzg==}
    engines: {node: '>=10'}
    cpu: [x86, ia32]
    os: [win32]

  '@sentry/cli-win32-x64@2.42.4':
    resolution: {integrity: sha512-OIBj3uaQ6nAERSm5Dcf8UIhyElEEwMNsZEEppQpN4IKl0mrwb/57AznM23Dvpu6GR8WGbVQUSolt879YZR5E9g==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [win32]

  '@sentry/cli@2.42.4':
    resolution: {integrity: sha512-BoSZDAWJiz/40tu6LuMDkSgwk4xTsq6zwqYoUqLU3vKBR/VsaaQGvu6EWxZXORthfZU2/5Agz0+t220cge6VQw==}
    engines: {node: '>= 10'}
    hasBin: true

  '@sentry/core@8.54.0':
    resolution: {integrity: sha512-03bWf+D1j28unOocY/5FDB6bUHtYlm6m6ollVejhg45ZmK9iPjdtxNWbrLsjT1WRym0Tjzowu+A3p+eebYEv0Q==}
    engines: {node: '>=14.18'}

  '@sentry/react-native@6.10.0':
    resolution: {integrity: sha512-B56vc+pnFHMiu3cabFb454v4qD0zObW6JVzJ5Gb6fIMdt93AFIJg10ZErzC+ump7xM4BOEROFFRuLiyvadvlPA==}
    hasBin: true
    peerDependencies:
      expo: '>=49.0.0'
      react: '>=17.0.0'
      react-native: '>=0.65.0'
    peerDependenciesMeta:
      expo:
        optional: true

  '@sentry/react@8.54.0':
    resolution: {integrity: sha512-42T/fp8snYN19Fy/2P0Mwotu4gcdy+1Lx+uYCNcYP1o7wNGigJ7qb27sW7W34GyCCHjoCCfQgeOqDQsyY8LC9w==}
    engines: {node: '>=14.18'}
    peerDependencies:
      react: ^16.14.0 || 17.x || 18.x || 19.x

  '@sentry/types@8.54.0':
    resolution: {integrity: sha512-wztdtr7dOXQKi0iRvKc8XJhJ7HaAfOv8lGu0yqFOFwBZucO/SHnu87GOPi8mvrTiy1bentQO5l+zXWAaMvG4uw==}
    engines: {node: '>=14.18'}

  '@sentry/utils@8.54.0':
    resolution: {integrity: sha512-JL8UDjrsKxKclTdLXfuHfE7B3KbrAPEYP7tMyN/xiO2vsF6D84fjwYyalO0ZMtuFZE6vpSze8ZOLEh6hLnPYsw==}
    engines: {node: '>=14.18'}

  '@shopify/flash-list@1.7.6':
    resolution: {integrity: sha512-0kuuAbWgy4YSlN05mt0ScvxK8uiDixMsICWvDed+LTxvZ5+5iRyt3M8cRLUroB8sfiZlJJZWlxHrx0frBpsYOQ==}
    peerDependencies:
      '@babel/runtime': '*'
      react: '*'
      react-native: '*'

  '@shopify/react-native-skia@2.0.0-next.4':
    resolution: {integrity: sha512-NzvdgryRz6tkKMHgCChCKa3wXfN9TZhlV0/LrfIU/wKLC1uKgGXkoZgNz7Is0wwdhtao1JJJJ81fqHCGHgzk9g==}
    hasBin: true
    peerDependencies:
      react: '>=19.0'
      react-native: '>=0.78'
      react-native-reanimated: ^3.0
    peerDependenciesMeta:
      react-native:
        optional: true
      react-native-reanimated:
        optional: true

  '@sinclair/typebox@0.27.8':
    resolution: {integrity: sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==}

  '@sinonjs/commons@3.0.1':
    resolution: {integrity: sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==}

  '@sinonjs/fake-timers@10.3.0':
    resolution: {integrity: sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==}

  '@smithy/abort-controller@3.1.9':
    resolution: {integrity: sha512-yiW0WI30zj8ZKoSYNx90no7ugVn3khlyH/z5W8qtKBtVE6awRALbhSG+2SAHA1r6bO/6M9utxYKVZ3PCJ1rWxw==}
    engines: {node: '>=16.0.0'}

  '@smithy/config-resolver@3.0.13':
    resolution: {integrity: sha512-Gr/qwzyPaTL1tZcq8WQyHhTZREER5R1Wytmz4WnVGL4onA3dNk6Btll55c8Vr58pLdvWZmtG8oZxJTw3t3q7Jg==}
    engines: {node: '>=16.0.0'}

  '@smithy/core@2.5.7':
    resolution: {integrity: sha512-8olpW6mKCa0v+ibCjoCzgZHQx1SQmZuW/WkrdZo73wiTprTH6qhmskT60QLFdT9DRa5mXxjz89kQPZ7ZSsoqqg==}
    engines: {node: '>=16.0.0'}

  '@smithy/credential-provider-imds@3.2.8':
    resolution: {integrity: sha512-ZCY2yD0BY+K9iMXkkbnjo+08T2h8/34oHd0Jmh6BZUSZwaaGlGCyBT/3wnS7u7Xl33/EEfN4B6nQr3Gx5bYxgw==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-codec@3.1.10':
    resolution: {integrity: sha512-323B8YckSbUH0nMIpXn7HZsAVKHYHFUODa8gG9cHo0ySvA1fr5iWaNT+iIL0UCqUzG6QPHA3BSsBtRQou4mMqQ==}

  '@smithy/eventstream-serde-browser@3.0.14':
    resolution: {integrity: sha512-kbrt0vjOIihW3V7Cqj1SXQvAI5BR8SnyQYsandva0AOR307cXAc+IhPngxIPslxTLfxwDpNu0HzCAq6g42kCPg==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-config-resolver@3.0.11':
    resolution: {integrity: sha512-P2pnEp4n75O+QHjyO7cbw/vsw5l93K/8EWyjNCAAybYwUmj3M+hjSQZ9P5TVdUgEG08ueMAP5R4FkuSkElZ5tQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-node@3.0.13':
    resolution: {integrity: sha512-zqy/9iwbj8Wysmvi7Lq7XFLeDgjRpTbCfwBhJa8WbrylTAHiAu6oQTwdY7iu2lxigbc9YYr9vPv5SzYny5tCXQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/eventstream-serde-universal@3.0.13':
    resolution: {integrity: sha512-L1Ib66+gg9uTnqp/18Gz4MDpJPKRE44geOjOQ2SVc0eiaO5l255ADziATZgjQjqumC7yPtp1XnjHlF1srcwjKw==}
    engines: {node: '>=16.0.0'}

  '@smithy/fetch-http-handler@3.2.9':
    resolution: {integrity: sha512-hYNVQOqhFQ6vOpenifFME546f0GfJn2OiQ3M0FDmuUu8V/Uiwy2wej7ZXxFBNqdx0R5DZAqWM1l6VRhGz8oE6A==}

  '@smithy/fetch-http-handler@4.1.3':
    resolution: {integrity: sha512-6SxNltSncI8s689nvnzZQc/dPXcpHQ34KUj6gR/HBroytKOd/isMG3gJF/zBE1TBmTT18TXyzhg3O3SOOqGEhA==}

  '@smithy/hash-node@3.0.11':
    resolution: {integrity: sha512-emP23rwYyZhQBvklqTtwetkQlqbNYirDiEEwXl2v0GYWMnCzxst7ZaRAnWuy28njp5kAH54lvkdG37MblZzaHA==}
    engines: {node: '>=16.0.0'}

  '@smithy/invalid-dependency@3.0.11':
    resolution: {integrity: sha512-NuQmVPEJjUX6c+UELyVz8kUx8Q539EDeNwbRyu4IIF8MeV7hUtq1FB3SHVyki2u++5XLMFqngeMKk7ccspnNyQ==}

  '@smithy/is-array-buffer@2.2.0':
    resolution: {integrity: sha512-GGP3O9QFD24uGeAXYUjwSTXARoqpZykHadOmA8G5vfJPK0/DC67qa//0qvqrJzL1xc8WQWX7/yc7fwudjPHPhA==}
    engines: {node: '>=14.0.0'}

  '@smithy/is-array-buffer@3.0.0':
    resolution: {integrity: sha512-+Fsu6Q6C4RSJiy81Y8eApjEB5gVtM+oFKTffg+jSuwtvomJJrhUJBu2zS8wjXSgH/g1MKEWrzyChTBe6clb5FQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/md5-js@2.0.7':
    resolution: {integrity: sha512-2i2BpXF9pI5D1xekqUsgQ/ohv5+H//G9FlawJrkOJskV18PgJ8LiNbLiskMeYt07yAsSTZR7qtlcAaa/GQLWww==}

  '@smithy/middleware-content-length@3.0.13':
    resolution: {integrity: sha512-zfMhzojhFpIX3P5ug7jxTjfUcIPcGjcQYzB9t+rv0g1TX7B0QdwONW+ATouaLoD7h7LOw/ZlXfkq4xJ/g2TrIw==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-endpoint@3.2.8':
    resolution: {integrity: sha512-OEJZKVUEhMOqMs3ktrTWp7UvvluMJEvD5XgQwRePSbDg1VvBaL8pX8mwPltFn6wk1GySbcVwwyldL8S+iqnrEQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-retry@3.0.34':
    resolution: {integrity: sha512-yVRr/AAtPZlUvwEkrq7S3x7Z8/xCd97m2hLDaqdz6ucP2RKHsBjEqaUA2ebNv2SsZoPEi+ZD0dZbOB1u37tGCA==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-serde@3.0.11':
    resolution: {integrity: sha512-KzPAeySp/fOoQA82TpnwItvX8BBURecpx6ZMu75EZDkAcnPtO6vf7q4aH5QHs/F1s3/snQaSFbbUMcFFZ086Mw==}
    engines: {node: '>=16.0.0'}

  '@smithy/middleware-stack@3.0.11':
    resolution: {integrity: sha512-1HGo9a6/ikgOMrTrWL/WiN9N8GSVYpuRQO5kjstAq4CvV59bjqnh7TbdXGQ4vxLD3xlSjfBjq5t1SOELePsLnA==}
    engines: {node: '>=16.0.0'}

  '@smithy/node-config-provider@3.1.12':
    resolution: {integrity: sha512-O9LVEu5J/u/FuNlZs+L7Ikn3lz7VB9hb0GtPT9MQeiBmtK8RSY3ULmsZgXhe6VAlgTw0YO+paQx4p8xdbs43vQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/node-http-handler@3.3.3':
    resolution: {integrity: sha512-BrpZOaZ4RCbcJ2igiSNG16S+kgAc65l/2hmxWdmhyoGWHTLlzQzr06PXavJp9OBlPEG/sHlqdxjWmjzV66+BSQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/property-provider@3.1.11':
    resolution: {integrity: sha512-I/+TMc4XTQ3QAjXfOcUWbSS073oOEAxgx4aZy8jHaf8JQnRkq2SZWw8+PfDtBvLUjcGMdxl+YwtzWe6i5uhL/A==}
    engines: {node: '>=16.0.0'}

  '@smithy/protocol-http@4.1.8':
    resolution: {integrity: sha512-hmgIAVyxw1LySOwkgMIUN0kjN8TG9Nc85LJeEmEE/cNEe2rkHDUWhnJf2gxcSRFLWsyqWsrZGw40ROjUogg+Iw==}
    engines: {node: '>=16.0.0'}

  '@smithy/querystring-builder@3.0.11':
    resolution: {integrity: sha512-u+5HV/9uJaeLj5XTb6+IEF/dokWWkEqJ0XiaRRogyREmKGUgZnNecLucADLdauWFKUNbQfulHFEZEdjwEBjXRg==}
    engines: {node: '>=16.0.0'}

  '@smithy/querystring-parser@3.0.11':
    resolution: {integrity: sha512-Je3kFvCsFMnso1ilPwA7GtlbPaTixa3WwC+K21kmMZHsBEOZYQaqxcMqeFFoU7/slFjKDIpiiPydvdJm8Q/MCw==}
    engines: {node: '>=16.0.0'}

  '@smithy/service-error-classification@3.0.11':
    resolution: {integrity: sha512-QnYDPkyewrJzCyaeI2Rmp7pDwbUETe+hU8ADkXmgNusO1bgHBH7ovXJiYmba8t0fNfJx75fE8dlM6SEmZxheog==}
    engines: {node: '>=16.0.0'}

  '@smithy/shared-ini-file-loader@3.1.12':
    resolution: {integrity: sha512-1xKSGI+U9KKdbG2qDvIR9dGrw3CNx+baqJfyr0igKEpjbHL5stsqAesYBzHChYHlelWtb87VnLWlhvfCz13H8Q==}
    engines: {node: '>=16.0.0'}

  '@smithy/signature-v4@4.2.4':
    resolution: {integrity: sha512-5JWeMQYg81TgU4cG+OexAWdvDTs5JDdbEZx+Qr1iPbvo91QFGzjy0IkXAKaXUHqmKUJgSHK0ZxnCkgZpzkeNTA==}
    engines: {node: '>=16.0.0'}

  '@smithy/smithy-client@3.7.0':
    resolution: {integrity: sha512-9wYrjAZFlqWhgVo3C4y/9kpc68jgiSsKUnsFPzr/MSiRL93+QRDafGTfhhKAb2wsr69Ru87WTiqSfQusSmWipA==}
    engines: {node: '>=16.0.0'}

  '@smithy/types@2.12.0':
    resolution: {integrity: sha512-QwYgloJ0sVNBeBuBs65cIkTbfzV/Q6ZNPCJ99EICFEdJYG50nGIY/uYXp+TbsdJReIuPr0a0kXmCvren3MbRRw==}
    engines: {node: '>=14.0.0'}

  '@smithy/types@3.7.2':
    resolution: {integrity: sha512-bNwBYYmN8Eh9RyjS1p2gW6MIhSO2rl7X9QeLM8iTdcGRP+eDiIWDt66c9IysCc22gefKszZv+ubV9qZc7hdESg==}
    engines: {node: '>=16.0.0'}

  '@smithy/types@4.1.0':
    resolution: {integrity: sha512-enhjdwp4D7CXmwLtD6zbcDMbo6/T6WtuuKCY49Xxc6OMOmUWlBEBDREsxxgV2LIdeQPW756+f97GzcgAwp3iLw==}
    engines: {node: '>=18.0.0'}

  '@smithy/url-parser@3.0.11':
    resolution: {integrity: sha512-TmlqXkSk8ZPhfc+SQutjmFr5FjC0av3GZP4B/10caK1SbRwe/v+Wzu/R6xEKxoNqL+8nY18s1byiy6HqPG37Aw==}

  '@smithy/util-base64@3.0.0':
    resolution: {integrity: sha512-Kxvoh5Qtt0CDsfajiZOCpJxgtPHXOKwmM+Zy4waD43UoEMA+qPxxa98aE/7ZhdnBFZFXMOiBR5xbcaMhLtznQQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-body-length-browser@3.0.0':
    resolution: {integrity: sha512-cbjJs2A1mLYmqmyVl80uoLTJhAcfzMOyPgjwAYusWKMdLeNtzmMz9YxNl3/jRLoxSS3wkqkf0jwNdtXWtyEBaQ==}

  '@smithy/util-body-length-node@3.0.0':
    resolution: {integrity: sha512-Tj7pZ4bUloNUP6PzwhN7K386tmSmEET9QtQg0TgdNOnxhZvCssHji+oZTUIuzxECRfG8rdm2PMw2WCFs6eIYkA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-buffer-from@2.2.0':
    resolution: {integrity: sha512-IJdWBbTcMQ6DA0gdNhh/BwrLkDR+ADW5Kr1aZmd4k3DIF6ezMV4R2NIAmT08wQJ3yUK82thHWmC/TnK/wpMMIA==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-buffer-from@3.0.0':
    resolution: {integrity: sha512-aEOHCgq5RWFbP+UDPvPot26EJHjOC+bRgse5A8V3FSShqd5E5UN4qc7zkwsvJPPAVsf73QwYcHN1/gt/rtLwQA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-config-provider@3.0.0':
    resolution: {integrity: sha512-pbjk4s0fwq3Di/ANL+rCvJMKM5bzAQdE5S/6RL5NXgMExFAi6UgQMPOm5yPaIWPpr+EOXKXRonJ3FoxKf4mCJQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-defaults-mode-browser@3.0.34':
    resolution: {integrity: sha512-FumjjF631lR521cX+svMLBj3SwSDh9VdtyynTYDAiBDEf8YPP5xORNXKQ9j0105o5+ARAGnOOP/RqSl40uXddA==}
    engines: {node: '>= 10.0.0'}

  '@smithy/util-defaults-mode-node@3.0.34':
    resolution: {integrity: sha512-vN6aHfzW9dVVzkI0wcZoUXvfjkl4CSbM9nE//08lmUMyf00S75uuCpTrqF9uD4bD9eldIXlt53colrlwKAT8Gw==}
    engines: {node: '>= 10.0.0'}

  '@smithy/util-endpoints@2.1.7':
    resolution: {integrity: sha512-tSfcqKcN/Oo2STEYCABVuKgJ76nyyr6skGl9t15hs+YaiU06sgMkN7QYjo0BbVw+KT26zok3IzbdSOksQ4YzVw==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-hex-encoding@2.0.0':
    resolution: {integrity: sha512-c5xY+NUnFqG6d7HFh1IFfrm3mGl29lC+vF+geHv4ToiuJCBmIfzx6IeHLg+OgRdPFKDXIw6pvi+p3CsscaMcMA==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-hex-encoding@3.0.0':
    resolution: {integrity: sha512-eFndh1WEK5YMUYvy3lPlVmYY/fZcQE1D8oSf41Id2vCeIkKJXPcYDCZD+4+xViI6b1XSd7tE+s5AmXzz5ilabQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-middleware@3.0.11':
    resolution: {integrity: sha512-dWpyc1e1R6VoXrwLoLDd57U1z6CwNSdkM69Ie4+6uYh2GC7Vg51Qtan7ITzczuVpqezdDTKJGJB95fFvvjU/ow==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-retry@3.0.11':
    resolution: {integrity: sha512-hJUC6W7A3DQgaee3Hp9ZFcOxVDZzmBIRBPlUAk8/fSOEl7pE/aX7Dci0JycNOnm9Mfr0KV2XjIlUOcGWXQUdVQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-stream@3.3.4':
    resolution: {integrity: sha512-SGhGBG/KupieJvJSZp/rfHHka8BFgj56eek9px4pp7lZbOF+fRiVr4U7A3y3zJD8uGhxq32C5D96HxsTC9BckQ==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-uri-escape@3.0.0':
    resolution: {integrity: sha512-LqR7qYLgZTD7nWLBecUi4aqolw8Mhza9ArpNEQ881MJJIU2sE5iHCK6TdyqqzcDLy0OPe10IY4T8ctVdtynubg==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-utf8@2.0.0':
    resolution: {integrity: sha512-rctU1VkziY84n5OXe3bPNpKR001ZCME2JCaBBFgtiM2hfKbHFudc/BkMuPab8hRbLd0j3vbnBTTZ1igBf0wgiQ==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-utf8@2.3.0':
    resolution: {integrity: sha512-R8Rdn8Hy72KKcebgLiv8jQcQkXoLMOGGv5uI1/k0l+snqkOzQ1R0ChUBCxWMlBsFMekWjq0wRudIweFs7sKT5A==}
    engines: {node: '>=14.0.0'}

  '@smithy/util-utf8@3.0.0':
    resolution: {integrity: sha512-rUeT12bxFnplYDe815GXbq/oixEGHfRFFtcTF3YdDi/JaENIM6aSYYLJydG83UNzLXeRI5K8abYd/8Sp/QM0kA==}
    engines: {node: '>=16.0.0'}

  '@smithy/util-waiter@3.2.0':
    resolution: {integrity: sha512-PpjSboaDUE6yl+1qlg3Si57++e84oXdWGbuFUSAciXsVfEZJJJupR2Nb0QuXHiunt2vGR+1PTizOMvnUPaG2Qg==}
    engines: {node: '>=16.0.0'}

  '@supabase/auth-js@2.69.1':
    resolution: {integrity: sha512-FILtt5WjCNzmReeRLq5wRs3iShwmnWgBvxHfqapC/VoljJl+W8hDAyFmf1NVw3zH+ZjZ05AKxiKxVeb0HNWRMQ==}

  '@supabase/functions-js@2.4.4':
    resolution: {integrity: sha512-WL2p6r4AXNGwop7iwvul2BvOtuJ1YQy8EbOd0dhG1oN1q8el/BIRSFCFnWAMM/vJJlHWLi4ad22sKbKr9mvjoA==}

  '@supabase/node-fetch@2.6.15':
    resolution: {integrity: sha512-1ibVeYUacxWYi9i0cf5efil6adJ9WRyZBLivgjs+AUpewx1F3xPi7gLgaASI2SmIQxPoCEjAsLAzKPgMJVgOUQ==}
    engines: {node: 4.x || >=6.0.0}

  '@supabase/postgrest-js@1.19.4':
    resolution: {integrity: sha512-O4soKqKtZIW3olqmbXXbKugUtByD2jPa8kL2m2c1oozAO11uCcGrRhkZL0kVxjBLrXHE0mdSkFsMj7jDSfyNpw==}

  '@supabase/realtime-js@2.11.8-next.4':
    resolution: {integrity: sha512-gB/yYBv9tO3SXKQSK9WD6Fnh8+3r7BDrTxvdlGuM4zWop/AjmzhgB5Lgc0rt6ZY+ZOfUbTchNeQxYZLNZ84WiA==}

  '@supabase/storage-js@2.7.1':
    resolution: {integrity: sha512-asYHcyDR1fKqrMpytAS1zjyEfvxuOIp1CIXX7ji4lHHcJKqyk+sLl/Vxgm4sN6u8zvuUtae9e4kDxQP2qrwWBA==}

  '@supabase/supabase-js@2.49.5-next.5':
    resolution: {integrity: sha512-Sx9pHrDkUuqCVStz5lBrcXPNOOxQaWknLVOqwT5011z+4at9m+X73clnVjfD946mqBhW1UmNMGTTiBUSKglIQw==}

  '@testing-library/react-native@12.9.0':
    resolution: {integrity: sha512-wIn/lB1FjV2N4Q7i9PWVRck3Ehwq5pkhAef5X5/bmQ78J/NoOsGbVY2/DG5Y9Lxw+RfE+GvSEh/fe5Tz6sKSvw==}
    peerDependencies:
      jest: '>=28.0.0'
      react: '>=16.8.0'
      react-native: '>=0.59'
      react-test-renderer: '>=16.8.0'
    peerDependenciesMeta:
      jest:
        optional: true

  '@tootallnate/once@2.0.0':
    resolution: {integrity: sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==}
    engines: {node: '>= 10'}

  '@tsconfig/node10@1.0.11':
    resolution: {integrity: sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==}

  '@tsconfig/node12@1.0.11':
    resolution: {integrity: sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==}

  '@tsconfig/node14@1.0.3':
    resolution: {integrity: sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==}

  '@tsconfig/node16@1.0.4':
    resolution: {integrity: sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==}

  '@types/aws-lambda@8.10.147':
    resolution: {integrity: sha512-nD0Z9fNIZcxYX5Mai2CTmFD7wX7UldCkW2ezCF8D1T5hdiLsnTWDGRpfRYntU6VjTdLQjOvyszru7I1c1oCQew==}

  '@types/babel__core@7.20.5':
    resolution: {integrity: sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==}

  '@types/babel__generator@7.6.8':
    resolution: {integrity: sha512-ASsj+tpEDsEiFr1arWrlN6V3mdfjRMZt6LtK/Vp/kreFLnr5QH5+DhvD5nINYZXzwJvXeGq+05iUXcAzVrqWtw==}

  '@types/babel__template@7.4.4':
    resolution: {integrity: sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==}

  '@types/babel__traverse@7.20.6':
    resolution: {integrity: sha512-r1bzfrm0tomOI8g1SzvCaQHo6Lcv6zu0EA+W2kHrt8dyrHQxGzBBL4kdkzIS+jBMV+EYcMAEAqXqYaLJq5rOZg==}

  '@types/eslint-scope@3.7.7':
    resolution: {integrity: sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==}

  '@types/eslint@9.6.1':
    resolution: {integrity: sha512-FXx2pKgId/WyYo2jXw63kk7/+TY7u7AziEJxJAnSFzHlqTAS3Ync6SvgYAN/k4/PQpnnVuzoMuVnByKK2qp0ag==}

  '@types/estree@1.0.6':
    resolution: {integrity: sha512-AYnb1nQyY49te+VRAVgmzfcgjYS91mY5P0TKUDCLEM+gNnA+3T6rWITXRLYCpahpqSQbN5cE+gHpnPyXjHWxcw==}

  '@types/graceful-fs@4.1.9':
    resolution: {integrity: sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==}

  '@types/hammerjs@2.0.46':
    resolution: {integrity: sha512-ynRvcq6wvqexJ9brDMS4BnBLzmr0e14d6ZJTEShTBWKymQiHwlAyGu0ZPEFI2Fh1U53F7tN9ufClWM5KvqkKOw==}

  '@types/i18n-js@3.8.2':
    resolution: {integrity: sha512-F+AuFCjllE1A0W/YUxJB13q2t7cWITMqXOTXQ/InfXxxT8nXrrqL7s/8Pv6XThGjFPemukElwk6QlMOKCEg7eQ==}

  '@types/istanbul-lib-coverage@2.0.6':
    resolution: {integrity: sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==}

  '@types/istanbul-lib-report@3.0.3':
    resolution: {integrity: sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==}

  '@types/istanbul-reports@3.0.4':
    resolution: {integrity: sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==}

  '@types/jest@29.5.14':
    resolution: {integrity: sha512-ZN+4sdnLUbo8EVvVc2ao0GFW6oVrQRPn4K2lglySj7APvSrgzxHiNNK99us4WDMi57xxA2yggblIAMNhXOotLQ==}

  '@types/jsdom@20.0.1':
    resolution: {integrity: sha512-d0r18sZPmMQr1eG35u12FZfhIXNrnsPU/g5wvRKCUf/tOGilKKwYMYGqh33BNR6ba+2gkHw1EUiHoN3mn7E5IQ==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/json5@0.0.29':
    resolution: {integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==}

  '@types/node@22.13.5':
    resolution: {integrity: sha512-+lTU0PxZXn0Dr1NBtC7Y8cR21AJr87dLLU953CWA6pMxxv/UDc7jYAY90upcrie1nRcD6XNG5HOYEDtgW5TxAg==}

  '@types/phoenix@1.6.6':
    resolution: {integrity: sha512-PIzZZlEppgrpoT2QgbnDU+MMzuR6BbCjllj0bM70lWoejMeNJAxCchxnv7J3XFkI8MpygtRpzXrIlmWUBclP5A==}

  '@types/react-native-onboarding-swiper@1.1.9':
    resolution: {integrity: sha512-xCvsk7e6gQ9tEsK/xUPGA88mjF4sz8xFWAt+8de5xyjvrp3/nY0RJKPUd8ZtZzhGqcDm2tdsfI6KdNS5XuDxuA==}

  '@types/react-native-vector-icons@6.4.18':
    resolution: {integrity: sha512-YGlNWb+k5laTBHd7+uZowB9DpIK3SXUneZqAiKQaj1jnJCZM0x71GDim5JCTMi4IFkhc9m8H/Gm28T5BjyivUw==}

  '@types/react-native@0.70.19':
    resolution: {integrity: sha512-c6WbyCgWTBgKKMESj/8b4w+zWcZSsCforson7UdXtXMecG3MxCinYi6ihhrHVPyUrVzORsvEzK8zg32z4pK6Sg==}

  '@types/react-reconciler@0.28.9':
    resolution: {integrity: sha512-HHM3nxyUZ3zAylX8ZEyrDNd2XZOnQ0D5XfunJF5FLQnZbHHYq4UWvW1QfelQNXv1ICNkwYhfxjwfnqivYB6bFg==}
    peerDependencies:
      '@types/react': '*'

  '@types/react@19.0.14':
    resolution: {integrity: sha512-ixLZ7zG7j1fM0DijL9hDArwhwcCb4vqmePgwtV0GfnkHRSCUEv4LvzarcTdhoqgyMznUx/EhoTUv31CKZzkQlw==}

  '@types/semver@7.5.8':
    resolution: {integrity: sha512-I8EUhyrgfLrcTkzV3TSsGyl1tSuPrEDzr0yd5m90UgNxQkyDXULk3b6MlQqTCpZpNtWe1K0hzclnZkTcLBe2UQ==}

  '@types/stack-utils@2.0.3':
    resolution: {integrity: sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==}

  '@types/tough-cookie@4.0.5':
    resolution: {integrity: sha512-/Ad8+nIOV7Rl++6f1BdKxFSMgmoqEoYbHRpPcx3JEfv8VRsQe9Z4mCXeJBzxs7mbHY/XOZZuXlRNfhpVPbs6ZA==}

  '@types/uuid@9.0.8':
    resolution: {integrity: sha512-jg+97EGIcY9AGHJJRaaPVgetKDsrTgbRjQ5Msgjh/DQKEFl0DtyRr/VCOyD1T2R1MNeWPK/u7JoGhlDZnKBAfA==}

  '@types/ws@8.18.1':
    resolution: {integrity: sha512-ThVF6DCVhA8kUGy+aazFQ4kXQ7E1Ty7A3ypFOe0IcJV8O/M511G99AW24irKrW56Wt44yG9+ij8FaqoBGkuBXg==}

  '@types/yargs-parser@21.0.3':
    resolution: {integrity: sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==}

  '@types/yargs@17.0.33':
    resolution: {integrity: sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==}

  '@typescript-eslint/eslint-plugin@5.62.0':
    resolution: {integrity: sha512-TiZzBSJja/LbhNPvk6yc0JrX9XqhQ0hdh6M2svYfsHGejaKFIAGd9MQ+ERIMzLGlN/kZoYIgdxFV0PuljTKXag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      '@typescript-eslint/parser': ^5.0.0
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/parser@5.62.0':
    resolution: {integrity: sha512-VlJEV0fOQ7BExOsHYAGrgbEiZoi8D+Bl2+f6V2RrXerRSylnp+ZBHmPvaIa8cz0Ajx7WO7Z5RqfgYg7ED1nRhA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/scope-manager@5.62.0':
    resolution: {integrity: sha512-VXuvVvZeQCQb5Zgf4HAxc04q5j+WrNAtNh9OwCsCgpKqESMTu3tF/jhZ3xG6T4NZwWl65Bg8KuS2uEvhSfLl0w==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/type-utils@5.62.0':
    resolution: {integrity: sha512-xsSQreu+VnfbqQpW5vnCJdq1Z3Q0U31qiWmRhr98ONQmcp/yhiPJFPq8MXiJVLiksmOKSjIldZzkebzHuCGzew==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '*'
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/types@5.62.0':
    resolution: {integrity: sha512-87NVngcbVXUahrRTqIK27gD2t5Cu1yuCXxbLcFtCzZGlfyVWWh8mLHkoxzjsB6DDNnvdL+fW8MiwPEJyGJQDgQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@typescript-eslint/typescript-estree@5.62.0':
    resolution: {integrity: sha512-CmcQ6uY7b9y694lKdRB8FEel7JbU/40iSAPomu++SjLMntB+2Leay2LO6i8VnJk58MtE9/nQSFIH6jpyRWyYzA==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      typescript: '*'
    peerDependenciesMeta:
      typescript:
        optional: true

  '@typescript-eslint/utils@5.62.0':
    resolution: {integrity: sha512-n8oxjeb5aIbPFEtmQxQYOLI0i9n5ySBEY/ZEHHZqKQSFnxio1rv6dthascc9dLuwrL0RC5mPCxB7vnAVGAYWAQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0

  '@typescript-eslint/visitor-keys@5.62.0':
    resolution: {integrity: sha512-07ny+LHRzQXepkGg6w0mFY41fVUNBrL2Roj/++7V1txKugfjm/Ci/qSND03r2RhlJhJYMcTn9AhhSSqQp0Ysyw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  '@urql/core@5.1.0':
    resolution: {integrity: sha512-yC3sw8yqjbX45GbXxfiBY8GLYCiyW/hLBbQF9l3TJrv4ro00Y0ChkKaD9I2KntRxAVm9IYBqh0awX8fwWAe/Yw==}

  '@urql/exchange-retry@1.3.1':
    resolution: {integrity: sha512-EEmtFu8JTuwsInqMakhLq+U3qN8ZMd5V3pX44q0EqD2imqTDsa8ikZqJ1schVrN8HljOdN+C08cwZ1/r5uIgLw==}
    peerDependencies:
      '@urql/core': ^5.0.0

  '@webassemblyjs/ast@1.14.1':
    resolution: {integrity: sha512-nuBEDgQfm1ccRp/8bCQrx1frohyufl4JlbMMZ4P1wpeOfDhF6FQkxZJ1b/e+PLwr6X1Nhw6OLme5usuBWYBvuQ==}

  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    resolution: {integrity: sha512-6oXyTOzbKxGH4steLbLNOu71Oj+C8Lg34n6CqRvqfS2O71BxY6ByfMDRhBytzknj9yGUPVJ1qIKhRlAwO1AovA==}

  '@webassemblyjs/helper-api-error@1.13.2':
    resolution: {integrity: sha512-U56GMYxy4ZQCbDZd6JuvvNV/WFildOjsaWD3Tzzvmw/mas3cXzRJPMjP83JqEsgSbyrmaGjBfDtV7KDXV9UzFQ==}

  '@webassemblyjs/helper-buffer@1.14.1':
    resolution: {integrity: sha512-jyH7wtcHiKssDtFPRB+iQdxlDf96m0E39yb0k5uJVhFGleZFoNw1c4aeIcVUPPbXUVJ94wwnMOAqUHyzoEPVMA==}

  '@webassemblyjs/helper-numbers@1.13.2':
    resolution: {integrity: sha512-FE8aCmS5Q6eQYcV3gI35O4J789wlQA+7JrqTTpJqn5emA4U2hvwJmvFRC0HODS+3Ye6WioDklgd6scJ3+PLnEA==}

  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    resolution: {integrity: sha512-3QbLKy93F0EAIXLh0ogEVR6rOubA9AoZ+WRYhNbFyuB70j3dRdwH9g+qXhLAO0kiYGlg3TxDV+I4rQTr/YNXkA==}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    resolution: {integrity: sha512-ds5mXEqTJ6oxRoqjhWDU83OgzAYjwsCV8Lo/N+oRsNDmx/ZDpqalmrtgOMkHwxsG0iI//3BwWAErYRHtgn0dZw==}

  '@webassemblyjs/ieee754@1.13.2':
    resolution: {integrity: sha512-4LtOzh58S/5lX4ITKxnAK2USuNEvpdVV9AlgGQb8rJDHaLeHciwG4zlGr0j/SNWlr7x3vO1lDEsuePvtcDNCkw==}

  '@webassemblyjs/leb128@1.13.2':
    resolution: {integrity: sha512-Lde1oNoIdzVzdkNEAWZ1dZ5orIbff80YPdHx20mrHwHrVNNTjNr8E3xz9BdpcGqRQbAEa+fkrCb+fRFTl/6sQw==}

  '@webassemblyjs/utf8@1.13.2':
    resolution: {integrity: sha512-3NQWGjKTASY1xV5m7Hr0iPeXD9+RDobLll3T9d2AO+g3my8xy5peVyjSag4I50mR1bBSN/Ct12lo+R9tJk0NZQ==}

  '@webassemblyjs/wasm-edit@1.14.1':
    resolution: {integrity: sha512-RNJUIQH/J8iA/1NzlE4N7KtyZNHi3w7at7hDjvRNm5rcUXa00z1vRz3glZoULfJ5mpvYhLybmVcwcjGrC1pRrQ==}

  '@webassemblyjs/wasm-gen@1.14.1':
    resolution: {integrity: sha512-AmomSIjP8ZbfGQhumkNvgC33AY7qtMCXnN6bL2u2Js4gVCg8fp735aEiMSBbDR7UQIj90n4wKAFUSEd0QN2Ukg==}

  '@webassemblyjs/wasm-opt@1.14.1':
    resolution: {integrity: sha512-PTcKLUNvBqnY2U6E5bdOQcSM+oVP/PmrDY9NzowJjislEjwP/C4an2303MCVS2Mg9d3AJpIGdUFIQQWbPds0Sw==}

  '@webassemblyjs/wasm-parser@1.14.1':
    resolution: {integrity: sha512-JLBl+KZ0R5qB7mCnud/yyX08jWFw5MsoalJ1pQ4EdFlgj9VdXKGuENGsiCIjegI1W7p91rUlcB/LB5yRJKNTcQ==}

  '@webassemblyjs/wast-printer@1.14.1':
    resolution: {integrity: sha512-kPSSXE6De1XOR820C90RIo2ogvZG+c3KiHzqUoO/F34Y2shGzesfqv7o57xrxovZJH/MetF5UjroJ/R/3isoiw==}

  '@webgpu/types@0.1.21':
    resolution: {integrity: sha512-pUrWq3V5PiSGFLeLxoGqReTZmiiXwY3jRkIG5sLLKjyqNxrwm/04b4nw7LSmGWJcKk59XOM/YRTUwOzo4MMlow==}

  '@xmldom/xmldom@0.8.10':
    resolution: {integrity: sha512-2WALfTl4xo2SkGCYRt6rDTFfk9R1czmBvUQy12gK2KuRKIpWEhcbbzy8EZXtz/jkRqHX8bFEc6FC1HjX4TUWYw==}
    engines: {node: '>=10.0.0'}

  '@xtuc/ieee754@1.2.0':
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}

  '@xtuc/long@4.2.2':
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}

  '@yarnpkg/lockfile@1.1.0':
    resolution: {integrity: sha512-GpSwvyXOcOOlV70vbnzjj4fW5xW/FdUF6nQEt1ENy7m4ZCczi1+/buVUPAqmGfqznsORNFzUMjctTIp8a9tuCQ==}

  abab@2.0.6:
    resolution: {integrity: sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==}
    deprecated: Use your platform's native atob() and btoa() methods instead

  abort-controller@3.0.0:
    resolution: {integrity: sha512-h8lQ8tacZYnR3vNQTgibj+tODHI5/+l06Au2Pcriv/Gmet0eaj4TwWH41sO9wnHDiQsEj19q0drzdWdeAHtweg==}
    engines: {node: '>=6.5'}

  accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}

  acorn-globals@7.0.1:
    resolution: {integrity: sha512-umOSDSDrfHbTNPuNpC2NSnnA3LUrqpevPb4T9jRx4MagXNS0rs+gwiTcAvqCRmsD6utzsrzNt+ebm00SNWiC3Q==}

  acorn-jsx@5.3.2:
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==}
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn-loose@8.4.0:
    resolution: {integrity: sha512-M0EUka6rb+QC4l9Z3T0nJEzNOO7JcoJlYMrBlyBCiFSXRyxjLKayd4TbQs2FDRWQU1h9FR7QVNHt+PEaoNL5rQ==}
    engines: {node: '>=0.4.0'}

  acorn-walk@8.3.4:
    resolution: {integrity: sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==}
    engines: {node: '>=0.4.0'}

  acorn@8.14.0:
    resolution: {integrity: sha512-cl669nCJTZBsL97OF4kUQm5g5hC2uihk0NxY3WENAC0TYdILVkAyHymAntgxGkl7K+t0cXIrH5siy5S4XkFycA==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  aes-js@3.1.2:
    resolution: {integrity: sha512-e5pEa2kBnBOgR4Y/p20pskXI74UEz7de8ZGVo58asOtvSVG5YAbJeELPZxOmt+Bnz3rX753YKhfIn4X4l1PPRQ==}

  agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}

  agent-base@7.1.3:
    resolution: {integrity: sha512-jRR5wdylq8CkOe6hei19GGZnxM6rBGwFl3Bg0YItGDimvjGtAvdZk4Pu6Cl4u4Igsws4a1fd1Vq3ezrhn4KmFw==}
    engines: {node: '>= 14'}

  ajv-formats@2.1.1:
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-keywords@5.1.0:
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==}
    peerDependencies:
      ajv: ^8.8.2

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ajv@8.17.1:
    resolution: {integrity: sha512-B/gBuNg5SiMTrPkC+A2+cW0RszwxYmn6VYxB/inlBStS5nx6xHIt/ehKRhIMhqusl7a8LjQoZnjCs5vhwxOQ1g==}

  anser@1.4.10:
    resolution: {integrity: sha512-hCv9AqTQ8ycjpSd3upOJd7vFwW1JaoYQ7tpham03GJ1ca8/65rqn0RpaWpItOAd6ylW9wAw6luXYPJIyPFVOww==}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}

  ansi-escapes@6.2.1:
    resolution: {integrity: sha512-4nJ3yixlEthEJ9Rk4vPcdBRkZvQZlYyu8j4/Mqz5sgIkddmEnH2Yj2ZrnP9S3tQOvSNRUIgVNF/1yPpRAGNRig==}
    engines: {node: '>=14.16'}

  ansi-regex@4.1.1:
    resolution: {integrity: sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==}
    engines: {node: '>=6'}

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.1.0:
    resolution: {integrity: sha512-7HSX4QQb4CspciLpVFwyRe79O3xsIZDDLER21kERQ71oaPodF8jL725AgJMFAYbooIqolJoRLuM81SpeUkpkvA==}
    engines: {node: '>=12'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@5.2.0:
    resolution: {integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==}
    engines: {node: '>=10'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  apisauce@3.0.1:
    resolution: {integrity: sha512-4HEmETv0skPW+bFS4TzB5nQ5y2TdafbUpnAjp83MW8Re9lHwngao2hpnk7aIaxExJqSTxkpWl+ThgZbqjx2bpQ==}

  application-config-path@0.1.1:
    resolution: {integrity: sha512-zy9cHePtMP0YhwG+CfHm0bgwdnga2X3gZexpdCwEj//dpb+TKajtiC8REEUJUSq6Ab4f9cgNy2l8ObXzCXFkEw==}

  arg@4.1.3:
    resolution: {integrity: sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==}

  arg@5.0.2:
    resolution: {integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==}

  argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  array-buffer-byte-length@1.0.2:
    resolution: {integrity: sha512-LHE+8BuR7RYGDKvnrmcuSq3tDcKv9OFEXQt/HpbZhY7V6h0zlUXutnAD82GiFx9rdieCMjkvtcsPqBwgUl1Iiw==}
    engines: {node: '>= 0.4'}

  array-includes@3.1.8:
    resolution: {integrity: sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==}
    engines: {node: '>= 0.4'}

  array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==}
    engines: {node: '>=8'}

  array.prototype.flat@1.3.3:
    resolution: {integrity: sha512-rwG/ja1neyLqCuGZ5YYrznA62D4mZXg0i1cIskIUKSiqF3Cje9/wXAls9B9s1Wa2fomMsIv8czB8jZcPmxCXFg==}
    engines: {node: '>= 0.4'}

  array.prototype.flatmap@1.3.3:
    resolution: {integrity: sha512-Y7Wt51eKJSyi80hFrJCePGGNo5ktJCslFuboqJsbf57CCPcm5zztluPlc4/aD8sWsKvlwatezpV4U1efk8kpjg==}
    engines: {node: '>= 0.4'}

  arraybuffer.prototype.slice@1.0.4:
    resolution: {integrity: sha512-BNoCY6SXXPQ7gF2opIP4GBE+Xw7U+pHMYKuzjgCN3GwiaIR09UUeKfheyIry77QtrCBlC0KK0q5/TER/tYh3PQ==}
    engines: {node: '>= 0.4'}

  asap@2.0.6:
    resolution: {integrity: sha512-BSHWgDSAiKs50o2Re8ppvp3seVHXSRM44cdSsT9FfNEUUZLOGWVCsiWaRPWM1Znn+mqZ1OfVZ3z3DWEzSp7hRA==}

  async-function@1.0.0:
    resolution: {integrity: sha512-hsU18Ae8CDTR6Kgu9DYf0EbCr/a5iGL0rytQDobUcdpYOKokk8LEjVphnXkDkgpi0wYVsqrXuP0bZxJaTqdgoA==}
    engines: {node: '>= 0.4'}

  async-limiter@1.0.1:
    resolution: {integrity: sha512-csOlWGAcRFJaI6m+F2WKdnMKr4HhdhFVBk0H/QbJFMCr+uO2kwohwXQPxw/9OCxp05r5ghVBFSyioixx3gfkNQ==}

  async@3.2.6:
    resolution: {integrity: sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  at-least-node@1.0.0:
    resolution: {integrity: sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==}
    engines: {node: '>= 4.0.0'}

  available-typed-arrays@1.0.7:
    resolution: {integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==}
    engines: {node: '>= 0.4'}

  aws-amplify@6.14.4:
    resolution: {integrity: sha512-V1uRj0zg/pMsu0MWEg1tgUyR4YdO9Qfgo+P+tOtHUa0Id3SCWGnSaPCGQjIhli6kWHKT74vYsRwXdTfbKCA6xg==}

  axios@1.8.1:
    resolution: {integrity: sha512-NN+fvwH/kV01dYUQ3PTOZns4LWtWhOFCAhQ/pHb88WQ1hNe5V/dvFwc4VJcDL11LT9xSX0QtsR8sWUuyOuOq7g==}

  babel-jest@29.7.0:
    resolution: {integrity: sha512-BrvGY3xZSwEcCzKvKsCi2GgHqDqsYkOP4/by5xCgIwGXQxIEh+8ew3gmrE1y7XRR6LHZIj6yLYnUi/mm2KXKBg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@babel/core': ^7.8.0

  babel-plugin-istanbul@6.1.1:
    resolution: {integrity: sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==}
    engines: {node: '>=8'}

  babel-plugin-jest-hoist@29.6.3:
    resolution: {integrity: sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  babel-plugin-polyfill-corejs2@0.4.12:
    resolution: {integrity: sha512-CPWT6BwvhrTO2d8QVorhTCQw9Y43zOu7G9HigcfxvepOU6b8o3tcWad6oVgZIsZCTt42FFv97aA7ZJsbM4+8og==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.10.6:
    resolution: {integrity: sha512-b37+KR2i/khY5sKmWNVQAnitvquQbNdWy6lJdsr0kmquCKEEUgMKK4SboVM3HtfnZilfjr4MMQ7vY58FVWDtIA==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.11.1:
    resolution: {integrity: sha512-yGCqvBT4rwMczo28xkH/noxJ6MZ4nJfkVYdoDaC/utLtWrXxv27HVrzAeSbqR8SxDsp46n0YF47EbHoixy6rXQ==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.6.3:
    resolution: {integrity: sha512-LiWSbl4CRSIa5x/JAU6jZiG9eit9w6mz+yVMFwDE83LAWvt0AfGBoZ7HS/mkhrKuh2ZlzfVZYKoLjXdqw6Yt7Q==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-react-native-web@0.19.13:
    resolution: {integrity: sha512-4hHoto6xaN23LCyZgL9LJZc3olmAxd7b6jDzlZnKXAh4rRAbZRKNBJoOOdp46OBqgy+K0t0guTj5/mhA8inymQ==}

  babel-plugin-syntax-hermes-parser@0.25.1:
    resolution: {integrity: sha512-IVNpGzboFLfXZUAwkLFcI/bnqVbwky0jP3eBno4HKtqvQJAHBLdgxiG6lQ4to0+Q/YCN3PO0od5NZwIKyY4REQ==}

  babel-plugin-transform-flow-enums@0.0.2:
    resolution: {integrity: sha512-g4aaCrDDOsWjbm0PUUeVnkcVd6AKJsVc/MbnPhEotEpkeJQP6b8nzewohQi7+QS8UyPehOhGWn0nOwjvWpmMvQ==}

  babel-preset-current-node-syntax@1.1.0:
    resolution: {integrity: sha512-ldYss8SbBlWva1bs28q78Ju5Zq1F+8BrqBZZ0VFhLBvhh6lCpC2o3gDJi/5DRLs9FgYZCnmPYIVFU4lRXCkyUw==}
    peerDependencies:
      '@babel/core': ^7.0.0

  babel-preset-expo@13.1.11:
    resolution: {integrity: sha512-jigWjvhRVdm9UTPJ1wjLYJ0OJvD5vLZ8YYkEknEl6+9S1JWORO/y3xtHr/hNj5n34nOilZqdXrmNFcqKc8YTsg==}
    peerDependencies:
      babel-plugin-react-compiler: ^19.0.0-beta-e993439-20250405
    peerDependenciesMeta:
      babel-plugin-react-compiler:
        optional: true

  babel-preset-jest@29.6.3:
    resolution: {integrity: sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@babel/core': ^7.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  better-opn@3.0.2:
    resolution: {integrity: sha512-aVNobHnJqLiUelTaHat9DZ1qM2w0C0Eym4LPI/3JxOnSokGVdsl1T1kN7TFvsEAD8G47A6VKQ0TVHqbBnYMJlQ==}
    engines: {node: '>=12.0.0'}

  big-integer@1.6.52:
    resolution: {integrity: sha512-QxD8cf2eVqJOOz63z6JIN9BzvVs/dlySa5HGSBH5xtR8dPteIRQnBxxKqkNTiT6jbDTF6jAfrd4oMcND9RGbQg==}
    engines: {node: '>=0.6'}

  bignumber.js@9.1.2:
    resolution: {integrity: sha512-2/mKyZH9K85bzOEfhXDBFZTGd1CTs+5IHpeFQo9luiBG7hghdC851Pj2WAhb6E3R6b9tZj/XKhbg4fum+Kepug==}

  boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}

  bowser@2.11.0:
    resolution: {integrity: sha512-AlcaJBi/pqqJBIQ8U9Mcpc9i8Aqxn88Skv5d+xBX006BY5u8N3mGLHa5Lgppa7L/HfwgwLgZ6NYs+Ag6uUmJRA==}

  bplist-creator@0.1.0:
    resolution: {integrity: sha512-sXaHZicyEEmY86WyueLTQesbeoH/mquvarJaQNbjuOQO+7gbFcDEWqKmcWA4cOTLzFlfgvkiVxolk1k5bBIpmg==}

  bplist-parser@0.3.1:
    resolution: {integrity: sha512-PyJxiNtA5T2PlLIeBot4lbp7rj4OadzjnMZD/G5zuBNt8ei/yCU7+wW0h2bag9vr8c+/WuRWmSxbqAl9hL1rBA==}
    engines: {node: '>= 5.10.0'}

  bplist-parser@0.3.2:
    resolution: {integrity: sha512-apC2+fspHGI3mMKj+dGevkGo/tCqVB8jMb6i+OX+E29p0Iposz07fABkRIfVUPNd5A5VbuOz1bZbnmkKLYF+wQ==}
    engines: {node: '>= 5.10.0'}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  browserslist@4.24.4:
    resolution: {integrity: sha512-KDi1Ny1gSePi1vm0q4oxSF8b4DR44GF4BbmS2YdhPLOEqd8pDviZOGH/GsmRwoWJ2+5Lr085X7naowMwKHDG1A==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  bs-logger@0.2.6:
    resolution: {integrity: sha512-pd8DCoxmbgc7hyPKOvxtqNcjYoOsABPQdcCUjGp3d42VR2CX1ORhk2A87oqqu5R1kk+76nsxZupkmyd+MVtCog==}
    engines: {node: '>= 6'}

  bser@2.1.1:
    resolution: {integrity: sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==}

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  buffer@4.9.2:
    resolution: {integrity: sha512-xq+q3SRMOxGivLhBNaUdC64hDTQwejJ+H0T/NB1XMtTVEwNTrfFF3gAxiyW0Bu/xWEGhjVKgUcMhCrUy2+uCWg==}

  buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}

  builtins@5.1.0:
    resolution: {integrity: sha512-SW9lzGTLvWTP1AY8xeAMZimqDrIaSdLQUcVr9DMef51niJ022Ri87SwRRKYm4A6iHfkPaiVUu/Duw2Wc4J7kKg==}

  bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  call-bind@1.0.8:
    resolution: {integrity: sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==}
    engines: {node: '>= 0.4'}

  call-bound@1.0.3:
    resolution: {integrity: sha512-YTd+6wGlNlPxSuri7Y6X8tY2dmm12UMH66RpKMhiX6rsk5wXXnYgbUcOt8kiS31/AjfoTOvCsE+w8nZQLQnzHA==}
    engines: {node: '>= 0.4'}

  caller-callsite@2.0.0:
    resolution: {integrity: sha512-JuG3qI4QOftFsZyOn1qq87fq5grLIyk1JYd5lJmdA+fG7aQ9pA/i3JIJGcO3q0MrRcHlOt1U+ZeHW8Dq9axALQ==}
    engines: {node: '>=4'}

  caller-path@2.0.0:
    resolution: {integrity: sha512-MCL3sf6nCSXOwCTzvPKhN18TU7AHTvdtam8DAogxcrJ8Rjfbbg7Lgng64H9Iy+vUV6VGFClN/TyxBkAebLRR4A==}
    engines: {node: '>=4'}

  callsites@2.0.0:
    resolution: {integrity: sha512-ksWePWBloaWPxJYQ8TL0JHvtci6G5QTKwQ95RcWAa/lzoAKuAOflGdAK92hpHXjkwb8zLxoLNUoNYZgVsaJzvQ==}
    engines: {node: '>=4'}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==}
    engines: {node: '>=10'}

  caniuse-lite@1.0.30001701:
    resolution: {integrity: sha512-faRs/AW3jA9nTwmJBSO1PQ6L/EOgsB5HMQQq4iCu5zhPgVVgO/pZRHlmatwijZKetFw8/Pr4q6dEN8sJuq8qTw==}

  canvaskit-wasm@0.40.0:
    resolution: {integrity: sha512-Od2o+ZmoEw9PBdN/yCGvzfu0WVqlufBPEWNG452wY7E9aT8RBE+ChpZF526doOlg7zumO4iCS+RAeht4P0Gbpw==}

  chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}

  chalk@3.0.0:
    resolution: {integrity: sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==}
    engines: {node: '>=8'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  char-regex@1.0.2:
    resolution: {integrity: sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==}
    engines: {node: '>=10'}

  char-regex@2.0.2:
    resolution: {integrity: sha512-cbGOjAptfM2LVmWhwRFHEKTPkLwNddVmuqYZQt895yXwAsWsXObCG+YN4DGQ/JBtT4GP1a1lPPdio2z413LmTg==}
    engines: {node: '>=12.20'}

  chownr@3.0.0:
    resolution: {integrity: sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g==}
    engines: {node: '>=18'}

  chrome-launcher@0.15.2:
    resolution: {integrity: sha512-zdLEwNo3aUVzIhKhTtXfxhdvZhUghrnmkvcAq2NoDd+LeOHKf03H5jwZ8T/STsAlzyALkBVK552iaG1fGf1xVQ==}
    engines: {node: '>=12.13.0'}
    hasBin: true

  chrome-trace-event@1.0.4:
    resolution: {integrity: sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==}
    engines: {node: '>=6.0'}

  chromium-edge-launcher@0.2.0:
    resolution: {integrity: sha512-JfJjUnq25y9yg4FABRRVPmBGWPZZi+AQXT4mxupb67766/0UlhG8PAZCz6xzEMXTbW3CsSoE8PcCWA49n35mKg==}

  ci-info@2.0.0:
    resolution: {integrity: sha512-5tK7EtrZ0N+OLFMthtqOj4fI2Jeb88C4CAZPu25LDVUgXJ0A3Js4PMGqrn0JU1W0Mh1/Z8wZzYPxqUrXeBboCQ==}

  ci-info@3.9.0:
    resolution: {integrity: sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==}
    engines: {node: '>=8'}

  cjs-module-lexer@1.4.3:
    resolution: {integrity: sha512-9z8TZaGM1pfswYeXrUpzPrkx8UnWYdhJclsiYMm6x/w5+nN+8Tf/LnAgfLGQCm59qAOxU8WwHEq2vNwF6i4j+Q==}

  cli-cursor@2.1.0:
    resolution: {integrity: sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw==}
    engines: {node: '>=4'}

  cli-spinners@2.9.2:
    resolution: {integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==}
    engines: {node: '>=6'}

  cliui@7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}

  cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}

  clone@1.0.4:
    resolution: {integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==}
    engines: {node: '>=0.8'}

  co@4.6.0:
    resolution: {integrity: sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==}
    engines: {iojs: '>= 1.0.0', node: '>= 0.12.0'}

  collect-v8-coverage@1.0.2:
    resolution: {integrity: sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  color-string@1.9.1:
    resolution: {integrity: sha512-shrVawQFojnZv6xM40anx4CkoDP+fZsw/ZerEMsW/pyzsRbElpsL/DBVW7q3ExxwusdNXI3lXpuhEZkzs8p5Eg==}

  color@3.2.1:
    resolution: {integrity: sha512-aBl7dZI9ENN6fUGC7mWpMTPNHmWUSNan9tuWN6ahh5ZLNk9baLJOnSMlrQkHcrfFgz2/RigjUVAjdx36VcemKA==}

  color@4.2.3:
    resolution: {integrity: sha512-1rXeuUUiGGrykh+CeBdu5Ie7OJwinCgQY0bc7GCRxy5xVHy+moaqkpL/jqQq0MtQOeYcrqEz4abc5f0KtU7W4A==}
    engines: {node: '>=12.5.0'}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  command-exists@1.2.9:
    resolution: {integrity: sha512-LTQ/SGc+s0Xc0Fu5WaKnR0YiygZkm9eKFvyS+fRsU7/ZWFF8ykFM6Pc9aCVf1+xasOOZpO3BAVgVrKvsqKHV7w==}

  commander@12.1.0:
    resolution: {integrity: sha512-Vw8qHK3bZM9y/P10u3Vib8o/DdkvA2OtPtZvD871QKjy74Wj1WSKFILMPRPSdUSx5RFK1arlJzEtA4PkFgnbuA==}
    engines: {node: '>=18'}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  commander@4.1.1:
    resolution: {integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==}
    engines: {node: '>= 6'}

  commander@7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==}
    engines: {node: '>= 10'}

  compressible@2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}

  compression@1.8.0:
    resolution: {integrity: sha512-k6WLKfunuqCYD3t6AsuPGvQWaKwuLLh2/xHNcX4qE+vIfDNXpSqnrhwA7O53R7WVQUnt8dVAIW+YHr7xTgOgGA==}
    engines: {node: '>= 0.8.0'}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  connect@3.7.0:
    resolution: {integrity: sha512-ZqRXc+tZukToSNmh5C2iWMSoV3X1YUcPbqEM4DkEG5tNQXrQUZCNVGGv3IuicnkMtPfGf3Xtp8WCXs295iQ1pQ==}
    engines: {node: '>= 0.10.0'}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  core-js-compat@3.40.0:
    resolution: {integrity: sha512-0XEDpr5y5mijvw8Lbc6E5AkjrHfp7eEoPlu36SWeAbcL8fn1G1ANe8DBlo2XoNN89oVpxWwOjYIPVzR4ZvsKCQ==}

  cosmiconfig@5.2.1:
    resolution: {integrity: sha512-H65gsXo1SKjf8zmrJ67eJk8aIRKV5ff2D4uKZIBZShbhGSpEmsQOPW/SKMKYhSTrqR7ufy6RP69rPogdaPh/kA==}
    engines: {node: '>=4'}

  crc-32@1.2.2:
    resolution: {integrity: sha512-ROmzCKrTnOwybPcJApAA6WBWij23HVfGVNKqqrZpuyZOHqK2CwHSvpGuyt/UNNvaIjEd8X5IFGp4Mh+Ie1IHJQ==}
    engines: {node: '>=0.8'}
    hasBin: true

  create-jest@29.7.0:
    resolution: {integrity: sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true

  create-require@1.1.1:
    resolution: {integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==}

  cross-fetch@3.2.0:
    resolution: {integrity: sha512-Q+xVJLoGOeIMXZmbUK4HYk+69cQH6LudR0Vu/pRm2YlU/hDV9CiS0gKUMaWY5f2NeUH9C1nV3bsTlCo0FsTV1Q==}

  cross-spawn@7.0.6:
    resolution: {integrity: sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==}
    engines: {node: '>= 8'}

  crypto-random-string@2.0.0:
    resolution: {integrity: sha512-v1plID3y9r/lPhviJ1wrXpLeyUIGAZ2SHNYTEapm7/8A9nLPoyvVp3RK/EPFqn5kEznyWgYZNsRtYYIWbuG8KA==}
    engines: {node: '>=8'}

  css-in-js-utils@3.1.0:
    resolution: {integrity: sha512-fJAcud6B3rRu+KHYk+Bwf+WFL2MDCJJ1XG9x137tJQ0xYxor7XziQtuGFbWNdqrvF4Tk26O3H73nfVqXt/fW1A==}

  css-select@5.1.0:
    resolution: {integrity: sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==}

  css-tree@1.1.3:
    resolution: {integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==}
    engines: {node: '>=8.0.0'}

  css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}

  cssom@0.3.8:
    resolution: {integrity: sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==}

  cssom@0.5.0:
    resolution: {integrity: sha512-iKuQcq+NdHqlAcwUY0o/HL69XQrUaQdMjmStJ8JFmUaiiQErlhrmuigkg/CU4E2J0IyUKUrMAgl36TvN67MqTw==}

  cssstyle@2.3.0:
    resolution: {integrity: sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A==}
    engines: {node: '>=8'}

  csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==}

  d3-array@3.2.4:
    resolution: {integrity: sha512-tdQAmyA18i4J7wprpYq8ClcxZy3SC31QMeByyCFyRt7BVHdREQZ5lpzoe5mFEYZUWe+oq8HBvk9JjpibyEV4Jg==}
    engines: {node: '>=12'}

  d3-color@3.1.0:
    resolution: {integrity: sha512-zg/chbXyeBtMQ1LbD/WSoW2DpC3I0mpmPdW+ynRTj/x2DAWYrIY7qeZIHidozwV24m4iavr15lNwIwLxRmOxhA==}
    engines: {node: '>=12'}

  d3-dispatch@3.0.1:
    resolution: {integrity: sha512-rzUyPU/S7rwUflMyLc1ETDeBj0NRuHKKAcvukozwhshr6g6c5d8zh4c2gQjY2bZ0dXeGLWc1PF174P2tVvKhfg==}
    engines: {node: '>=12'}

  d3-drag@3.0.0:
    resolution: {integrity: sha512-pWbUJLdETVA8lQNJecMxoXfH6x+mO2UQo8rSmZ+QqxcbyA3hfeprFgIT//HW2nlHChWeIIMwS2Fq+gEARkhTkg==}
    engines: {node: '>=12'}

  d3-ease@3.0.1:
    resolution: {integrity: sha512-wR/XK3D3XcLIZwpbvQwQ5fK+8Ykds1ip7A2Txe0yxncXSdq1L9skcG7blcedkOX+ZcgxGAmLX1FrRGbADwzi0w==}
    engines: {node: '>=12'}

  d3-format@3.1.0:
    resolution: {integrity: sha512-YyUI6AEuY/Wpt8KWLgZHsIU86atmikuoOmCfommt0LYHiQSPjvX2AcFc38PX0CBpr2RCyZhjex+NS/LPOv6YqA==}
    engines: {node: '>=12'}

  d3-interpolate@3.0.1:
    resolution: {integrity: sha512-3bYs1rOD33uo8aqJfKP3JWPAibgw8Zm2+L9vBKEHJ2Rg+viTR7o5Mmv5mZcieN+FRYaAOWX5SJATX6k1PWz72g==}
    engines: {node: '>=12'}

  d3-path@3.1.0:
    resolution: {integrity: sha512-p3KP5HCf/bvjBSSKuXid6Zqijx7wIfNW+J/maPs+iwR35at5JCbLUT0LzF1cnjbCHWhqzQTIN2Jpe8pRebIEFQ==}
    engines: {node: '>=12'}

  d3-scale@4.0.2:
    resolution: {integrity: sha512-GZW464g1SH7ag3Y7hXjf8RoUuAFIqklOAq3MRl4OaWabTFJY9PN/E1YklhXLh+OQ3fM9yS2nOkCoS+WLZ6kvxQ==}
    engines: {node: '>=12'}

  d3-selection@3.0.0:
    resolution: {integrity: sha512-fmTRWbNMmsmWq6xJV8D19U/gw/bwrHfNXxrIN+HfZgnzqTHp9jOmKMhsTUjXOJnZOdZY9Q28y4yebKzqDKlxlQ==}
    engines: {node: '>=12'}

  d3-shape@3.2.0:
    resolution: {integrity: sha512-SaLBuwGm3MOViRq2ABk3eLoxwZELpH6zhl3FbAoJ7Vm1gofKx6El1Ib5z23NUEhF9AsGl7y+dzLe5Cw2AArGTA==}
    engines: {node: '>=12'}

  d3-time-format@4.1.0:
    resolution: {integrity: sha512-dJxPBlzC7NugB2PDLwo9Q8JiTR3M3e4/XANkreKSUxF8vvXKqm1Yfq4Q5dl8budlunRVlUUaDUgFt7eA8D6NLg==}
    engines: {node: '>=12'}

  d3-time@3.1.0:
    resolution: {integrity: sha512-VqKjzBLejbSMT4IgbmVgDjpkYrNWUYJnbCGo874u7MMKIWsILRX+OpX/gTk8MqjpT1A/c6HY2dCA77ZN0lkQ2Q==}
    engines: {node: '>=12'}

  d3-timer@3.0.1:
    resolution: {integrity: sha512-ndfJ/JxxMd3nw31uyKoY2naivF+r29V+Lc0svZxe1JvvIRmi8hUsrMvdOwgS1o6uBHmiz91geQ0ylPP0aj1VUA==}
    engines: {node: '>=12'}

  d3-transition@3.0.1:
    resolution: {integrity: sha512-ApKvfjsSR6tg06xrL434C0WydLr7JewBB3V+/39RMHsaXTOG0zmt/OAXeng5M5LBm0ojmxJrpomQVZ1aPvBL4w==}
    engines: {node: '>=12'}
    peerDependencies:
      d3-selection: 2 - 3

  d3-zoom@3.0.0:
    resolution: {integrity: sha512-b8AmV3kfQaqWAuacbPuNbL6vahnOJflOhexLzMMNLga62+/nh0JzvJ0aO/5a5MVgUFGS7Hu1P9P03o3fJkDCyw==}
    engines: {node: '>=12'}

  data-urls@3.0.2:
    resolution: {integrity: sha512-Jy/tj3ldjZJo63sVAvg6LHt2mHvl4V6AgRAmNDtLdm7faqtsx+aJG42rsyCo9JCoRVKwPFzKlIPx3DIibwSIaQ==}
    engines: {node: '>=12'}

  data-view-buffer@1.0.2:
    resolution: {integrity: sha512-EmKO5V3OLXh1rtK2wgXRansaK1/mtVdTUEiEI0W8RkvgT05kfxaH29PliLnpLP73yYO6142Q72QNa8Wx/A5CqQ==}
    engines: {node: '>= 0.4'}

  data-view-byte-length@1.0.2:
    resolution: {integrity: sha512-tuhGbE6CfTM9+5ANGf+oQb72Ky/0+s3xKUpHvShfiz2RxMFgFPjsXuRLBVMtvMs15awe45SRb83D6wH4ew6wlQ==}
    engines: {node: '>= 0.4'}

  data-view-byte-offset@1.0.1:
    resolution: {integrity: sha512-BS8PfmtDGnrgYdOonGZQdLZslWIeCGFP9tpan0hi1Co2Zr2NKADsvGYA8XxuG/4UWgJ6Cjtv+YJnB6MM69QGlQ==}
    engines: {node: '>= 0.4'}

  date-fns@2.30.0:
    resolution: {integrity: sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==}
    engines: {node: '>=0.11'}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.4.0:
    resolution: {integrity: sha512-6WTZ/IxCY/T6BALoZHaE4ctp9xm+Z5kY/pzYaCHRFeyVhojxlrm+46y68HA6hr0TcwEssoxNiDEUJQjfPZ/RYA==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decimal.js@10.5.0:
    resolution: {integrity: sha512-8vDa8Qxvr/+d94hSh5P3IJwI5t8/c0KsMp+g8bNw9cY2icONa5aPfvKeieW1WlG0WQYwwhJ7mjui2xtiePQSXw==}

  decode-uri-component@0.2.2:
    resolution: {integrity: sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==}
    engines: {node: '>=0.10'}

  dedent@1.5.3:
    resolution: {integrity: sha512-NHQtfOOW68WD8lgypbLA5oT+Bt0xXJhiYvoR6SmmNXZfpzOGXwdKWmcwG8N7PwVVWV3eF/68nmD9BaJSsTBhyQ==}
    peerDependencies:
      babel-plugin-macros: ^3.1.0
    peerDependenciesMeta:
      babel-plugin-macros:
        optional: true

  deep-extend@0.6.0:
    resolution: {integrity: sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==}
    engines: {node: '>=4.0.0'}

  deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==}

  deepmerge@4.3.1:
    resolution: {integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==}
    engines: {node: '>=0.10.0'}

  defaults@1.0.4:
    resolution: {integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==}

  define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==}
    engines: {node: '>= 0.4'}

  define-lazy-prop@2.0.0:
    resolution: {integrity: sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==}
    engines: {node: '>=8'}

  define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==}
    engines: {node: '>= 0.4'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  denque@2.1.0:
    resolution: {integrity: sha512-HVQE3AAb/pxF8fQAoiqpvg9i3evqug3hoiwakOyZAwJm+6vZehbkYXZ0l4JxS+I3QxM97v5aaRNhj8v5oBhekw==}
    engines: {node: '>=0.10'}

  depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  detect-libc@1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==}
    engines: {node: '>=0.10'}
    hasBin: true

  detect-newline@3.1.0:
    resolution: {integrity: sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==}
    engines: {node: '>=8'}

  diff-sequences@29.6.3:
    resolution: {integrity: sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  diff@4.0.2:
    resolution: {integrity: sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==}
    engines: {node: '>=0.3.1'}

  dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}

  doctrine@2.1.0:
    resolution: {integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==}
    engines: {node: '>=0.10.0'}

  doctrine@3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==}
    engines: {node: '>=6.0.0'}

  dom-serializer@2.0.0:
    resolution: {integrity: sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==}

  domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}

  domexception@4.0.0:
    resolution: {integrity: sha512-A2is4PLG+eeSfoTMA95/s4pvAoSo2mKtiM5jlHkAVewmiO8ISFTFKZjH7UAM1Atli/OT/7JHOrJRJiMKUZKYBw==}
    engines: {node: '>=12'}
    deprecated: Use your platform's native DOMException instead

  domhandler@5.0.3:
    resolution: {integrity: sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==}
    engines: {node: '>= 4'}

  domutils@3.2.2:
    resolution: {integrity: sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==}

  dotenv-expand@11.0.7:
    resolution: {integrity: sha512-zIHwmZPRshsCdpMDyVsqGmgyP0yT8GAgXUnkdAoJisxvf33k7yO6OuoKmcTGuXPWSsm8Oh88nZicRLA9Y0rUeA==}
    engines: {node: '>=12'}

  dotenv@16.4.7:
    resolution: {integrity: sha512-47qPchRCykZC03FhkYAhrvwU4xDBFIj1QPqaarj6mdM/hgUzfPHcpkHJOn3mJAufFeeAxAzeGsr5X0M4k6fLZQ==}
    engines: {node: '>=12'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}

  ejs@3.1.10:
    resolution: {integrity: sha512-UeJmFfOrAQS8OJWPZ4qtgHyWExa088/MtK5UEyoJGFH67cDEXkZSviOiKRCZ4Xij0zxI3JECgYs3oKx+AizQBA==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  electron-to-chromium@1.5.109:
    resolution: {integrity: sha512-AidaH9JETVRr9DIPGfp1kAarm/W6hRJTPuCnkF+2MqhF4KaAgRIcBc8nvjk+YMXZhwfISof/7WG29eS4iGxQLQ==}

  emittery@0.13.1:
    resolution: {integrity: sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==}
    engines: {node: '>=12'}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}

  encodeurl@2.0.0:
    resolution: {integrity: sha512-Q0n9HRi4m6JuGIV1eFlmvJB7ZEVxu93IrMyiMsGC0lrMJMWzRgx6WGquyfQgZVb31vhGgXnfmPNNXmxnOkRBrg==}
    engines: {node: '>= 0.8'}

  enhanced-resolve@5.18.1:
    resolution: {integrity: sha512-ZSW3ma5GkcQBIpwZTSRAI8N71Uuwgs93IezB7mf7R60tC8ZbJideoDNKjHn2O9KIlx6rkGTTEk1xUCK2E1Y2Yg==}
    engines: {node: '>=10.13.0'}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  env-editor@0.4.2:
    resolution: {integrity: sha512-ObFo8v4rQJAE59M69QzwloxPZtd33TpYEIjtKD1rrFDcM1Gd7IkDxEBU+HriziN6HSHQnBJi8Dmy+JWkav5HKA==}
    engines: {node: '>=8'}

  eol@0.9.1:
    resolution: {integrity: sha512-Ds/TEoZjwggRoz/Q2O7SE3i4Jm66mqTDfmdHdq/7DKVk3bro9Q8h6WdXKdPqFLMoqxrDK5SVRzHVPOS6uuGtrg==}

  error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}

  error-stack-parser@2.1.4:
    resolution: {integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==}

  es-abstract@1.23.9:
    resolution: {integrity: sha512-py07lI0wjxAC/DcfK1S6G7iANonniZwTISvdPzk9hzeH0IZIshbuuFxLIU96OyF89Yb9hiqWn8M/bY83KY5vzA==}
    engines: {node: '>= 0.4'}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-module-lexer@1.6.0:
    resolution: {integrity: sha512-qqnD1yMU6tk/jnaMosogGySTZP8YtUgAffA9nMN+E/rjxcfRQ6IEk7IiozUjgxKoFHBGjTLnrHB/YC45r/59EQ==}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}

  es-shim-unscopables@1.1.0:
    resolution: {integrity: sha512-d9T8ucsEhh8Bi1woXCf+TIKDIROLG5WCkxg8geBCbvk22kzwC5G2OnXVMO6FUsvQlgUUXQ2itephWDLqDzbeCw==}
    engines: {node: '>= 0.4'}

  es-to-primitive@1.3.0:
    resolution: {integrity: sha512-w+5mJ3GuFL+NjVtJlvydShqE1eN3h3PbI7/5LAsYJP/2qtuMXjfL2LpHSRqo4b4eSF5K/DH1JXKUAHSB2UW50g==}
    engines: {node: '>= 0.4'}

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  escape-string-regexp@2.0.0:
    resolution: {integrity: sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==}
    engines: {node: '>=8'}

  escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==}
    engines: {node: '>=10'}

  escodegen@2.1.0:
    resolution: {integrity: sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w==}
    engines: {node: '>=6.0'}
    hasBin: true

  eslint-config-prettier@8.5.0:
    resolution: {integrity: sha512-obmWKLUNCnhtQRKc+tmnYuQl0pFU1ibYJQ5BGhTVB08bHe9wC8qUeG7c08dj9XX+AuPj1YSGSQIHl1pnDHZR0Q==}
    hasBin: true
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-config-standard@17.0.0:
    resolution: {integrity: sha512-/2ks1GKyqSOkH7JFvXJicu0iMpoojkwB+f5Du/1SC0PtBL+s8v30k9njRZ21pm2drKYm2342jFnGWzttxPmZVg==}
    peerDependencies:
      eslint: ^8.0.1
      eslint-plugin-import: ^2.25.2
      eslint-plugin-n: ^15.0.0
      eslint-plugin-promise: ^6.0.0

  eslint-import-resolver-node@0.3.9:
    resolution: {integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==}

  eslint-module-utils@2.12.0:
    resolution: {integrity: sha512-wALZ0HFoytlyh/1+4wuZ9FJCD/leWHQzzrxJ8+rebyReSLk7LApMyd3WJaLVoN+D5+WIdJyDK1c6JnE65V4Zyg==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: '*'
      eslint-import-resolver-node: '*'
      eslint-import-resolver-typescript: '*'
      eslint-import-resolver-webpack: '*'
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true

  eslint-plugin-es@4.1.0:
    resolution: {integrity: sha512-GILhQTnjYE2WorX5Jyi5i4dz5ALWxBIdQECVQavL6s7cI76IZTDWleTHkxz/QT3kvcs2QlGHvKLYsSlPOlPXnQ==}
    engines: {node: '>=8.10.0'}
    peerDependencies:
      eslint: '>=4.19.1'

  eslint-plugin-import@2.26.0:
    resolution: {integrity: sha512-hYfi3FXaM8WPLf4S1cikh/r4IxnO6zrhZbEGz2b660EJRbuxgpDS5gkCuYgGWg2xxh2rBuIr4Pvhve/7c31koA==}
    engines: {node: '>=4'}
    peerDependencies:
      '@typescript-eslint/parser': '*'
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
    peerDependenciesMeta:
      '@typescript-eslint/parser':
        optional: true

  eslint-plugin-n@15.7.0:
    resolution: {integrity: sha512-jDex9s7D/Qial8AGVIHq4W7NswpUD5DPDL2RH8Lzd9EloWUuvUkHfv4FRLMipH5q2UtyurorBkPeNi1wVWNh3Q==}
    engines: {node: '>=12.22.0'}
    peerDependencies:
      eslint: '>=7.0.0'

  eslint-plugin-promise@6.0.0:
    resolution: {integrity: sha512-7GPezalm5Bfi/E22PnQxDWH2iW9GTvAlUNTztemeHb6c1BniSyoeTrM87JkC0wYdi6aQrZX9p2qEiAno8aTcbw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0

  eslint-plugin-react-native-globals@0.1.2:
    resolution: {integrity: sha512-9aEPf1JEpiTjcFAmmyw8eiIXmcNZOqaZyHO77wgm0/dWfT/oxC1SrIq8ET38pMxHYrcB6Uew+TzUVsBeczF88g==}

  eslint-plugin-react-native@4.0.0:
    resolution: {integrity: sha512-kMmdxrSY7A1WgdqaGC+rY/28rh7kBGNBRsk48ovqkQmdg5j4K+DaFmegENDzMrdLkoufKGRNkKX6bgSwQTCAxQ==}
    peerDependencies:
      eslint: ^3.17.0 || ^4 || ^5 || ^6 || ^7 || ^8

  eslint-plugin-react@7.30.0:
    resolution: {integrity: sha512-RgwH7hjW48BleKsYyHK5vUAvxtE9SMPDKmcPRQgtRCYaZA0XQPt5FSkrU3nhz5ifzMZcA8opwmRJ2cmOO8tr5A==}
    engines: {node: '>=4'}
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8

  eslint-plugin-reactotron@0.1.6:
    resolution: {integrity: sha512-zDm+vmkRwZZx2gUoA4fS5FMbJcekf78tOULKuBWNLMPyMjgCIreQiG/hBSXUSFh1pvAFOJHO/QpHxUW82gaAUQ==}
    peerDependencies:
      reactotron-core-client: '*'

  eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}

  eslint-scope@7.2.2:
    resolution: {integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint-utils@2.1.0:
    resolution: {integrity: sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==}
    engines: {node: '>=6'}

  eslint-utils@3.0.0:
    resolution: {integrity: sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==}
    engines: {node: ^10.0.0 || ^12.0.0 || >= 14.0.0}
    peerDependencies:
      eslint: '>=5'

  eslint-visitor-keys@1.3.0:
    resolution: {integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==}
    engines: {node: '>=4'}

  eslint-visitor-keys@2.1.0:
    resolution: {integrity: sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==}
    engines: {node: '>=10'}

  eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  eslint@8.17.0:
    resolution: {integrity: sha512-gq0m0BTJfci60Fz4nczYxNAlED+sMcihltndR8t9t1evnU/azx53x3t2UHXC/uRjcbvRw/XctpaNygSTcQD+Iw==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true

  espree@9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==}
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}

  esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  esquery@1.6.0:
    resolution: {integrity: sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg==}
    engines: {node: '>=0.10'}

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}

  event-target-shim@5.0.1:
    resolution: {integrity: sha512-i/2XbnSz/uxRCU6+NdVJgKWDTM427+MqYbkQzD321DuCQJUqOuJKIA0IM2+W2xtYHdKOmZ4dR6fExsd4SXL+WQ==}
    engines: {node: '>=6'}

  events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  exec-async@2.2.0:
    resolution: {integrity: sha512-87OpwcEiMia/DeiKFzaQNBNFeN3XkkpYIh9FyOqq5mS2oKv3CBE67PXoEKcr6nodWdXNogTiQ0jE2NGuoffXPw==}

  execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}

  exit@0.1.2:
    resolution: {integrity: sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==}
    engines: {node: '>= 0.8.0'}

  expect@29.7.0:
    resolution: {integrity: sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  expo-application@6.1.4:
    resolution: {integrity: sha512-jXVZb3llTQ5j4C/I03GxKjujmhKex9Xo5JDZo/pRjScHSr4NoeMjPKWThyWVlWDM1v5YSEcsRJebVfTvq9SR5Q==}
    peerDependencies:
      expo: '*'

  expo-asset@11.1.5:
    resolution: {integrity: sha512-GEQDCqC25uDBoXHEnXeBuwpeXvI+3fRGvtzwwt0ZKKzWaN+********************************+yCCLUQ==}
    peerDependencies:
      expo: '*'
      react: '*'
      react-native: '*'

  expo-auth-session@6.1.5:
    resolution: {integrity: sha512-KvXWpufwIjze5W9TdX4CqCLm4SAtjBEHvMPE/7CiX6ThB583ckpoo1mgzKGNyrKl7p7eKcdZmHcmXJYldXugkA==}
    peerDependencies:
      react: '*'
      react-native: '*'

  expo-build-properties@0.14.6:
    resolution: {integrity: sha512-46+gcnFxb2Dz2TFEhFlEJ11qT85THlPtFgkRKQ3a11S3+stgDzDBC2WwbXS5/GMINLIDdBFbbZlajgVND0tMnQ==}
    peerDependencies:
      expo: '*'

  expo-constants@17.1.6:
    resolution: {integrity: sha512-q5mLvJiLtPcaZ7t2diSOlQ2AyxIO8YMVEJsEfI/ExkGj15JrflNQ7CALEW6IF/uNae/76qI/XcjEuuAyjdaCNw==}
    peerDependencies:
      expo: '*'
      react-native: '*'

  expo-crypto@14.1.4:
    resolution: {integrity: sha512-RmKhB3FgvKE5aNFBw4+hifOkyE0tywsDQVksdHA3jFRzcU9toFiJAz6nhPsBKDf5JlzJiIXhbNMtydoWtuuE7w==}
    peerDependencies:
      expo: '*'

  expo-file-system@18.1.10:
    resolution: {integrity: sha512-SyaWg+HitScLuyEeSG9gMSDT0hIxbM9jiZjSBP9l9zMnwZjmQwsusE6+7qGiddxJzdOhTP4YGUfvEzeeS0YL3Q==}
    peerDependencies:
      expo: '*'
      react-native: '*'

  expo-font@13.3.1:
    resolution: {integrity: sha512-d+xrHYvSM9WB42wj8vP9OOFWyxed5R1evphfDb6zYBmC1dA9Hf89FpT7TNFtj2Bk3clTnpmVqQTCYbbA2P3CLg==}
    peerDependencies:
      expo: '*'
      react: '*'

  expo-keep-awake@14.1.4:
    resolution: {integrity: sha512-wU9qOnosy4+U4z/o4h8W9PjPvcFMfZXrlUoKTMBW7F4pLqhkkP/5G4EviPZixv4XWFMjn1ExQ5rV6BX8GwJsWA==}
    peerDependencies:
      expo: '*'
      react: '*'

  expo-linking@7.1.5:
    resolution: {integrity: sha512-8g20zOpROW78bF+bLI4a3ZWj4ntLgM0rCewKycPL0jk9WGvBrBtFtwwADJgOiV1EurNp3lcquerXGlWS+SOQyA==}
    peerDependencies:
      react: '*'
      react-native: '*'

  expo-localization@16.1.5:
    resolution: {integrity: sha512-dymvf0S11afyMeRbnoXd2iWWzFYwg21jHTnLBO/7ObNO1rKlYpus0ghVDnh+sJFV2u7s518e/JTcAqNR69EZkw==}
    peerDependencies:
      expo: '*'
      react: '*'

  expo-modules-autolinking@2.1.10:
    resolution: {integrity: sha512-k93fzoszrYTKbZ51DSVnewYIGUV6Gi22Su8qySXPFJEfvtDs2NUUNRHBZNKgLHvwc6xPzVC5j7JYbrpXNuY44A==}
    hasBin: true

  expo-modules-core@2.3.13:
    resolution: {integrity: sha512-vmKHv7tEo2wUQoYDV6grhsLsQfD3DUnew5Up3yNnOE1gHGQE+zhV1SBYqaPMPB12OvpyD1mlfzGhu6r9PODnng==}

  expo-secure-store@14.2.3:
    resolution: {integrity: sha512-hYBbaAD70asKTFd/eZBKVu+9RTo9OSTMMLqXtzDF8ndUGjpc6tmRCoZtrMHlUo7qLtwL5jm+vpYVBWI8hxh/1Q==}
    peerDependencies:
      expo: '*'

  expo-splash-screen@0.30.8:
    resolution: {integrity: sha512-2eh+uA543brfeG5HILXmtNKA7E2/pfywKzNumzy3Ef6OtDjYy6zJUGNSbhnZRbVEjUZo3/QNRs0JRBfY80okZg==}
    peerDependencies:
      expo: '*'

  expo-status-bar@2.2.3:
    resolution: {integrity: sha512-+c8R3AESBoduunxTJ8353SqKAKpxL6DvcD8VKBuh81zzJyUUbfB4CVjr1GufSJEKsMzNPXZU+HJwXx7Xh7lx8Q==}
    peerDependencies:
      react: '*'
      react-native: '*'

  expo-web-browser@14.1.6:
    resolution: {integrity: sha512-/4P8eWqRyfXIMZna3acg320LXNA+P2cwyEVbjDX8vHnWU+UnOtyRKWy3XaAIyMPQ9hVjBNUQTh4MPvtnPRzakw==}
    peerDependencies:
      expo: '*'
      react-native: '*'

  expo@53.0.9:
    resolution: {integrity: sha512-UFG68aVOpccg3s++S3pbtI3YCQCnlu/TFvhnQ5vaD3vhOox1Uk/f2O2T95jmwA/EvKvetqGj34lys3DNXvPqgQ==}
    hasBin: true
    peerDependencies:
      '@expo/dom-webview': '*'
      '@expo/metro-runtime': '*'
      react: '*'
      react-native: '*'
      react-native-webview: '*'
    peerDependenciesMeta:
      '@expo/dom-webview':
        optional: true
      '@expo/metro-runtime':
        optional: true
      react-native-webview:
        optional: true

  exponential-backoff@3.1.2:
    resolution: {integrity: sha512-8QxYTVXUkuy7fIIoitQkPwGonB8F3Zj8eEO8Sqg9Zv/bkI7RJAzowee4gr81Hak/dUTpA2Z7VfQgoijjPNlUZA==}

  fast-base64-decode@1.0.0:
    resolution: {integrity: sha512-qwaScUgUGBYeDNRnbc/KyllVU88Jk1pRHPStuF/lO7B0/RTRLj7U0lkdTAutlBblY08rwZDff6tNU9cjv6j//Q==}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-fuzzy@1.12.0:
    resolution: {integrity: sha512-sXxGgHS+ubYpsdLnvOvJ9w5GYYZrtL9mkosG3nfuD446ahvoWEsSKBP7ieGmWIKVLnaxRDgUJkZMdxRgA2Ni+Q==}

  fast-glob@3.3.3:
    resolution: {integrity: sha512-7MptL8U0cqcFdzIzwOTHoilX9x5BrNqye7Z/LuC7kCMRio1EMSyqRK3BEAUD7sXRq4iT4AzTVuZdhgQ2TCvYLg==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==}

  fast-uri@3.0.6:
    resolution: {integrity: sha512-Atfo14OibSv5wAp4VWNsFYE1AchQRTv9cBGWET4pZWHzYshFSS9NQI6I57rdKn9croWVMbYFbLhJ+yJvmZIIHw==}

  fast-xml-parser@4.4.1:
    resolution: {integrity: sha512-xkjOecfnKGkSsOwtZ5Pz7Us/T6mrbPQrq0nh+aCO5V9nk5NLWmasAHumTKjiPJPWANe+kAZ84Jc8ooJkzZ88Sw==}
    hasBin: true

  fast-xml-parser@4.5.3:
    resolution: {integrity: sha512-RKihhV+SHsIUGXObeVy9AXiBbFwkVk7Syp8XgwN5U3JV416+Gwp/GO9i0JYKmikykgz/UHRrrV4ROuZEo/T0ig==}
    hasBin: true

  fastq@1.19.1:
    resolution: {integrity: sha512-GwLTyxkCXjXbxqIhTsMI2Nui8huMPtnxg7krajPJAjnEG/iiOS7i+zCtWGZR9G0NBKbXKh6X9m9UIsYX/N6vvQ==}

  fb-watchman@2.0.2:
    resolution: {integrity: sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==}

  fbjs-css-vars@1.0.2:
    resolution: {integrity: sha512-b2XGFAFdWZWg0phtAWLHCk836A1Xann+I+Dgd3Gk64MHKZO44FfoD1KxyvbSh0qZsIoXQGGlVztIY+oitJPpRQ==}

  fbjs@3.0.5:
    resolution: {integrity: sha512-ztsSx77JBtkuMrEypfhgc3cI0+0h+svqeie7xHbh1k/IKdcydnvadp/mUaGgjAOXQmQSxsqgaRhS3q9fy+1kxg==}

  file-entry-cache@6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==}
    engines: {node: ^10.12.0 || >=12.0.0}

  filelist@1.0.4:
    resolution: {integrity: sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q==}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  filter-obj@1.1.0:
    resolution: {integrity: sha512-8rXg1ZnX7xzy2NGDVkBVaAy+lSlPNwad13BtgSlLuxfIslyt5Vg64U7tFcCt4WS1R0hvtnQybT/IyCkGZ3DpXQ==}
    engines: {node: '>=0.10.0'}

  finalhandler@1.1.2:
    resolution: {integrity: sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==}
    engines: {node: '>= 0.8'}

  find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}

  find-up@5.0.0:
    resolution: {integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==}
    engines: {node: '>=10'}

  find-yarn-workspace-root@2.0.0:
    resolution: {integrity: sha512-1IMnbjt4KzsQfnhnzNd8wUEgXZ44IzZaZmnLYx7D5FZlaHt2gW20Cri8Q+E/t5tIj4+epTBub+2Zxu/vNILzqQ==}

  flat-cache@3.2.0:
    resolution: {integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==}
    engines: {node: ^10.12.0 || >=12.0.0}

  flatted@3.3.3:
    resolution: {integrity: sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==}

  flow-enums-runtime@0.0.6:
    resolution: {integrity: sha512-3PYnM29RFXwvAN6Pc/scUfkI7RwhQ/xqyLUyPNlXUp9S40zI8nup9tUSrTLSVnWGBN38FNiGWbwZOB6uR4OGdw==}

  follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  fontfaceobserver@2.3.0:
    resolution: {integrity: sha512-6FPvD/IVyT4ZlNe7Wcn5Fb/4ChigpucKYSvD6a+0iMoLn2inpo711eyIcKjmDtE5XNcgAkSH9uN/nfAeZzHEfg==}

  for-each@0.3.5:
    resolution: {integrity: sha512-dKx12eRCVIzqCxFGplyFKJMPvLEWgmNtUrpTiJIR5u97zEhRG8ySrtboPHZXx7daLxQVrl643cTzbab2tkQjxg==}
    engines: {node: '>= 0.4'}

  foreground-child@3.3.1:
    resolution: {integrity: sha512-gIXjKqtFuWEgzFRJA9WCQeSJLZDjgJUOMCMzxtvFq/37KojM1BFGufqsCy0r4qSQmYLsZYMeyRqzIWOMup03sw==}
    engines: {node: '>=14'}

  form-data@4.0.2:
    resolution: {integrity: sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==}
    engines: {node: '>= 6'}

  freeport-async@2.0.0:
    resolution: {integrity: sha512-K7od3Uw45AJg00XUmy15+Hae2hOcgKcmN3/EF6Y7i01O0gaqiRx8sUSpsb9+BRNL8RPBrhzPsVfy8q9ADlJuWQ==}
    engines: {node: '>=8'}

  fresh@0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}

  fs-extra@9.1.0:
    resolution: {integrity: sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==}
    engines: {node: '>=10'}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  function.prototype.name@1.1.8:
    resolution: {integrity: sha512-e5iwyodOHhbMr/yNrc7fDYG4qlbIvI5gajyzPnb5TCwyhjApznQh1BMFou9b30SevY43gCJKXycoCBjMbsuW0Q==}
    engines: {node: '>= 0.4'}

  functional-red-black-tree@1.0.1:
    resolution: {integrity: sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==}

  functions-have-names@1.2.3:
    resolution: {integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==}

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-package-type@0.1.0:
    resolution: {integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==}
    engines: {node: '>=8.0.0'}

  get-port@3.2.0:
    resolution: {integrity: sha512-x5UJKlgeUiNT8nyo/AcnwLnZuZNcSjSw0kogRB+Whd1fjjFq4B1hySFxSFWWSn4mIBzg3sRNUDFYc4g5gjPoLg==}
    engines: {node: '>=4'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}

  get-symbol-description@1.1.0:
    resolution: {integrity: sha512-w9UMqWwJxHNOvoNzSJ2oPF5wvYcvP7jUvYzhp67yEhTi17ZDBBC1z9pTdGuzjD+EFIqLSYRweZjqfiPzQ06Ebg==}
    engines: {node: '>= 0.4'}

  getenv@1.0.0:
    resolution: {integrity: sha512-7yetJWqbS9sbn0vIfliPsFgoXMKn/YMF+Wuiog97x+urnSRRRZ7xB+uVkwGKzRgq9CDFfMQnE9ruL5DHv9c6Xg==}
    engines: {node: '>=6'}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}

  glob@10.4.5:
    resolution: {integrity: sha512-7Bv8RF0k6xjo7d4A/PxYLbUCfb6c+Vpd2/mB2yRDlew7Jb5hEXiCD9ibfO7wpk8i4sevK6DFny9h7EYbM3/sHg==}
    hasBin: true

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globals@13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==}
    engines: {node: '>=8'}

  globalthis@1.0.4:
    resolution: {integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==}
    engines: {node: '>= 0.4'}

  globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==}
    engines: {node: '>=10'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  graphemer@1.4.0:
    resolution: {integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==}

  graphemesplit@2.6.0:
    resolution: {integrity: sha512-rG9w2wAfkpg0DILa1pjnjNfucng3usON360shisqIMUBw/87pojcBSrHmeE4UwryAuBih7g8m1oilf5/u8EWdQ==}

  graphql@15.10.1:
    resolution: {integrity: sha512-BL/Xd/T9baO6NFzoMpiMD7YUZ62R6viR5tp/MULVEnbYJXZA//kRNW7J0j1w/wXArgL0sCxhDfK5dczSKn3+cg==}
    engines: {node: '>= 10.x'}

  graphql@15.8.0:
    resolution: {integrity: sha512-5gghUc24tP9HRznNpV2+FIoq3xKkj5dTQqf4v0CpdPbFVwFkWoxOM+o+2OC9ZSvjEMTjfmG9QT+gcvggTwW1zw==}
    engines: {node: '>= 10.x'}

  has-bigints@1.1.0:
    resolution: {integrity: sha512-R3pbpkcIqv2Pm3dUwgjclDRVmWpTJW2DcMzcIhEXEx1oh/CEMObMm3KLmRJOdvhM7o4uQBnwr8pzRK2sJWIqfg==}
    engines: {node: '>= 0.4'}

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==}

  has-proto@1.2.0:
    resolution: {integrity: sha512-KIL7eQPfHQRC8+XluaIw7BHUwwqL19bQn4hzNgdr+1wXoU0KKj6rufu47lhY7KbJR2C6T6+PfyN0Ea7wkSS+qQ==}
    engines: {node: '>= 0.4'}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  has@1.0.4:
    resolution: {integrity: sha512-qdSAmqLF6209RFj4VVItywPMbm3vWylknmB3nvNiUIs72xAimcM8nVYxYr7ncvZq5qzk9MKIZR8ijqD/1QuYjQ==}
    engines: {node: '>= 0.4.0'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  hermes-estree@0.25.1:
    resolution: {integrity: sha512-0wUoCcLp+5Ev5pDW2OriHC2MJCbwLwuRx+gAqMTOkGKJJiBCLjtrvy4PWUGn6MIVefecRpzoOZ/UV6iGdOr+Cw==}

  hermes-estree@0.28.1:
    resolution: {integrity: sha512-w3nxl/RGM7LBae0v8LH2o36+8VqwOZGv9rX1wyoWT6YaKZLqpJZ0YQ5P0LVr3tuRpf7vCx0iIG4i/VmBJejxTQ==}

  hermes-parser@0.25.1:
    resolution: {integrity: sha512-6pEjquH3rqaI6cYAXYPcz9MS4rY6R4ngRgrgfDshRptUZIc3lw0MCIJIGDj9++mfySOuPTHB4nrSW99BCvOPIA==}

  hermes-parser@0.28.1:
    resolution: {integrity: sha512-nf8o+hE8g7UJWParnccljHumE9Vlq8F7MqIdeahl+4x0tvCUJYRrT0L7h0MMg/X9YJmkNwsfbaNNrzPtFXOscg==}

  hoist-non-react-statics@3.3.2:
    resolution: {integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==}

  hosted-git-info@7.0.2:
    resolution: {integrity: sha512-puUZAUKT5m8Zzvs72XWy3HtvVbTWljRE66cP60bxJzAqf2DgICo7lYTY2IHUmLnNpjYvw5bvmoHvPc0QO2a62w==}
    engines: {node: ^16.14.0 || >=18.0.0}

  html-encoding-sniffer@3.0.0:
    resolution: {integrity: sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==}
    engines: {node: '>=12'}

  html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}

  http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}

  http-proxy-agent@5.0.0:
    resolution: {integrity: sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==}
    engines: {node: '>= 6'}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}

  https-proxy-agent@7.0.6:
    resolution: {integrity: sha512-vK9P5/iUfdl95AI+JVyUuIcVtd4ofvtrOr3HNtM2yxC9bnMbEdp3x01OhQNnjb8IJYi38VlTE3mBXwcfvywuSw==}
    engines: {node: '>= 14'}

  human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}

  humps@2.0.1:
    resolution: {integrity: sha512-E0eIbrFWUhwfXJmsbdjRQFQPrl5pTEoKlz163j1mTqqUnU9PgR4AgB8AIITzuB3vLBdxZXyZ9TDIrwB2OASz4g==}

  hyphenate-style-name@1.1.0:
    resolution: {integrity: sha512-WDC/ui2VVRrz3jOVi+XtjqkDjiVjTtFaAGiW37k6b+ohyQ5wYDOGkvCZa8+H0nx3gyvv0+BST9xuOgIyGQ00gw==}

  i18n-js@4.5.1:
    resolution: {integrity: sha512-n7jojFj1WC0tztgr0I8jqTXuIlY1xNzXnC3mjKX/YjJhimdM+jXM8vOmn9d3xQFNC6qDHJ4ovhdrGXrRXLIGkA==}

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  idb@5.0.6:
    resolution: {integrity: sha512-/PFvOWPzRcEPmlDt5jEvzVZVs0wyd/EvGvkDIcbBpGuMMLQKrTPG0TxvE2UJtgZtCQCmOtM2QD7yQJBVEjKGOw==}

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  ignore@5.3.2:
    resolution: {integrity: sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g==}
    engines: {node: '>= 4'}

  image-size@1.2.0:
    resolution: {integrity: sha512-4S8fwbO6w3GeCVN6OPtA9I5IGKkcDMPcKndtUlpJuCwu7JLjtj7JZpwqLuyY2nrmQT3AWsCJLSKPsc2mPBSl3w==}
    engines: {node: '>=16.x'}
    hasBin: true

  immer@9.0.6:
    resolution: {integrity: sha512-G95ivKpy+EvVAnAab4fVa4YGYn24J1SpEktnJX7JJ45Bd7xqME/SCplFzYFmTbrkwZbQ4xJK1xMTUYBkN6pWsQ==}

  import-fresh@2.0.0:
    resolution: {integrity: sha512-eZ5H8rcgYazHbKC3PG4ClHNykCSxtAhxSSEM+2mb+7evD2CKF5V7c0dNum7AdpDh0ZdICwZY9sRSn8f+KH96sg==}
    engines: {node: '>=4'}

  import-fresh@3.3.1:
    resolution: {integrity: sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ==}
    engines: {node: '>=6'}

  import-local@3.2.0:
    resolution: {integrity: sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==}
    engines: {node: '>=8'}
    hasBin: true

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}
    deprecated: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  ini@1.3.8:
    resolution: {integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==}

  inline-style-prefixer@7.0.1:
    resolution: {integrity: sha512-lhYo5qNTQp3EvSSp3sRvXMbVQTLrvGV6DycRMJ5dm2BLMiJ30wpXKdDdgX+GmJZ5uQMucwRKHamXSst3Sj/Giw==}

  internal-slot@1.1.0:
    resolution: {integrity: sha512-4gd7VpWNQNB4UKKCFFVcp1AVv+FMOgs9NKzjHKusc8jTMhd5eL1NqQqOpE0KzMds804/yHlglp3uxgluOqAPLw==}
    engines: {node: '>= 0.4'}

  internmap@2.0.3:
    resolution: {integrity: sha512-5Hh7Y1wQbvY5ooGgPbDaL5iYLAPzMTUrjMulskHLH6wnv/A+1q5rgEaiuqEjB+oxGXIVZs1FF+R/KPN3ZSQYYg==}
    engines: {node: '>=12'}

  invariant@2.2.4:
    resolution: {integrity: sha512-phJfQVBuaJM5raOpJjSfkiD6BpbCE4Ns//LaXl6wGYtUBY83nWS6Rf9tXm2e8VaK60JEjYldbPif/A2B1C2gNA==}

  is-array-buffer@3.0.5:
    resolution: {integrity: sha512-DDfANUiiG2wC1qawP66qlTugJeL5HyzMpfr8lLK+jMQirGzNod0B12cFB/9q838Ru27sBwfw78/rdoU7RERz6A==}
    engines: {node: '>= 0.4'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  is-arrayish@0.3.2:
    resolution: {integrity: sha512-eVRqCvVlZbuw3GrM63ovNSNAeA1K16kaR/LRY/92w0zxQ5/1YzwblUX652i4Xs9RwAGjW9d9y6X88t8OaAJfWQ==}

  is-async-function@2.1.1:
    resolution: {integrity: sha512-9dgM/cZBnNvjzaMYHVoxxfPj2QXt22Ev7SuuPrs+xav0ukGB0S6d4ydZdEiM48kLx5kDV+QBPrpVnFyefL8kkQ==}
    engines: {node: '>= 0.4'}

  is-bigint@1.1.0:
    resolution: {integrity: sha512-n4ZT37wG78iz03xPRKJrHTdZbe3IicyucEtdRsV5yglwc3GyUfbAfpSeD0FJ41NbUNSt5wbhqfp1fS+BgnvDFQ==}
    engines: {node: '>= 0.4'}

  is-boolean-object@1.2.2:
    resolution: {integrity: sha512-wa56o2/ElJMYqjCjGkXri7it5FbebW5usLw/nPmCMs5DeZ7eziSYZhSmPRn0txqeW4LnAmQQU7FgqLpsEFKM4A==}
    engines: {node: '>= 0.4'}

  is-callable@1.2.7:
    resolution: {integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==}
    engines: {node: '>= 0.4'}

  is-core-module@2.16.1:
    resolution: {integrity: sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==}
    engines: {node: '>= 0.4'}

  is-data-view@1.0.2:
    resolution: {integrity: sha512-RKtWF8pGmS87i2D6gqQu/l7EYRlVdfzemCJN/P3UOs//x1QE7mfhvzHIApBTRf7axvT6DMGwSwBXYCT0nfB9xw==}
    engines: {node: '>= 0.4'}

  is-date-object@1.1.0:
    resolution: {integrity: sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==}
    engines: {node: '>= 0.4'}

  is-directory@0.3.1:
    resolution: {integrity: sha512-yVChGzahRFvbkscn2MlwGismPO12i9+znNruC5gVEntG3qu0xQMzsGg/JFbrsqDOHtHFPci+V5aP5T9I+yeKqw==}
    engines: {node: '>=0.10.0'}

  is-docker@2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-finalizationregistry@1.1.1:
    resolution: {integrity: sha512-1pC6N8qWJbWoPtEjgcL2xyhQOP491EQjeUo3qTKcmV8YSDDJrOepfG8pcC7h/QgnQHYSv0mJ3Z/ZWxmatVrysg==}
    engines: {node: '>= 0.4'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-generator-fn@2.1.0:
    resolution: {integrity: sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==}
    engines: {node: '>=6'}

  is-generator-function@1.1.0:
    resolution: {integrity: sha512-nPUB5km40q9e8UfN/Zc24eLlzdSf9OfKByBw9CIdw4H1giPMeA0OIJvbchsCu4npfI2QcMVBsGEBHKZ7wLTWmQ==}
    engines: {node: '>= 0.4'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-map@2.0.3:
    resolution: {integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==}
    engines: {node: '>= 0.4'}

  is-number-object@1.1.1:
    resolution: {integrity: sha512-lZhclumE1G6VYD8VHe35wFaIif+CTy5SJIi5+3y4psDgWu4wPDoBhF8NxUOinEc7pHgiTsT6MaBb92rKhhD+Xw==}
    engines: {node: '>= 0.4'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-plain-obj@2.1.0:
    resolution: {integrity: sha512-YWnfyRwxL/+SsrWYfOpUtz5b3YD+nyfkHvjbcanzk8zgyO4ASD67uVMRt8k5bM4lLMDnXfriRhOpemw+NfT1eA==}
    engines: {node: '>=8'}

  is-potential-custom-element-name@1.0.1:
    resolution: {integrity: sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==}

  is-regex@1.2.1:
    resolution: {integrity: sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==}
    engines: {node: '>= 0.4'}

  is-set@2.0.3:
    resolution: {integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==}
    engines: {node: '>= 0.4'}

  is-shared-array-buffer@1.0.4:
    resolution: {integrity: sha512-ISWac8drv4ZGfwKl5slpHG9OwPNty4jOWPRIhBpxOoD+hqITiwuipOQ2bNthAzwA3B4fIjO4Nln74N0S9byq8A==}
    engines: {node: '>= 0.4'}

  is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}

  is-string@1.1.1:
    resolution: {integrity: sha512-BtEeSsoaQjlSPBemMQIrY1MY0uM6vnS1g5fmufYOtnxLGUZM2178PKbhsk7Ffv58IX+ZtcvoGwccYsh0PglkAA==}
    engines: {node: '>= 0.4'}

  is-symbol@1.1.1:
    resolution: {integrity: sha512-9gGx6GTtCQM73BgmHQXfDmLtfjjTUDSyoxTCbp5WtoixAhfgsDirWIcVQ/IHpvI5Vgd5i/J5F7B9cN/WlVbC/w==}
    engines: {node: '>= 0.4'}

  is-typed-array@1.1.15:
    resolution: {integrity: sha512-p3EcsicXjit7SaskXHs1hA91QxgTw46Fv6EFKKGS5DRFLD8yKnohjF3hxoju94b/OcMZoQukzpPpBE9uLVKzgQ==}
    engines: {node: '>= 0.4'}

  is-weakmap@2.0.2:
    resolution: {integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==}
    engines: {node: '>= 0.4'}

  is-weakref@1.1.1:
    resolution: {integrity: sha512-6i9mGWSlqzNMEqpCp93KwRS1uUOodk2OJ6b+sq7ZPDSy2WuI5NFIxp/254TytR8ftefexkWn5xNiHUNpPOfSew==}
    engines: {node: '>= 0.4'}

  is-weakset@2.0.4:
    resolution: {integrity: sha512-mfcwb6IzQyOKTs84CQMrOwW4gQcaTOAWJ0zzJCl2WSPDrWk/OzDaImWFH3djXhb24g4eudZfLRozAvPGw4d9hQ==}
    engines: {node: '>= 0.4'}

  is-wsl@2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==}
    engines: {node: '>=8'}

  isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  isarray@2.0.5:
    resolution: {integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==}
    engines: {node: '>=8'}

  istanbul-lib-instrument@5.2.1:
    resolution: {integrity: sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==}
    engines: {node: '>=8'}

  istanbul-lib-instrument@6.0.3:
    resolution: {integrity: sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==}
    engines: {node: '>=10'}

  istanbul-lib-report@3.0.1:
    resolution: {integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==}
    engines: {node: '>=10'}

  istanbul-lib-source-maps@4.0.1:
    resolution: {integrity: sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==}
    engines: {node: '>=10'}

  istanbul-reports@3.1.7:
    resolution: {integrity: sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==}
    engines: {node: '>=8'}

  its-fine@1.2.5:
    resolution: {integrity: sha512-fXtDA0X0t0eBYAGLVM5YsgJGsJ5jEmqZEPrGbzdf5awjv0xE7nqv3TVnvtUF060Tkes15DbDAKW/I48vsb6SyA==}
    peerDependencies:
      react: '>=18.0'

  jackspeak@3.4.3:
    resolution: {integrity: sha512-OGlZQpz2yfahA/Rd1Y8Cd9SIEsqvXkLVoSw/cgwhnhFMDbsQFeZYoJJ7bIZBS9BcamUW96asq/npPWugM+RQBw==}

  jake@10.9.2:
    resolution: {integrity: sha512-2P4SQ0HrLQ+fw6llpLnOaGAvN2Zu6778SJMrCUwns4fOoG9ayrTiZk3VV8sCPkVZF8ab0zksVpS8FDY5pRCNBA==}
    engines: {node: '>=10'}
    hasBin: true

  jest-changed-files@29.7.0:
    resolution: {integrity: sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-circus@29.7.0:
    resolution: {integrity: sha512-3E1nCMgipcTkCocFwM90XXQab9bS+GMsjdpmPrlelaxwD93Ad8iVEjX/vvHPdLPnFf+L40u+5+iutRdA1N9myw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-cli@29.7.0:
    resolution: {integrity: sha512-OVVobw2IubN/GSYsxETi+gOe7Ka59EFMR/twOU3Jb2GnKKeMGJB5SGUUrEz3SFVmJASUdZUzy83sLNNQ2gZslg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  jest-config@29.7.0:
    resolution: {integrity: sha512-uXbpfeQ7R6TZBqI3/TxCU4q4ttk3u0PJeC+E0zbfSoSjq6bJ7buBPxzQPL0ifrkY4DNu4JUdk0ImlBUYi840eQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      '@types/node': '*'
      ts-node: '>=9.0.0'
    peerDependenciesMeta:
      '@types/node':
        optional: true
      ts-node:
        optional: true

  jest-diff@29.7.0:
    resolution: {integrity: sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-docblock@29.7.0:
    resolution: {integrity: sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-each@29.7.0:
    resolution: {integrity: sha512-gns+Er14+ZrEoC5fhOfYCY1LOHHr0TI+rQUHZS8Ttw2l7gl+80eHc/gFf2Ktkw0+SIACDTeWvpFcv3B04VembQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-environment-jsdom@29.7.0:
    resolution: {integrity: sha512-k9iQbsf9OyOfdzWH8HDmrRT0gSIcX+FLNW7IQq94tFX0gynPwqDTW0Ho6iMVNjGz/nb+l/vW3dWM2bbLLpkbXA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jest-environment-node@29.7.0:
    resolution: {integrity: sha512-DOSwCRqXirTOyheM+4d5YZOrWcdu0LNZ87ewUoywbcb2XR4wKgqiG8vNeYwhjFMbEkfju7wx2GYH0P2gevGvFw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-expo@53.0.5:
    resolution: {integrity: sha512-kRQbgU5SJvx27seV20i+PXjkqOladWnrCi7gFsfGV217lSHm+7ZN8jm7o8DAuTJ/AtuudsxZh3+xUgbsxiKTvg==}
    hasBin: true
    peerDependencies:
      expo: '*'
      react-native: '*'

  jest-get-type@29.6.3:
    resolution: {integrity: sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-haste-map@29.7.0:
    resolution: {integrity: sha512-fP8u2pyfqx0K1rGn1R9pyE0/KTn+G7PxktWidOBTqFPLYX0b9ksaMFkhK5vrS3DVun09pckLdlx90QthlW7AmA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-leak-detector@29.7.0:
    resolution: {integrity: sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-matcher-utils@29.7.0:
    resolution: {integrity: sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-message-util@29.7.0:
    resolution: {integrity: sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-mock@29.7.0:
    resolution: {integrity: sha512-ITOMZn+UkYS4ZFh83xYAOzWStloNzJFO2s8DWrE4lhtGD+AorgnbkiKERe4wQVBydIGPx059g6riW5Btp6Llnw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-pnp-resolver@1.2.3:
    resolution: {integrity: sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==}
    engines: {node: '>=6'}
    peerDependencies:
      jest-resolve: '*'
    peerDependenciesMeta:
      jest-resolve:
        optional: true

  jest-regex-util@29.6.3:
    resolution: {integrity: sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-resolve-dependencies@29.7.0:
    resolution: {integrity: sha512-un0zD/6qxJ+S0et7WxeI3H5XSe9lTBBR7bOHCHXkKR6luG5mwDDlIzVQ0V5cZCuoTgEdcdwzTghYkTWfubi+nA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-resolve@29.7.0:
    resolution: {integrity: sha512-IOVhZSrg+UvVAshDSDtHyFCCBUl/Q3AAJv8iZ6ZjnZ74xzvwuzLXid9IIIPgTnY62SJjfuupMKZsZQRsCvxEgA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-runner@29.7.0:
    resolution: {integrity: sha512-fsc4N6cPCAahybGBfTRcq5wFR6fpLznMg47sY5aDpsoejOcVYFb07AHuSnR0liMcPTgBsA3ZJL6kFOjPdoNipQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-runtime@29.7.0:
    resolution: {integrity: sha512-gUnLjgwdGqW7B4LvOIkbKs9WGbn+QLqRQQ9juC6HndeDiezIwhDP+mhMwHWCEcfQ5RUXa6OPnFF8BJh5xegwwQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-snapshot@29.7.0:
    resolution: {integrity: sha512-Rm0BMWtxBcioHr1/OX5YCP8Uov4riHvKPknOGs804Zg9JGZgmIBkbtlxJC/7Z4msKYVbIJtfU+tKb8xlYNfdkw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-util@29.7.0:
    resolution: {integrity: sha512-z6EbKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-validate@29.7.0:
    resolution: {integrity: sha512-ZB7wHqaRGVw/9hST/OuFUReG7M8vKeq0/J2egIGLdvjHCmYqGARhzXmtgi+gVeZ5uXFF219aOc3Ls2yLg27tkw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-watch-select-projects@2.0.0:
    resolution: {integrity: sha512-j00nW4dXc2NiCW6znXgFLF9g8PJ0zP25cpQ1xRro/HU2GBfZQFZD0SoXnAlaoKkIY4MlfTMkKGbNXFpvCdjl1w==}

  jest-watch-typeahead@2.2.1:
    resolution: {integrity: sha512-jYpYmUnTzysmVnwq49TAxlmtOAwp8QIqvZyoofQFn8fiWhEDZj33ZXzg3JA4nGnzWFm1hbWf3ADpteUokvXgFA==}
    engines: {node: ^14.17.0 || ^16.10.0 || >=18.0.0}
    peerDependencies:
      jest: ^27.0.0 || ^28.0.0 || ^29.0.0

  jest-watcher@29.7.0:
    resolution: {integrity: sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest-worker@27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}

  jest-worker@29.7.0:
    resolution: {integrity: sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  jest@29.7.0:
    resolution: {integrity: sha512-NIy3oAFp9shda19hy4HK0HRTWKtPJmGdnvywu01nOqNC2vZg+Z+fvJDxpMQA88eb2I9EcafcdjYgsDthnYTvGw==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}
    hasBin: true
    peerDependencies:
      node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
    peerDependenciesMeta:
      node-notifier:
        optional: true

  jimp-compact@0.16.1:
    resolution: {integrity: sha512-dZ6Ra7u1G8c4Letq/B5EzAxj4tLFHL+cGtdpR+PVm4yzPDj+lCk+AbivWt1eOM+ikzkowtyV7qSqX6qr3t71Ww==}

  js-base64@3.7.7:
    resolution: {integrity: sha512-7rCnleh0z2CkXhH67J8K1Ytz0b2Y+yxTPL+/KOJoa20hfnVQ/3/T6W/KflYI4bRHRagNeXeU2bkNGI3v1oS/lw==}

  js-cookie@3.0.5:
    resolution: {integrity: sha512-cEiJEAEoIbWfCZYKWhVwFuvPX1gETRYPw6LlaTKoxD3s2AkXzkCjnp6h0V77ozyqj0jakteJ4YqDJT830+lVGw==}
    engines: {node: '>=14'}

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsc-safe-url@0.2.4:
    resolution: {integrity: sha512-0wM3YBWtYePOjfyXQH5MWQ8H7sdk5EXSwZvmSLKk2RboVQ2Bu239jycHDz5J/8Blf3K0Qnoy2b6xD+z10MFB+Q==}

  jsdom@20.0.3:
    resolution: {integrity: sha512-SYhBvTh89tTfCD/CRdSOm13mOBa42iTaTyfyEWBdKcGdPxPtLFBXuHR8XHb33YNYaP+lLbmSvBTsnoesCNJEsQ==}
    engines: {node: '>=14'}
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@3.0.2:
    resolution: {integrity: sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==}
    engines: {node: '>=6'}
    hasBin: true

  jsesc@3.1.0:
    resolution: {integrity: sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==}
    engines: {node: '>=6'}
    hasBin: true

  json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==}

  json-parse-better-errors@1.0.2:
    resolution: {integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==}

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}

  json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==}

  json-stable-stringify@1.2.1:
    resolution: {integrity: sha512-Lp6HbbBgosLmJbjx0pBLbgvx68FaFU1sdkmBuckmhhJ88kL13OA51CDtR2yJB50eCNMH9wRqtQNNiAqQH4YXnA==}
    engines: {node: '>= 0.4'}

  json5@1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==}
    hasBin: true

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==}

  jsonify@0.0.1:
    resolution: {integrity: sha512-2/Ki0GcmuqSrgFyelQq9M05y7PS0mEwuIzrf3f1fPqkVDVRvZrPZtVSMHxdgo8Aq0sxAOb/cr2aqqA3LeWHVPg==}

  jsx-ast-utils@3.3.5:
    resolution: {integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==}
    engines: {node: '>=4.0'}

  keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==}

  klaw-sync@6.0.0:
    resolution: {integrity: sha512-nIeuVSzdCCs6TDPTqI8w1Yre34sSq7AkZ4B3sfOBbI2CgVSB4Du4aLQijFU2+lhAFCwt9+42Hel6lQNIv6AntQ==}

  kleur@3.0.3:
    resolution: {integrity: sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==}
    engines: {node: '>=6'}

  lan-network@0.1.6:
    resolution: {integrity: sha512-0qPYjNoD89v+bfhkIqFBYGBAof1xhxLqjX8bkNN1kQdP81UHpZw5TDXgEjwB+X2iCFGQmzF8TRmvg4vQcykyDA==}
    hasBin: true

  leven@3.1.0:
    resolution: {integrity: sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==}
    engines: {node: '>=6'}

  levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==}
    engines: {node: '>= 0.8.0'}

  lighthouse-logger@1.4.2:
    resolution: {integrity: sha512-gPWxznF6TKmUHrOQjlVo2UbaL2EJ71mb2CCeRs/2qBpi4L/g4LUVc9+3lKQ6DTUZwJswfM7ainGrLO1+fOqa2g==}

  lightningcss-darwin-arm64@1.27.0:
    resolution: {integrity: sha512-Gl/lqIXY+d+ySmMbgDf0pgaWSqrWYxVHoc88q+Vhf2YNzZ8DwoRzGt5NZDVqqIW5ScpSnmmjcgXP87Dn2ylSSQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [darwin]

  lightningcss-darwin-x64@1.27.0:
    resolution: {integrity: sha512-0+mZa54IlcNAoQS9E0+niovhyjjQWEMrwW0p2sSdLRhLDc8LMQ/b67z7+B5q4VmjYCMSfnFi3djAAQFIDuj/Tg==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [darwin]

  lightningcss-freebsd-x64@1.27.0:
    resolution: {integrity: sha512-n1sEf85fePoU2aDN2PzYjoI8gbBqnmLGEhKq7q0DKLj0UTVmOTwDC7PtLcy/zFxzASTSBlVQYJUhwIStQMIpRA==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [freebsd]

  lightningcss-linux-arm-gnueabihf@1.27.0:
    resolution: {integrity: sha512-MUMRmtdRkOkd5z3h986HOuNBD1c2lq2BSQA1Jg88d9I7bmPGx08bwGcnB75dvr17CwxjxD6XPi3Qh8ArmKFqCA==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm]
    os: [linux]

  lightningcss-linux-arm64-gnu@1.27.0:
    resolution: {integrity: sha512-cPsxo1QEWq2sfKkSq2Bq5feQDHdUEwgtA9KaB27J5AX22+l4l0ptgjMZZtYtUnteBofjee+0oW1wQ1guv04a7A==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-arm64-musl@1.27.0:
    resolution: {integrity: sha512-rCGBm2ax7kQ9pBSeITfCW9XSVF69VX+fm5DIpvDZQl4NnQoMQyRwhZQm9pd59m8leZ1IesRqWk2v/DntMo26lg==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-x64-gnu@1.27.0:
    resolution: {integrity: sha512-Dk/jovSI7qqhJDiUibvaikNKI2x6kWPN79AQiD/E/KeQWMjdGe9kw51RAgoWFDi0coP4jinaH14Nrt/J8z3U4A==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-linux-x64-musl@1.27.0:
    resolution: {integrity: sha512-QKjTxXm8A9s6v9Tg3Fk0gscCQA1t/HMoF7Woy1u68wCk5kS4fR+q3vXa1p3++REW784cRAtkYKrPy6JKibrEZA==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-win32-arm64-msvc@1.27.0:
    resolution: {integrity: sha512-/wXegPS1hnhkeG4OXQKEMQeJd48RDC3qdh+OA8pCuOPCyvnm/yEayrJdJVqzBsqpy1aJklRCVxscpFur80o6iQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [win32]

  lightningcss-win32-x64-msvc@1.27.0:
    resolution: {integrity: sha512-/OJLj94Zm/waZShL8nB5jsNj3CfNATLCTyFxZyouilfTmSoLDX7VlVAmhPHoZWVFp4vdmoiEbPEYC8HID3m6yw==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [win32]

  lightningcss@1.27.0:
    resolution: {integrity: sha512-8f7aNmS1+etYSLHht0fQApPc2kNO8qGRutifN5rVIc6Xo6ABsEbqOr758UwI7ALVbTt4x1fllKt0PYgzD9S3yQ==}
    engines: {node: '>= 12.0.0'}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}

  locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}

  locate-path@6.0.0:
    resolution: {integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==}
    engines: {node: '>=10'}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}

  lodash.isequal@4.5.0:
    resolution: {integrity: sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==}
    deprecated: This package is deprecated. Use require('node:util').isDeepStrictEqual instead.

  lodash.memoize@4.1.2:
    resolution: {integrity: sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==}

  lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==}

  lodash.throttle@4.1.1:
    resolution: {integrity: sha512-wIkUCfVKpVsWo3JSZlc+8MB5it+2AN5W8J7YVMST30UrvcQNZ1Okbj+rbVniijTWE6FGYy4XJq/rHkas8qJMLQ==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  log-symbols@2.2.0:
    resolution: {integrity: sha512-VeIAFslyIerEJLXHziedo2basKbMKtTw3vfn5IzG0XTjhAVEJyNHnL2p7vc+wBDSdQuUpNw3M2u6xb9QsAY5Eg==}
    engines: {node: '>=4'}

  loose-envify@1.4.0:
    resolution: {integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==}
    hasBin: true

  lru-cache@10.4.3:
    resolution: {integrity: sha512-JNAzZcXrCt42VGLuYz0zfAzDfAvJWW6AfYlDBQyDV5DClI2m5sAmK+OIO7s59XfsRsWHp02jAJrRadPRGTt6SQ==}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  make-dir@4.0.0:
    resolution: {integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==}
    engines: {node: '>=10'}

  make-error@1.3.6:
    resolution: {integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==}

  make-plural@7.4.0:
    resolution: {integrity: sha512-4/gC9KVNTV6pvYg2gFeQYTW3mWaoJt7WZE5vrp1KnQDgW92JtYZnzmZT81oj/dUTqAIu0ufI2x3dkgu3bB1tYg==}

  makeerror@1.0.12:
    resolution: {integrity: sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==}

  marky@1.2.5:
    resolution: {integrity: sha512-q9JtQJKjpsVxCRVgQ+WapguSbKC3SQ5HEzFGPAJMStgh3QjCawp00UKv3MTTAArTmGmmPUvllHZoNbZ3gs0I+Q==}

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  mdn-data@2.0.14:
    resolution: {integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==}

  memoize-one@5.2.1:
    resolution: {integrity: sha512-zYiwtZUcYyXKo/np96AGZAckk+FWWsUdJ3cHGGmld7+AhvcWmQyGCYUh1hc4Q/pkOhb65dQR/pqCyK0cOaHz4Q==}

  memoize-one@6.0.0:
    resolution: {integrity: sha512-rkpe71W0N0c0Xz6QD0eJETuWAJGnJ9afsl1srmwPrI+yBCkge5EycXXbYRyvL29zZVUWQCY7InPRCv3GDXuZNw==}

  merge-options@3.0.4:
    resolution: {integrity: sha512-2Sug1+knBjkaMsMgf1ctR1Ujx+Ayku4EdJN4Z+C2+JzoeF7A3OZ9KM2GY0CpQS51NR61LTurMJrRKPhSs3ZRTQ==}
    engines: {node: '>=10'}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  metro-babel-transformer@0.82.3:
    resolution: {integrity: sha512-eC0f1MSA8rg7VoNDCYMIAIe5AEgYBskh5W8rIa4RGRdmEOsGlXbAV0AWMYoA7NlIALW/S9b10AcdIwD3n1e50w==}
    engines: {node: '>=18.18'}

  metro-cache-key@0.82.3:
    resolution: {integrity: sha512-dDLTUOJ7YYqGog9kR55InchwnkkHuxBXD765J3hQVWWPCy6xO9uZXZYGX1Y/tIMV8U7Ho1Sve0V13n5rFajrRQ==}
    engines: {node: '>=18.18'}

  metro-cache@0.82.3:
    resolution: {integrity: sha512-9zKhicA5GENROeP+iXku1NrI8FegtwEg3iPXHGixkm1Yppkbwsy/3lSHSiJZoT6GkZmxUDjN6sQ5QQ+/p72Msw==}
    engines: {node: '>=18.18'}

  metro-config@0.82.3:
    resolution: {integrity: sha512-GRG9sBkPvrGXD/Wu3RdEDuWg5NDixF9t0c6Zz9kZ9Aa/aQY+m85JgaCI5HYEV+UzVC/IUFFSpJiMfzQRicppLw==}
    engines: {node: '>=18.18'}

  metro-core@0.82.3:
    resolution: {integrity: sha512-JQZDdXo3hyLl1pqVT4IKEwcBK+3f11qFXeCjQ1hjVpjMwQLOqSM02J7NC/4DNSBt+qWBxWj6R5Jphcc7+9AEWw==}
    engines: {node: '>=18.18'}

  metro-file-map@0.82.3:
    resolution: {integrity: sha512-o4wtloAge85MZl85F87FT59R/4tn5GvCvLfYcnzzDB20o2YX9AMxZqswrGMaei/GbD/Win5FrLF/Iq8oetcByA==}
    engines: {node: '>=18.18'}

  metro-minify-terser@0.82.3:
    resolution: {integrity: sha512-/3FasOULfHq1P0KPNFy5y28Th5oknPSwEbt9JELVBMAPhUnLqQkCLr4M+RQzKG3aEQN1/mEqenWApFCkk6Nm/Q==}
    engines: {node: '>=18.18'}

  metro-resolver@0.82.3:
    resolution: {integrity: sha512-pdib7UrOM04j/RjWmaqmjjWRiuCbpA8BdUSuXzvBaK0QlNzHkRRDv6kiOGxgQ+UgG+KdbPcJktsW9olqiDhf9w==}
    engines: {node: '>=18.18'}

  metro-runtime@0.82.3:
    resolution: {integrity: sha512-J4SrUUsBy9ire8I2sFuXN5MzPmuBHlx1bjvAjdoo1ecpH2mtS3ubRqVnMotBxuK5+GhrbW0mtg5/46PVXy26cw==}
    engines: {node: '>=18.18'}

  metro-source-map@0.82.3:
    resolution: {integrity: sha512-gz7wfjz23rit6ePQ7NKE9x+VOWGKm54vli4wbphR9W+3y0bh6Ad7T0BGH9DUzRAnOnOorewrVEqFmT24mia5sg==}
    engines: {node: '>=18.18'}

  metro-symbolicate@0.82.3:
    resolution: {integrity: sha512-WZKhR+QGbwkOLWP1z58Y7BFWUqLVDEEPsSQ5UI5+OWQDAwdtsPU9+sSNoJtD5qRU9qrB2XewQE3lJ2EQRRFJew==}
    engines: {node: '>=18.18'}
    hasBin: true

  metro-transform-plugins@0.82.3:
    resolution: {integrity: sha512-s1gVrkhczwMbxZLRSLCJ16K/4Sqx5IhO4sWlL6j0jlIEs1/Drn3JrkUUdQTtgmJS8SBpxmmB66cw7wnz751dVg==}
    engines: {node: '>=18.18'}

  metro-transform-worker@0.82.3:
    resolution: {integrity: sha512-z5Y7nYlSlLAEhjFi73uEJh69G5IC6HFZmXFcrxnY+JNlsjT2r0GgsDF4WaQGtarAIt5NP88V8983/PedwNfEcw==}
    engines: {node: '>=18.18'}

  metro@0.82.3:
    resolution: {integrity: sha512-EfSLtuUmfsGk3znJ+zoN8cRLniQo3W1wyA+nJMfpTLdENfbbPnGRTwmKhzRcJIUh9jgkrrF4oRQ5shLtQ2DsUw==}
    engines: {node: '>=18.18'}
    hasBin: true

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-db@1.53.0:
    resolution: {integrity: sha512-oHlN/w+3MQ3rba9rqFr6V/ypF10LSkdwUysQL7GkXoTgIWeV+tcXGA852TBxH+gsh8UWoyhR1hKcoMJTuWflpg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  mimic-fn@1.2.0:
    resolution: {integrity: sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==}
    engines: {node: '>=4'}

  mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  min-indent@1.0.1:
    resolution: {integrity: sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==}
    engines: {node: '>=4'}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@5.1.6:
    resolution: {integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==}
    engines: {node: '>=10'}

  minimatch@9.0.5:
    resolution: {integrity: sha512-G6T0ZX48xgozx7587koeX9Ys2NYy6Gmv//P89sEte9V9whIapMNF4idKxnW2QtCcLiTWlb/wfCabAtAFWhhBow==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass@7.1.2:
    resolution: {integrity: sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw==}
    engines: {node: '>=16 || 14 >=14.17'}

  minizlib@3.0.2:
    resolution: {integrity: sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA==}
    engines: {node: '>= 18'}

  mitt@3.0.1:
    resolution: {integrity: sha512-vKivATfr97l2/QBCYAkXYDbrIWPM2IIKEl7YPhjCvKlG3kE2gm+uBo6nEXK3M5/Ffh/FLpKExzOQ3JJoJGFKBw==}

  mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true

  mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true

  mkdirp@3.0.1:
    resolution: {integrity: sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg==}
    engines: {node: '>=10'}
    hasBin: true

  mobx-react-lite@4.0.5:
    resolution: {integrity: sha512-StfB2wxE8imKj1f6T8WWPf4lVMx3cYH9Iy60bbKXEs21+HQ4tvvfIBZfSmMXgQAefi8xYEwQIz4GN9s0d2h7dg==}
    peerDependencies:
      mobx: ^6.9.0
      react: ^16.8.0 || ^17 || ^18
      react-dom: '*'
      react-native: '*'
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true

  mobx-state-tree@5.3.0:
    resolution: {integrity: sha512-2XInCjIxGQx/UmTbpAreWKcHswmuOKOV23HJmy1x4gqyDqCcOHJcaClpWnD2/qQlGncg8gAUfP/mm+cLpTrtlQ==}
    peerDependencies:
      mobx: ^6.3.0

  mobx@6.10.2:
    resolution: {integrity: sha512-B1UGC3ieK3boCjnMEcZSwxqRDMdzX65H/8zOHbuTY8ZhvrIjTUoLRR2TP2bPqIgYRfb3+dUigu8yMZufNjn0LQ==}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==}

  nanoid@3.3.11:
    resolution: {integrity: sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  nanoid@3.3.8:
    resolution: {integrity: sha512-WNLf5Sd8oZxOm+TzppcYk8gVOgP+l58xNy58D0nbUnOxOWRWvlcCV4kUF7ltmI6PsrLl/BgKEyS4mqsGChFN0w==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  natural-compare-lite@1.4.0:
    resolution: {integrity: sha512-Tj+HTDSJJKaZnfiuw+iaF9skdPpTo2GtEly5JHnWV/hfv2Qj/9RKsGISQtLh2ox3l5EAGw487hnBee0sIJ6v2g==}

  natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==}

  negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  negotiator@0.6.4:
    resolution: {integrity: sha512-myRT3DiWPHqho5PrJaIRyaMv2kgYf0mUVgBNOYMuCH5Ki1yEiQaf/ZJuQ62nvpc44wL5WDbTX7yGJi1Neevw8w==}
    engines: {node: '>= 0.6'}

  neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}

  nested-error-stacks@2.0.1:
    resolution: {integrity: sha512-SrQrok4CATudVzBS7coSz26QRSmlK9TzzoFbeKfcPBUFPjcQM9Rqvr/DlJkOrwI/0KcgvMub1n1g5Jt9EgRn4A==}

  node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==}
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-forge@1.3.1:
    resolution: {integrity: sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==}
    engines: {node: '>= 6.13.0'}

  node-int64@0.4.0:
    resolution: {integrity: sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==}

  node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  npm-package-arg@11.0.3:
    resolution: {integrity: sha512-sHGJy8sOC1YraBywpzQlIKBE4pBbGbiF95U6Auspzyem956E0+FtDtsx1ZxlOJkQCZ1AFXAY/yuvtFYrOxF+Bw==}
    engines: {node: ^16.14.0 || >=18.0.0}

  npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}

  nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}

  nullthrows@1.1.1:
    resolution: {integrity: sha512-2vPPEi+Z7WqML2jZYddDIfy5Dqb0r2fze2zTxNNknZaFpVHU3mFB3R+DWeJWGVx0ecvttSGlJTI+WG+8Z4cDWw==}

  nwsapi@2.2.16:
    resolution: {integrity: sha512-F1I/bimDpj3ncaNDhfyMWuFqmQDBwDB0Fogc2qpL3BWvkQteFD/8BzWuIRl83rq0DXfm8SGt/HFhLXZyljTXcQ==}

  ob1@0.82.3:
    resolution: {integrity: sha512-8/SeymYlPMVODpCATHqm+X8eiuvD1GsKVa11n688V4GGgjrM3CRvrbtrYBs4t89LJDkv5CwGYPdqayuY0DmTTA==}
    engines: {node: '>=18.18'}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-inspect@1.13.4:
    resolution: {integrity: sha512-W67iLl4J2EXEGTbfeHCffrjDfitvLANg0UlX3wFUUSTx92KXRFegMHUVgSqE+wvhAbi4WqjGg9czysTV2Epbew==}
    engines: {node: '>= 0.4'}

  object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==}
    engines: {node: '>= 0.4'}

  object.assign@4.1.7:
    resolution: {integrity: sha512-nK28WOo+QIjBkDduTINE4JkF/UJJKyf2EJxvJKfblDpyg0Q+pkOHNTL0Qwy6NP6FhE/EnzV73BxxqcJaXY9anw==}
    engines: {node: '>= 0.4'}

  object.entries@1.1.8:
    resolution: {integrity: sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==}
    engines: {node: '>= 0.4'}

  object.fromentries@2.0.8:
    resolution: {integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==}
    engines: {node: '>= 0.4'}

  object.hasown@1.1.4:
    resolution: {integrity: sha512-FZ9LZt9/RHzGySlBARE3VF+gE26TxR38SdmqOqliuTnl9wrKulaQs+4dee1V+Io8VfxqzAfHu6YuRgUy8OHoTg==}
    engines: {node: '>= 0.4'}

  object.values@1.2.1:
    resolution: {integrity: sha512-gXah6aZrcUxjWg2zR2MwouP2eHlCBzdV4pygudehaKXSGW4v2AsRQUK+lwwXhii6KFZcunEnmSUoYp5CXibxtA==}
    engines: {node: '>= 0.4'}

  on-finished@2.3.0:
    resolution: {integrity: sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==}
    engines: {node: '>= 0.8'}

  on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}

  on-headers@1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  onetime@2.0.1:
    resolution: {integrity: sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ==}
    engines: {node: '>=4'}

  onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}

  open@7.4.2:
    resolution: {integrity: sha512-MVHddDVweXZF3awtlAS+6pgKLlm/JgxZ90+/NBurBoQctVOOB/zDdVjcyPzQ+0laDGbsWgrRkflI65sQeOgT9Q==}
    engines: {node: '>=8'}

  open@8.4.2:
    resolution: {integrity: sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==}
    engines: {node: '>=12'}

  opencollective-postinstall@2.0.3:
    resolution: {integrity: sha512-8AV/sCtuzUeTo8gQK5qDZzARrulB3egtLzFgteqB2tcT4Mw7B8Kt7JcDHmltjz6FOAHsvTevk70gZEbhM4ZS9Q==}
    hasBin: true

  optionator@0.9.4:
    resolution: {integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==}
    engines: {node: '>= 0.8.0'}

  ora@3.4.0:
    resolution: {integrity: sha512-eNwHudNbO1folBP3JsZ19v9azXWtQZjICdr3Q0TDPIaeBQ3mXLrh54wM+er0+hSp+dWKf+Z8KM58CYzEyIYxYg==}
    engines: {node: '>=6'}

  os-tmpdir@1.0.2:
    resolution: {integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==}
    engines: {node: '>=0.10.0'}

  own-keys@1.0.1:
    resolution: {integrity: sha512-qFOyK5PjiWZd+QQIh+1jhdb9LpxTF0qs7Pm8o5QHYZ0M3vKqSqzsZaEB6oWlxZ+q2sJBMI/Ktgd2N5ZwQoRHfg==}
    engines: {node: '>= 0.4'}

  p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}

  p-limit@3.1.0:
    resolution: {integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==}
    engines: {node: '>=10'}

  p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}

  p-locate@5.0.0:
    resolution: {integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==}
    engines: {node: '>=10'}

  p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  package-json-from-dist@1.0.1:
    resolution: {integrity: sha512-UEZIS3/by4OC8vL3P2dTXRETpebLI2NiI5vIrjaD/5UtrkFX/tNbwjTSRAGC/+7CAo2pIcBaRgWmcBBHcsaCIw==}

  pako@0.2.9:
    resolution: {integrity: sha512-NUcwaKxUxWrZLpDG+z/xZaCgQITkA/Dv4V/T6bw7VON6l1Xz/VnrBqrYjZQ12TamKHzITTfOEIYUj48y2KXImA==}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-json@4.0.0:
    resolution: {integrity: sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw==}
    engines: {node: '>=4'}

  parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}

  parse-png@2.1.0:
    resolution: {integrity: sha512-Nt/a5SfCLiTnQAjx3fHlqp8hRgTL3z7kTQZzvIMS9uCAepnCyjpdEc6M/sz69WqMBdaDBw9sF1F1UaHROYzGkQ==}
    engines: {node: '>=10'}

  parse5@7.2.1:
    resolution: {integrity: sha512-BuBYQYlv1ckiPdQi/ohiivi9Sagc9JG+Ozs0r7b/0iK3sKmrb0b9FdWdBbOdx6hBCM/F9Ir82ofnBhtZOjCRPQ==}

  parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  password-prompt@1.1.3:
    resolution: {integrity: sha512-HkrjG2aJlvF0t2BMH0e2LB/EHf3Lcq3fNMzy4GYHcQblAvOl+QQji1Lx7WRBMqpVK8p+KR7bCg7oqAMXtdgqyw==}

  patch-package@8.0.0:
    resolution: {integrity: sha512-da8BVIhzjtgScwDJ2TtKsfT5JFWz1hYoBl9rUQ1f38MC2HwnEIkK8VN3dKMKcP7P7bvvgzNDbfNHtx3MsQb5vA==}
    engines: {node: '>=14', npm: '>5'}
    hasBin: true

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-scurry@1.11.1:
    resolution: {integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==}
    engines: {node: '>=16 || 14 >=14.18'}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  picomatch@3.0.1:
    resolution: {integrity: sha512-I3EurrIQMlRc9IaAZnqRR044Phh2DXY+55o7uJ0V+hYZAcQYSuFWsc9q5PvyDHUSCe1Qxn/iBz+78s86zWnGag==}
    engines: {node: '>=10'}

  pirates@4.0.6:
    resolution: {integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==}
    engines: {node: '>= 6'}

  pkg-dir@4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==}
    engines: {node: '>=8'}

  plist@3.1.0:
    resolution: {integrity: sha512-uysumyrvkUX0rX/dEVqt8gC3sTBzd4zoWfLeS29nb53imdaXVvLINYXTI2GNqzaMuvacNx4uJQ8+b3zXR0pkgQ==}
    engines: {node: '>=10.4.0'}

  pngjs@3.4.0:
    resolution: {integrity: sha512-NCrCHhWmnQklfH4MtJMRjZ2a8c80qXeMlQMv2uVp9ISJMTt562SbGd6n2oq0PaPgKm7Z6pL9E2UlLIhC+SHL3w==}
    engines: {node: '>=4.0.0'}

  possible-typed-array-names@1.1.0:
    resolution: {integrity: sha512-/+5VFTchJDoVj3bhoqi6UeymcD00DAwb1nJwamzPvHEszJ4FpF6SNNbUbOS8yI56qHzdV8eK0qEfOSiodkTdxg==}
    engines: {node: '>= 0.4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  postcss@8.4.49:
    resolution: {integrity: sha512-OCVPnIObs4N29kxTjzLfUryOkvZEq+pf8jTF0lg8E7uETuWHA+v7j3c/xJmiqpX450191LlmZfUKkXxkTry7nA==}
    engines: {node: ^10 || ^12 || >=14}

  postinstall-prepare@1.0.1:
    resolution: {integrity: sha512-4zxO4DjrV0XfD+ABUFEP0MiQmhKOGBnov5LfLsra/XVOUcQ5gMLLMcV3b8K8wJUfNDv1ozleGblYb06gPbjVUQ==}

  prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==}
    engines: {node: '>= 0.8.0'}

  prettier@2.8.8:
    resolution: {integrity: sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==}
    engines: {node: '>=10.13.0'}
    hasBin: true

  pretty-bytes@5.6.0:
    resolution: {integrity: sha512-FFw039TmrBqFK8ma/7OL3sDz/VytdtJr044/QUJtH0wK9lb9jLq9tJyIxUwtQJHwar2BqtiA4iCWSwo9JLkzFg==}
    engines: {node: '>=6'}

  pretty-format@29.7.0:
    resolution: {integrity: sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==}
    engines: {node: ^14.15.0 || ^16.10.0 || >=18.0.0}

  proc-log@4.2.0:
    resolution: {integrity: sha512-g8+OnU/L2v+wyiVK+D5fA34J7EH8jZ8DDlvwhRCMxmMj7UCBvxiO1mGeN+36JXIKF4zevU4kRBd8lVgG9vLelA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  progress@2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==}
    engines: {node: '>=0.4.0'}

  promise@7.3.1:
    resolution: {integrity: sha512-nolQXZ/4L+bP/UGlkfaIujX9BKxGwmQ9OT4mOt5yvy8iK1h3wqTEJCijzGANTCCl9nWjY41juyAn2K3Q1hLLTg==}

  promise@8.3.0:
    resolution: {integrity: sha512-rZPNPKTOYVNEEKFaq1HqTgOwZD+4/YHS5ukLzQCypkj+OkYx7iv0mA91lJlpPPZ8vMau3IIGj5Qlwrx+8iiSmg==}

  prompts@2.4.2:
    resolution: {integrity: sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==}
    engines: {node: '>= 6'}

  prop-types@15.8.1:
    resolution: {integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  psl@1.15.0:
    resolution: {integrity: sha512-JZd3gMVBAVQkSs6HdNZo9Sdo0LNcQeMNP3CozBJb3JYC/QUYZTnKxP+f8oWRX4rHP5EurWxqAHTSwUCjlNKa1w==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  pure-rand@6.1.0:
    resolution: {integrity: sha512-bVWawvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==}

  qrcode-terminal@0.11.0:
    resolution: {integrity: sha512-Uu7ii+FQy4Qf82G4xu7ShHhjhGahEpCWc3x8UavY3CTcWV+ufmmCtwkr7ZKsX42jdL0kr1B5FKUeqJvAn51jzQ==}
    hasBin: true

  query-string@7.1.3:
    resolution: {integrity: sha512-hh2WYhq4fi8+b+/2Kg9CEge4fDPvHS534aOOvOZeQ3+Vf2mCFsaFBYj0i+iXcAq6I9Vzp5fjMFBlONvayDC1qg==}
    engines: {node: '>=6'}

  querystringify@2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  queue@6.0.2:
    resolution: {integrity: sha512-iHZWu+q3IdFZFX36ro/lKBkSvfkztY5Y7HMiPlOUjhupPcG2JMfst2KKEpu5XndviX/3UhFbRngUPNKtgvtZiA==}

  ramda@0.28.0:
    resolution: {integrity: sha512-9QnLuG/kPVgWvMQ4aODhsBUFKOUmnbUnsSXACv+NCQZcHbeb+v8Lodp8OVxtRULN1/xOyYLLaL6npE6dMq5QTA==}

  randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}

  range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}

  rc@1.2.8:
    resolution: {integrity: sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==}
    hasBin: true

  react-devtools-core@6.1.1:
    resolution: {integrity: sha512-TFo1MEnkqE6hzAbaztnyR5uLTMoz6wnEWwWBsCUzNt+sVXJycuRJdDqvL078M4/h65BI/YO5XWTaxZDWVsW0fw==}

  react-dom@19.0.0:
    resolution: {integrity: sha512-4GV5sHFG0e/0AD4X+ySy6UJd3jVl1iNsNHdpad0qhABJ11twS3TTBnseqsKurKcsNqCEFeGL3uLpVChpIO3QfQ==}
    peerDependencies:
      react: ^19.0.0

  react-fast-compare@3.2.2:
    resolution: {integrity: sha512-nsO+KSNgo1SbJqJEYRE9ERzo7YtYbou/OqjSQKxV7jcKox7+usiUVZOAC+XnDOABXggQTno0Y1CpVnuWEc1boQ==}

  react-freeze@1.0.4:
    resolution: {integrity: sha512-r4F0Sec0BLxWicc7HEyo2x3/2icUTrRmDjaaRyzzn+7aDyFZliszMDOgLVwSnQnYENOlL1o569Ze2HZefk8clA==}
    engines: {node: '>=10'}
    peerDependencies:
      react: '>=17.0.0'

  react-is@16.13.1:
    resolution: {integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==}

  react-is@18.3.1:
    resolution: {integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==}

  react-is@19.1.0:
    resolution: {integrity: sha512-Oe56aUPnkHyyDxxkvqtd7KkdQP5uIUfHxd5XTb3wE9d/kRnZLmKbDB0GWk919tdQ+mxxPtG6EAs6RMT6i1qtHg==}

  react-native-edge-to-edge@1.6.0:
    resolution: {integrity: sha512-2WCNdE3Qd6Fwg9+4BpbATUxCLcouF6YRY7K+J36KJ4l3y+tWN6XCqAC4DuoGblAAbb2sLkhEDp4FOlbOIot2Og==}
    peerDependencies:
      react: '*'
      react-native: '*'

  react-native-elements@3.4.3:
    resolution: {integrity: sha512-VtZc25EecPZyUBER85zFK9ZbY6kkUdcm1ZwJ9hdoGSCr1R/GFgxor4jngOcSYeMvQ+qimd5No44OVJW3rSJECA==}
    peerDependencies:
      react-native-safe-area-context: '>= 3.0.0'
      react-native-vector-icons: '>7.0.0'

  react-native-email-link@1.16.1:
    resolution: {integrity: sha512-uTolJXOa6HKPEiRAQuLKN/OhVwUA4xS++yPV6YiCtRBWxl2q3FUlrJt1G6plfMAEU4qNhmwZnH3tCJ9WNYIDhg==}
    peerDependencies:
      react: '>=16.8.0'
      react-native: '>=0.40.0'

  react-native-gesture-handler@2.24.0:
    resolution: {integrity: sha512-ZdWyOd1C8axKJHIfYxjJKCcxjWEpUtUWgTOVY2wynbiveSQDm8X/PDyAKXSer/GOtIpjudUbACOndZXCN3vHsw==}
    peerDependencies:
      react: '*'
      react-native: '*'

  react-native-get-random-values@1.11.0:
    resolution: {integrity: sha512-4BTbDbRmS7iPdhYLRcz3PGFIpFJBwNZg9g42iwa2P6FOv9vZj/xJc678RZXnLNZzd0qd7Q3CCF6Yd+CU2eoXKQ==}
    peerDependencies:
      react-native: '>=0.56'

  react-native-is-edge-to-edge@1.1.7:
    resolution: {integrity: sha512-EH6i7E8epJGIcu7KpfXYXiV2JFIYITtq+rVS8uEb+92naMRBdxhTuS8Wn2Q7j9sqyO0B+Xbaaf9VdipIAmGW4w==}
    peerDependencies:
      react: '*'
      react-native: '*'

  react-native-onboarding-swiper@1.3.0:
    resolution: {integrity: sha512-2ZPMrZrJFgR5dmVWIj60x/vTBWrm0BZPuc2w7Cz2Sq/8ChypCi3oL8F7GYMrzky1fmknCS6Z0WPphfZVpnLUnQ==}
    peerDependencies:
      prop-types: '*'
      react: '*'
      react-native: '*'

  react-native-prompt-android@1.1.0:
    resolution: {integrity: sha512-4JoyEaT2ZnK9IH+tDFpbTiQBgva8UIFGQf4/Uw/tnEVWBERlVlzcs5B82T9BkeEhEqXhp89JaiSBnLWj30lciw==}

  react-native-ratings@8.0.4:
    resolution: {integrity: sha512-Xczu5lskIIRD6BEdz9A0jDRpEck/SFxRqiglkXi0u67yAtI1/pcJC76P4MukCbT8K4BPVl+42w83YqXBoBRl7A==}
    peerDependencies:
      react: '*'
      react-native: '*'

  react-native-reanimated@3.17.5:
    resolution: {integrity: sha512-SxBK7wQfJ4UoWoJqQnmIC7ZjuNgVb9rcY5Xc67upXAFKftWg0rnkknTw6vgwnjRcvYThrjzUVti66XoZdDJGtw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0
      react: '*'
      react-native: '*'

  react-native-safe-area-context@5.4.0:
    resolution: {integrity: sha512-JaEThVyJcLhA+vU0NU8bZ0a1ih6GiF4faZ+ArZLqpYbL6j7R3caRqj+mE3lEtKCuHgwjLg3bCxLL1GPUJZVqUA==}
    peerDependencies:
      react: '*'
      react-native: '*'

  react-native-screens@4.10.0:
    resolution: {integrity: sha512-Tw21NGuXm3PbiUGtZd0AnXirUixaAbPXDjNR0baBH7/WJDaDTTELLcQ7QRXuqAWbmr/EVCrKj1348ei1KFIr8A==}
    peerDependencies:
      react: '*'
      react-native: '*'

  react-native-size-matters@0.3.1:
    resolution: {integrity: sha512-mKOfBLIBFBcs9br1rlZDvxD5+mAl8Gfr5CounwJtxI6Z82rGrMO+Kgl9EIg3RMVf3G855a85YVqHJL2f5EDRlw==}
    peerDependencies:
      react-native: '*'

  react-native-svg@15.11.2:
    resolution: {integrity: sha512-+YfF72IbWQUKzCIydlijV1fLuBsQNGMT6Da2kFlo1sh+LE3BIm/2Q7AR1zAAR6L0BFLi1WaQPLfFUC9bNZpOmw==}
    peerDependencies:
      react: '*'
      react-native: '*'

  react-native-url-polyfill@2.0.0:
    resolution: {integrity: sha512-My330Do7/DvKnEvwQc0WdcBnFPploYKp9CYlefDXzIdEaA+PAhDYllkvGeEroEzvc4Kzzj2O4yVdz8v6fjRvhA==}
    peerDependencies:
      react-native: '*'

  react-native-vector-icons@10.2.0:
    resolution: {integrity: sha512-n5HGcxUuVaTf9QJPs/W22xQpC2Z9u0nb0KgLPnVltP8vdUvOp6+R26gF55kilP/fV4eL4vsAHUqUjewppJMBOQ==}
    hasBin: true

  react-native-web@0.20.0:
    resolution: {integrity: sha512-OOSgrw+aON6R3hRosCau/xVxdLzbjEcsLysYedka0ZON4ZZe6n9xgeN9ZkoejhARM36oTlUgHIQqxGutEJ9Wxg==}
    peerDependencies:
      react: ^18.0.0 || ^19.0.0
      react-dom: ^18.0.0 || ^19.0.0

  react-native-webview@13.13.5:
    resolution: {integrity: sha512-MfC2B+woL4Hlj2WCzcb1USySKk+SteXnUKmKktOk/H/AQy5+LuVdkPKm8SknJ0/RxaxhZ48WBoTRGaqgR137hw==}
    peerDependencies:
      react: '*'
      react-native: '*'

  react-native@0.79.2:
    resolution: {integrity: sha512-AnGzb56JvU5YCL7cAwg10+ewDquzvmgrMddiBM0GAWLwQM/6DJfGd2ZKrMuKKehHerpDDZgG+EY64gk3x3dEkw==}
    engines: {node: '>=18'}
    hasBin: true
    peerDependencies:
      '@types/react': ^19.0.0
      react: ^19.0.0
    peerDependenciesMeta:
      '@types/react':
        optional: true

  react-reconciler@0.31.0:
    resolution: {integrity: sha512-7Ob7Z+URmesIsIVRjnLoDGwBEG/tVitidU0nMsqX/eeJaLY89RISO/10ERe0MqmzuKUUB1rmY+h1itMbUHg9BQ==}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      react: ^19.0.0

  react-refresh@0.14.2:
    resolution: {integrity: sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==}
    engines: {node: '>=0.10.0'}

  react-server-dom-webpack@19.0.0:
    resolution: {integrity: sha512-hLug9KEXLc8vnU9lDNe2b2rKKDaqrp5gNiES4uyu2Up3FZfZJZmdwLFXlWzdA9gTB/6/cWduSB2K1Lfag2pSvw==}
    engines: {node: '>=0.10.0'}
    peerDependencies:
      react: ^19.0.0
      react-dom: ^19.0.0
      webpack: ^5.59.0

  react-test-renderer@19.0.0:
    resolution: {integrity: sha512-oX5u9rOQlHzqrE/64CNr0HB0uWxkCQmZNSfozlYvwE71TLVgeZxVf0IjouGEr1v7r1kcDifdAJBeOhdhxsG/DA==}
    peerDependencies:
      react: ^19.0.0

  react@19.0.0:
    resolution: {integrity: sha512-V8AVnmPIICiWpGfm6GLzCR/W5FXLchHop40W4nXBmdlEceh16rCN8O8LNWm5bh5XUX91fh7KpA+W0TgMKmgTpQ==}
    engines: {node: '>=0.10.0'}

  reactotron-core-client@2.9.7:
    resolution: {integrity: sha512-QWd0ekjU71CQL70J87NT5f7cmIJAMVZND+YgOaaUgmgFjhiXzHrpeIWs19+IFQPVLsJr97gdi58X8Y62Hchs9w==}

  reactotron-core-contract@0.2.5:
    resolution: {integrity: sha512-pxuXFG1jffAWQSdT0FBws+/Xvl8++6T6WrAi4g7AxIZU5FPeut2wq//0yDhDWx6lF82eAo5JTZK7zECQy45QxA==}

  reactotron-mst@3.1.11:
    resolution: {integrity: sha512-G/t+6z6e70mInstRhf5hmT8iFwgQEaS0kJBYAXRDJ1sLgrxJEchGfrBc+LDRDT/Bze6EvSWEG+wcPbr1iNbM2Q==}
    peerDependencies:
      mobx: ^3.4.1 || ^4.1.0 || >6.0.0
      mobx-state-tree: ^1.4.0 || ^2.0.2 || >3.0.0
      reactotron-core-client: '*'

  reactotron-react-js@3.3.16:
    resolution: {integrity: sha512-6IY3faYFGgGchS08cZxwiVPE4NosRzynJrQVbWJc12nUI8t4XnldBsdvAy+FVDOaFxYW1S0dR5Hgh3oCzEwXng==}

  reactotron-react-native@5.1.13:
    resolution: {integrity: sha512-+mlAzW9DIOfWkz0v3OoquX9uhvvCzJNczx2YBfU+22TxorpaC/nSVddi63qxoL9q4lNScw4EqXSEbdra1jbNaQ==}
    peerDependencies:
      react-native: '>=0.40.0'

  recyclerlistview@4.2.3:
    resolution: {integrity: sha512-STR/wj/FyT8EMsBzzhZ1l2goYirMkIgfV3gYEPxI3Kf3lOnu6f7Dryhyw7/IkQrgX5xtTcDrZMqytvteH9rL3g==}
    peerDependencies:
      react: '>= 15.2.1'
      react-native: '>= 0.30.0'

  redent@3.0.0:
    resolution: {integrity: sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==}
    engines: {node: '>=8'}

  reflect.getprototypeof@1.0.10:
    resolution: {integrity: sha512-00o4I+DVrefhv+nX0ulyi3biSHCPDe+yLv5o/p6d/UVlirijB8E16FtfwSAi4g3tcqrQ4lRAqQSoFEZJehYEcw==}
    engines: {node: '>= 0.4'}

  regenerate-unicode-properties@10.2.0:
    resolution: {integrity: sha512-DqHn3DwbmmPVzeKj9woBadqmXxLvQoQIwu7nopMc72ztvxVmVk2SBhSnx67zuye5TP+lJsb/TBQsjLKhnDf3MA==}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}

  regenerator-runtime@0.13.11:
    resolution: {integrity: sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}

  regenerator-transform@0.15.2:
    resolution: {integrity: sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==}

  regexp.prototype.flags@1.5.4:
    resolution: {integrity: sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==}
    engines: {node: '>= 0.4'}

  regexpp@3.2.0:
    resolution: {integrity: sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==}
    engines: {node: '>=8'}

  regexpu-core@6.2.0:
    resolution: {integrity: sha512-H66BPQMrv+V16t8xtmq+UC0CBpiTBA60V8ibS1QVReIp8T1z8hwFxqcGzm9K6lgsN7sB5edVH8a+ze6Fqm4weA==}
    engines: {node: '>=4'}

  regjsgen@0.8.0:
    resolution: {integrity: sha512-RvwtGe3d7LvWiDQXeQw8p5asZUmfU1G/l6WbUXeHta7Y2PEIvBTwH6E2EfmYUK8pxcxEdEmaomqyp0vZZ7C+3Q==}

  regjsparser@0.12.0:
    resolution: {integrity: sha512-cnE+y8bz4NhMjISKbgeVJtqNbtf5QpjZP+Bslo+UqkIt9QPnX9q095eiRRASJG1/tz6dlNr6Z5NsBiWYokp6EQ==}
    hasBin: true

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}

  requireg@0.2.2:
    resolution: {integrity: sha512-nYzyjnFcPNGR3lx9lwPPPnuQxv6JWEZd2Ci0u9opN7N5zUEPIhY/GbL3vMGOr2UXwEg9WwSyV9X9Y/kLFgPsOg==}
    engines: {node: '>= 4.0.0'}

  requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  resolve-cwd@3.0.0:
    resolution: {integrity: sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==}
    engines: {node: '>=8'}

  resolve-from@3.0.0:
    resolution: {integrity: sha512-GnlH6vxLymXJNMBo7XP1fJIzBFbdYt49CuTwmB/6N53t+kMPRMFKz783LlQ4tv28XoQfMWinAJX6WCGf2IlaIw==}
    engines: {node: '>=4'}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}

  resolve-workspace-root@2.0.0:
    resolution: {integrity: sha512-IsaBUZETJD5WsI11Wt8PKHwaIe45or6pwNc8yflvLJ4DWtImK9kuLoH5kUva/2Mmx/RdIyr4aONNSa2v9LTJsw==}

  resolve.exports@2.0.3:
    resolution: {integrity: sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A==}
    engines: {node: '>=10'}

  resolve@1.22.10:
    resolution: {integrity: sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==}
    engines: {node: '>= 0.4'}
    hasBin: true

  resolve@1.7.1:
    resolution: {integrity: sha512-c7rwLofp8g1U+h1KNyHL/jicrKg1Ek4q+Lr33AL65uZTinUZHe30D5HlyN5V9NW0JX1D5dXQ4jqW5l7Sy/kGfw==}

  resolve@2.0.0-next.5:
    resolution: {integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==}
    hasBin: true

  restore-cursor@2.0.0:
    resolution: {integrity: sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q==}
    engines: {node: '>=4'}

  reusify@1.1.0:
    resolution: {integrity: sha512-g6QUff04oZpHs0eG5p83rFLhHeV00ug/Yf9nZM6fLeUrPguBTkTQOdpAWWspMh55TZfVQDPaN3NQJfbVRAxdIw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rimraf@2.7.1:
    resolution: {integrity: sha512-uWjbaKIK3T1OSVptzX7Nl6PvQ3qAGtKEtVRjRuazjfL3Bx5eI409VZSqgND+4UNnmzLVdPj9FqFJNPqBZFve4w==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rtl-detect@1.1.2:
    resolution: {integrity: sha512-PGMBq03+TTG/p/cRB7HCLKJ1MgDIi07+QU1faSjiYRfmY5UsAttV9Hs08jDAHVwcOwmVLcSJkpwyfXszVjWfIQ==}

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  rxjs@7.8.2:
    resolution: {integrity: sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==}

  safe-array-concat@1.1.3:
    resolution: {integrity: sha512-AURm5f0jYEOydBj7VQlVvDrjeFgthDdEF5H1dP+6mNpoXOMo1quQqJ4wvJDyRZ9+pO3kGWoOdmV08cSv2aJV6Q==}
    engines: {node: '>=0.4'}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safe-push-apply@1.0.0:
    resolution: {integrity: sha512-iKE9w/Z7xCzUMIZqdBsp6pEQvwuEebH4vdpjcDWnyzaI6yl6O9FHvVpmGelvEHNsoY6wGblkxR6Zty/h00WiSA==}
    engines: {node: '>= 0.4'}

  safe-regex-test@1.1.0:
    resolution: {integrity: sha512-x/+Cz4YrimQxQccJf5mKEbIa1NzeCRNI5Ecl/ekmlYaampdNLPalVyIcCZNNH3MvmqBugV5TMYZXv0ljslUlaw==}
    engines: {node: '>= 0.4'}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  sax@1.4.1:
    resolution: {integrity: sha512-+aWOz7yVScEGoKNd4PA10LZ8sk0A/z5+nXQG5giUO5rprX9jgYsTdov9qCchZiPIZezbZH+jRut8nPodFAX4Jg==}

  saxes@6.0.0:
    resolution: {integrity: sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==}
    engines: {node: '>=v12.22.7'}

  scheduler@0.25.0:
    resolution: {integrity: sha512-xFVuu11jh+xcO7JOAGJNOXld8/TcEHK/4CituBUeUb5hqxJLj9YuemAEuvm9gQ/+pgXYfbQuqAkiYu+u7YEsNA==}

  schema-utils@4.3.0:
    resolution: {integrity: sha512-Gf9qqc58SpCA/xdziiHz35F4GNIWYWZrEshUc/G/r5BnLph6xpKuLeoJoQuj5WfBIx/eQLf+hmVPYHaxJu7V2g==}
    engines: {node: '>= 10.13.0'}

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.7.1:
    resolution: {integrity: sha512-hlq8tAfn0m/61p4BVRcPzIGr6LKiMwo4VM6dGi6pt4qcRkmNzTcWq6eCEjEh+qXjkMDvPlOFFSGwQjoEa6gyMA==}
    engines: {node: '>=10'}
    hasBin: true

  send@0.19.0:
    resolution: {integrity: sha512-dW41u5VfLXu8SJh5bwRmyYUbAoSB3c9uQh6L8h/KtsFREPWpbX1lrljJo186Jc4nmci/sGUZ9a0a0J2zgfq2hw==}
    engines: {node: '>= 0.8.0'}

  serialize-error@2.1.0:
    resolution: {integrity: sha512-ghgmKt5o4Tly5yEG/UJp8qTd0AN7Xalw4XBtDEKP655B699qMEtra1WlXeE6WIvdEG481JvRxULKsInq/iNysw==}
    engines: {node: '>=0.10.0'}

  serialize-javascript@6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==}

  serve-static@1.16.2:
    resolution: {integrity: sha512-VqpjJZKadQB/PEbEwvFdO43Ax5dFBZ2UECszz8bQ7pi7wt//PWe1P6MN7eCnjsatYtBT6EuiClbjSWP2WrIoTw==}
    engines: {node: '>= 0.8.0'}

  server-only@0.0.1:
    resolution: {integrity: sha512-qepMx2JxAa5jjfzxG79yPPq+8BuFToHd1hm7kI+Z4zAq1ftQiP7HcxMhDDItrbtwVeLg/cY2JnKnrcFkmiswNA==}

  set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==}
    engines: {node: '>= 0.4'}

  set-function-name@2.0.2:
    resolution: {integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==}
    engines: {node: '>= 0.4'}

  set-proto@1.0.0:
    resolution: {integrity: sha512-RJRdvCo6IAnPdsvP/7m6bsQqNnn1FCBX5ZNtFL98MmFF/4xAIJTIg1YbHW5DC2W5SKZanrC6i4HsJqlajw/dZw==}
    engines: {node: '>= 0.4'}

  setimmediate@1.0.5:
    resolution: {integrity: sha512-MATJdZp8sLqDl/68LfQmbP8zKPLQNV6BIZoIgrscFDQ+RsvK/BxeDQOgyxKKoh0y/8h3BqVFnCqQ/gd+reiIXA==}

  setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shell-quote@1.8.2:
    resolution: {integrity: sha512-AzqKpGKjrj7EM6rKVQEPpB288oCfnrEIuyoT9cyF4nmGa7V8Zk6f7RRqYisX8X9m+Q7bd632aZW4ky7EhbQztA==}
    engines: {node: '>= 0.4'}

  side-channel-list@1.0.0:
    resolution: {integrity: sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==}
    engines: {node: '>= 0.4'}

  side-channel-map@1.0.1:
    resolution: {integrity: sha512-VCjCNfgMsby3tTdo02nbjtM/ewra6jPHmpThenkTYh8pG9ucZ/1P8So4u4FGBek/BjpOVsDCMoLA/iuBKIFXRA==}
    engines: {node: '>= 0.4'}

  side-channel-weakmap@1.0.2:
    resolution: {integrity: sha512-WPS/HvHQTYnHisLo9McqBHOJk2FkHO/tlpvldyrnem4aeQp4hai3gythswg6p01oSoTl58rcpiFAjF2br2Ak2A==}
    engines: {node: '>= 0.4'}

  side-channel@1.1.0:
    resolution: {integrity: sha512-ZX99e6tRweoUXqR+VBrslhda51Nh5MTQwou5tnUDgbtyM0dBgmhEDtWGP/xbKn6hqfPRHujUNwz5fy/wbbhnpw==}
    engines: {node: '>= 0.4'}

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  simple-plist@1.3.1:
    resolution: {integrity: sha512-iMSw5i0XseMnrhtIzRb7XpQEXepa9xhWxGUojHBL43SIpQuDQkh3Wpy67ZbDzZVr6EKxvwVChnVpdl8hEVLDiw==}

  simple-swizzle@0.2.2:
    resolution: {integrity: sha512-JA//kQgZtbuY83m+xT+tXJkmJncGMTFT+C+g2h2R9uxkYIrE2yy9sgmcLhCnw57/WSD+Eh3J97FPEDFnbXnDUg==}

  sisteransi@1.0.5:
    resolution: {integrity: sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==}

  slash@2.0.0:
    resolution: {integrity: sha512-ZYKh3Wh2z1PpEXWr0MpSBZ0V6mZHAQfYevttO11c51CaWjGTaadiKZ+wVt1PbMlDV5qhMFslpZCemhwOK7C89A==}
    engines: {node: '>=6'}

  slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==}
    engines: {node: '>=8'}

  slash@5.1.0:
    resolution: {integrity: sha512-ZA6oR3T/pEyuqwMgAKT0/hAv8oAXckzbkmR0UkUosQ+Mc4RxGoJkRmwHgHufaenlyAgE1Mxgpdcrf75y6XcnDg==}
    engines: {node: '>=14.16'}

  slugify@1.6.6:
    resolution: {integrity: sha512-h+z7HKHYXj6wJU+AnS/+IH8Uh9fdcX1Lrhg1/VMdf9PwoBQXFcXiAdsy2tSK0P6gKwJLXp02r90ahUCqHk9rrw==}
    engines: {node: '>=8.0.0'}

  source-map-js@1.2.1:
    resolution: {integrity: sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA==}
    engines: {node: '>=0.10.0'}

  source-map-support@0.5.13:
    resolution: {integrity: sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==}

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.5.6:
    resolution: {integrity: sha512-MjZkVp0NHr5+TPihLcadqnlVoGIoWo4IBHptutGh9wI3ttUYvCG26HkSuDi+K6lsZ25syXJXcctwgyVCt//xqA==}
    engines: {node: '>=0.10.0'}

  source-map@0.5.7:
    resolution: {integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==}
    engines: {node: '>=0.10.0'}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  split-on-first@1.1.0:
    resolution: {integrity: sha512-43ZssAJaMusuKWL8sKUBQXHWOpq8d6CfN/u1p4gUzfJkM05C8rxTmYrkIPTXapZpORA6LkkzcUulJ8FqA7Uudw==}
    engines: {node: '>=6'}

  sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}

  stack-generator@2.0.10:
    resolution: {integrity: sha512-mwnua/hkqM6pF4k8SnmZ2zfETsRUpWXREfA/goT8SLCV4iOFa4bzOX2nDipWAZFPTjLvQB82f5yaodMVhK0yJQ==}

  stack-utils@2.0.6:
    resolution: {integrity: sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==}
    engines: {node: '>=10'}

  stackframe@1.3.4:
    resolution: {integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==}

  stacktrace-gps@3.1.2:
    resolution: {integrity: sha512-GcUgbO4Jsqqg6RxfyTHFiPxdPqF+3LFmQhm7MgCuYQOYuWyqxo5pwRPz5d/u6/WYJdEnWfK4r+jGbyD8TSggXQ==}

  stacktrace-js@2.0.1:
    resolution: {integrity: sha512-13oDNgBSeWtdGa4/2BycNyKqe+VktCoJ8VLx4pDoJkwGGJVtiHdfMOAj3aW9xTi8oR2v34z9IcvfCvT6XNdNAw==}

  stacktrace-js@2.0.2:
    resolution: {integrity: sha512-Je5vBeY4S1r/RnLydLl0TBTi3F2qdfWmYsGvtfZgEI+SCprPppaIhQf5nGcal4gI4cGpCV/duLcAzT1np6sQqg==}

  stacktrace-parser@0.1.11:
    resolution: {integrity: sha512-WjlahMgHmCJpqzU8bIBy4qtsZdU9lRlcZE3Lvyej6t4tuOuv1vk57OW3MBrj6hXBFx/nNoC9MPMTcr5YA7NQbg==}
    engines: {node: '>=6'}

  statuses@1.5.0:
    resolution: {integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==}
    engines: {node: '>= 0.6'}

  statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  stream-buffers@2.2.0:
    resolution: {integrity: sha512-uyQK/mx5QjHun80FLJTfaWE7JtwfRMKBLkMne6udYOmvH0CawotVa7TfgYHzAnpphn4+TweIx1QKMnRIbipmUg==}
    engines: {node: '>= 0.10.0'}

  strict-uri-encode@2.0.0:
    resolution: {integrity: sha512-QwiXZgpRcKkhTj2Scnn++4PKtWsH0kpzZ62L2R6c/LUVYv7hVnZqcg2+sMuT6R7Jusu1vviK/MFsu6kNJfWlEQ==}
    engines: {node: '>=4'}

  string-length@4.0.2:
    resolution: {integrity: sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==}
    engines: {node: '>=10'}

  string-length@5.0.1:
    resolution: {integrity: sha512-9Ep08KAMUn0OadnVaBuRdE2l615CQ508kr0XMadjClfYpdCyvrbFp6Taebo8yyxokQ4viUd/xPPUA4FGgUa0ow==}
    engines: {node: '>=12.20'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  string.prototype.matchall@4.0.12:
    resolution: {integrity: sha512-6CC9uyBL+/48dYizRf7H7VAYCMCNTBeM78x/VTUe9bFEaxBepPJDa1Ow99LqI/1yF7kuy7Q3cQsYMrcjGUcskA==}
    engines: {node: '>= 0.4'}

  string.prototype.trim@1.2.10:
    resolution: {integrity: sha512-Rs66F0P/1kedk5lyYyH9uBzuiI/kNRmwJAR9quK6VOtIpZ2G+hMZd+HQbbv25MgCA6gEffoMZYxlTod4WcdrKA==}
    engines: {node: '>= 0.4'}

  string.prototype.trimend@1.0.9:
    resolution: {integrity: sha512-G7Ok5C6E/j4SGfyLCloXTrngQIQU3PWtXGst3yM7Bea9FRURf1S42ZHlZZtsNque2FN2PoUhfZXYLNWwEr4dLQ==}
    engines: {node: '>= 0.4'}

  string.prototype.trimstart@1.0.8:
    resolution: {integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==}
    engines: {node: '>= 0.4'}

  strip-ansi@5.2.0:
    resolution: {integrity: sha512-DuRs1gKbBqsMKIZlrffwlug8MHkcnpjs5VPmL1PAh+mA30U0DTotfDZ0d2UUsXpPmPmMMJ6W773MaA3J+lbiWA==}
    engines: {node: '>=6'}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}

  strip-bom@3.0.0:
    resolution: {integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==}
    engines: {node: '>=4'}

  strip-bom@4.0.0:
    resolution: {integrity: sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==}
    engines: {node: '>=8'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}

  strip-indent@3.0.0:
    resolution: {integrity: sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==}
    engines: {node: '>=8'}

  strip-json-comments@2.0.1:
    resolution: {integrity: sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==}
    engines: {node: '>=0.10.0'}

  strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==}
    engines: {node: '>=8'}

  strnum@1.1.2:
    resolution: {integrity: sha512-vrN+B7DBIoTTZjnPNewwhx6cBA/H+IS7rfW68n7XxC1y7uoiGQBxaKzqucGUgavX15dJgiGztLJ8vxuEzwqBdA==}

  structured-headers@0.4.1:
    resolution: {integrity: sha512-0MP/Cxx5SzeeZ10p/bZI0S6MpgD+yxAhi1BOQ34jgnMXsCq3j1t6tQnZu+KdlL7dvJTLT3g9xN8tl10TqgFMcg==}

  styleq@0.1.3:
    resolution: {integrity: sha512-3ZUifmCDCQanjeej1f6kyl/BeP/Vae5EYkQ9iJfUm/QwZvlgnZzyflqAsAWYURdtea8Vkvswu2GrC57h3qffcA==}

  sucrase@3.35.0:
    resolution: {integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true

  sudo-prompt@8.2.5:
    resolution: {integrity: sha512-rlBo3HU/1zAJUrkY6jNxDOC9eVYliG6nS4JA8u8KAshITd07tafMc/Br7xQwCSseXwJ2iCcHCE8SNWX3q8Z+kw==}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}

  supports-hyperlinks@2.3.0:
    resolution: {integrity: sha512-RpsAZlpWcDwOPQA22aCH4J0t7L8JmAvsCxfOSEwm7cQs3LshN36QaTkwd70DnBOXDWGssw2eUoc8CaRWT0XunA==}
    engines: {node: '>=8'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  symbol-tree@3.2.4:
    resolution: {integrity: sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==}

  tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}

  tar@7.4.3:
    resolution: {integrity: sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw==}
    engines: {node: '>=18'}

  temp-dir@2.0.0:
    resolution: {integrity: sha512-aoBAniQmmwtcKp/7BzsH8Cxzv8OL736p7v1ihGb5e9DJ9kTwGWHrQrVB5+lfVDzfGrdRzXch+ig7LHaY1JTOrg==}
    engines: {node: '>=8'}

  terminal-link@2.1.1:
    resolution: {integrity: sha512-un0FmiRUQNr5PJqy9kP7c40F5BOfpGlYTrxonDChEZB7pzZxRNp/bt+ymiy9/npwXya9KH99nJ/GXFIiUkYGFQ==}
    engines: {node: '>=8'}

  terser-webpack-plugin@5.3.14:
    resolution: {integrity: sha512-vkZjpUjb6OMS7dhV+tILUW6BhpDR7P2L/aQSAv+Uwk+m8KATX9EccViHTJR2qDtACKPIYndLGCyl3FMo+r2LMw==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true

  terser@5.39.0:
    resolution: {integrity: sha512-LBAhFyLho16harJoWMg/nZsQYgTrg5jXOn2nCYjRUcZZEdE3qa2zb8QEDRUGVZBW4rlazf2fxkg8tztybTaqWw==}
    engines: {node: '>=10'}
    hasBin: true

  test-exclude@6.0.0:
    resolution: {integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==}
    engines: {node: '>=8'}

  text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}

  thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==}
    engines: {node: '>=0.8'}

  thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==}

  throat@5.0.0:
    resolution: {integrity: sha512-fcwX4mndzpLQKBS1DVYhGAcYaYt7vsHNIvQV+WXMvnow5cgjPphq5CaayLaGsjRdSCKZFNGt7/GYAuXaNOiYCA==}

  tiny-inflate@1.0.3:
    resolution: {integrity: sha512-pkY1fj1cKHb2seWDy0B16HeWyczlJA9/WW3u3c4z/NiWDsO3DOU5D7nhTLE9CF0yXv/QZFY7sEJmj24dK+Rrqw==}

  tinycolor2@1.6.0:
    resolution: {integrity: sha512-XPaBkWQJdsf3pLKJV9p4qN/S+fm2Oj8AIPo1BTUhg5oxkvm9+SVEGFdhyOz7tTdUTfvxMiAs4sp6/eZO2Ew+pw==}

  tmp@0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==}
    engines: {node: '>=0.6.0'}

  tmpl@1.0.5:
    resolution: {integrity: sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  tough-cookie@4.1.4:
    resolution: {integrity: sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==}
    engines: {node: '>=6'}

  tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==}

  tr46@3.0.0:
    resolution: {integrity: sha512-l7FvfAHlcmulp8kr+flpQZmVwtu7nfRV7NZujtN0OqES8EL4O4e0qqzL0DC5gAvx/ZC/9lk6rhcUwYvkBnBnYA==}
    engines: {node: '>=12'}

  ts-interface-checker@0.1.13:
    resolution: {integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==}

  ts-jest@29.2.6:
    resolution: {integrity: sha512-yTNZVZqc8lSixm+QGVFcPe6+yj7+TWZwIesuOWvfcn4B9bz5x4NDzVCQQjOs7Hfouu36aEqfEbo9Qpo+gq8dDg==}
    engines: {node: ^14.15.0 || ^16.10.0 || ^18.0.0 || >=20.0.0}
    hasBin: true
    peerDependencies:
      '@babel/core': '>=7.0.0-beta.0 <8'
      '@jest/transform': ^29.0.0
      '@jest/types': ^29.0.0
      babel-jest: ^29.0.0
      esbuild: '*'
      jest: ^29.0.0
      typescript: '>=4.3 <6'
    peerDependenciesMeta:
      '@babel/core':
        optional: true
      '@jest/transform':
        optional: true
      '@jest/types':
        optional: true
      babel-jest:
        optional: true
      esbuild:
        optional: true

  ts-node@10.9.2:
    resolution: {integrity: sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==}
    hasBin: true
    peerDependencies:
      '@swc/core': '>=1.2.50'
      '@swc/wasm': '>=1.2.50'
      '@types/node': '*'
      typescript: '>=2.7'
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      '@swc/wasm':
        optional: true

  ts-object-utils@0.0.5:
    resolution: {integrity: sha512-iV0GvHqOmilbIKJsfyfJY9/dNHCs969z3so90dQWsO1eMMozvTpnB1MEaUbb3FYtZTGjv5sIy/xmslEz0Rg2TA==}

  tsconfig-paths@3.15.0:
    resolution: {integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==}

  tslib@1.14.1:
    resolution: {integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  tsutils@3.21.0:
    resolution: {integrity: sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==}
    engines: {node: '>= 6'}
    peerDependencies:
      typescript: '>=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta'

  type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==}
    engines: {node: '>= 0.8.0'}

  type-detect@4.0.8:
    resolution: {integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==}
    engines: {node: '>=4'}

  type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}

  type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}

  type-fest@0.7.1:
    resolution: {integrity: sha512-Ne2YiiGN8bmrmJJEuTWTLJR32nh/JdL1+PSicowtNb0WFpn59GK8/lfD61bVtzguz7b3PBt74nxpv/Pw5po5Rg==}
    engines: {node: '>=8'}

  typed-array-buffer@1.0.3:
    resolution: {integrity: sha512-nAYYwfY3qnzX30IkA6AQZjVbtK6duGontcQm1WSG1MD94YLqK0515GNApXkoxKOWMusVssAHWLh9SeaoefYFGw==}
    engines: {node: '>= 0.4'}

  typed-array-byte-length@1.0.3:
    resolution: {integrity: sha512-BaXgOuIxz8n8pIq3e7Atg/7s+DpiYrxn4vdot3w9KbnBhcRQq6o3xemQdIfynqSeXeDrF32x+WvfzmOjPiY9lg==}
    engines: {node: '>= 0.4'}

  typed-array-byte-offset@1.0.4:
    resolution: {integrity: sha512-bTlAFB/FBYMcuX81gbL4OcpH5PmlFHqlCCpAl8AlEzMz5k53oNDvN8p1PNOWLEmI2x4orp3raOFB51tv9X+MFQ==}
    engines: {node: '>= 0.4'}

  typed-array-length@1.0.7:
    resolution: {integrity: sha512-3KS2b+kL7fsuk/eJZ7EQdnEmQoaho/r6KUef7hxvltNA5DR8NAUM+8wJMbJyZ4G9/7i3v5zPBIMN5aybAh2/Jg==}
    engines: {node: '>= 0.4'}

  typescript@5.8.3:
    resolution: {integrity: sha512-p1diW6TqL9L07nNxvRMM7hMMw4c5XOo/1ibL4aAIGmSAt9slTE1Xgw5KWuof2uTOvCg9BY7ZRi+GaF+7sfgPeQ==}
    engines: {node: '>=14.17'}
    hasBin: true

  ua-parser-js@1.0.40:
    resolution: {integrity: sha512-z6PJ8Lml+v3ichVojCiB8toQJBuwR42ySM4ezjXIqXK3M0HczmKQ3LF4rhU55PfD99KEEXQG6yb7iOMyvYuHew==}
    hasBin: true

  ulid@2.3.0:
    resolution: {integrity: sha512-keqHubrlpvT6G2wH0OEfSW4mquYRcbe/J8NMmveoQOjUqmo+hXtO+ORCpWhdbZ7k72UtY61BL7haGxW6enBnjw==}
    hasBin: true

  unbox-primitive@1.1.0:
    resolution: {integrity: sha512-nWJ91DjeOkej/TA8pXQ3myruKpKEYgqvpw9lz4OPHj/NWFNluYrjbz9j01CJ8yKQd2g4jFoOkINCTW2I5LEEyw==}
    engines: {node: '>= 0.4'}

  undici-types@6.20.0:
    resolution: {integrity: sha512-Ny6QZ2Nju20vw1SRHe3d9jVu6gJ+4e3+MMpqu7pqE5HT6WsTSlce++GQmK5UXS8mzV8DSYHrQH+Xrf2jVcuKNg==}

  undici@6.21.2:
    resolution: {integrity: sha512-uROZWze0R0itiAKVPsYhFov9LxrPMHLMEQFszeI2gCN6bnIIZ8twzBCJcN2LJrBBLfrP0t1FW0g+JmKVl8Vk1g==}
    engines: {node: '>=18.17'}

  unicode-canonical-property-names-ecmascript@2.0.1:
    resolution: {integrity: sha512-dA8WbNeb2a6oQzAQ55YlT5vQAWGV9WXOsi3SskE3bcCdM0P4SDd+24zS/OCacdRq5BkdsRj9q3Pg6YyQoxIGqg==}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.2.0:
    resolution: {integrity: sha512-4IehN3V/+kkr5YeSSDDQG8QLqO26XpL2XP3GQtqwlT/QYSECAwFztxVHjlbh0+gjJ3XmNLS0zDsbgs9jWKExLg==}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}

  unicode-trie@2.0.0:
    resolution: {integrity: sha512-x7bc76x0bm4prf1VLg79uhAzKw8DVboClSN5VxJuQ+LKDOVEW9CdH+VY7SP+vX7xCYQqzzgQpFqz15zeLvAtZQ==}

  unique-string@2.0.0:
    resolution: {integrity: sha512-uNaeirEPvpZWSgzwsPGtU2zVSTrn/8L5q/IexZmH0eH6SA73CmAA5U4GwORTxQAZs95TAXLNqeLoPPNO5gZfWg==}
    engines: {node: '>=8'}

  universalify@0.2.0:
    resolution: {integrity: sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==}
    engines: {node: '>= 4.0.0'}

  universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==}
    engines: {node: '>= 10.0.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  url-parse@1.5.10:
    resolution: {integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==}

  use-latest-callback@0.2.3:
    resolution: {integrity: sha512-7vI3fBuyRcP91pazVboc4qu+6ZqM8izPWX9k7cRnT8hbD5svslcknsh3S9BUhaK11OmgTV4oWZZVSeQAiV53SQ==}
    peerDependencies:
      react: '>=16.8'

  use-sync-external-store@1.4.0:
    resolution: {integrity: sha512-9WXSPC5fMv61vaupRkCKCxsPxBocVnwakBEkMIHHpkTTg6icbJtg6jzgtLDm4bl3cSHAca52rYWih0k4K3PfHw==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  use-sync-external-store@1.5.0:
    resolution: {integrity: sha512-Rb46I4cGGVBmjamjphe8L/UnvJD+uPPtTkNvX5mZgqdbavhI4EbgIWJiIHXJ8bc/i9EQGPRh4DwEURJ552Do0A==}
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0

  utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}

  uuid@7.0.3:
    resolution: {integrity: sha512-DPSke0pXhTZgoF/d+WSt2QaKMCFSfx7QegxEWT+JOuHF5aWrKEn0G+ztjuJg/gG8/ItK+rbPCD/yNv8yyih6Cg==}
    hasBin: true

  uuid@9.0.1:
    resolution: {integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==}
    hasBin: true

  v8-compile-cache-lib@3.0.1:
    resolution: {integrity: sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==}

  v8-compile-cache@2.4.0:
    resolution: {integrity: sha512-ocyWc3bAHBB/guyqJQVI5o4BZkPhznPYUG2ea80Gond/BgNWpap8TOmLSeeQG7bnh2KMISxskdADG59j7zruhw==}

  v8-to-istanbul@9.3.0:
    resolution: {integrity: sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==}
    engines: {node: '>=10.12.0'}

  validate-npm-package-name@5.0.1:
    resolution: {integrity: sha512-OljLrQ9SQdOUqTaQxqL5dEfZWrXExyyWsozYlAWFawPVNuD83igl7uJD2RTkNMbniIYgt8l81eCJGIdQF7avLQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  victory-native@41.16.1:
    resolution: {integrity: sha512-YtIImMS9OgVUMPiHepWH9hL9Asrwi38NJuePq3x1oU6IzgCFi7ntrGId1+BcVDeRw5s392P0G3kvv/VABI4L2A==}
    peerDependencies:
      '@shopify/react-native-skia': '>=1.2.3'
      react: '*'
      react-native: '*'
      react-native-gesture-handler: '>=2.0.0'
      react-native-reanimated: '>=3.0.0'

  vlq@1.0.1:
    resolution: {integrity: sha512-gQpnTgkubC6hQgdIcRdYGDSDc+SaujOdyesZQMv6JlfQee/9Mp0Qhnys6WxDWvQnL5WZdT7o2Ul187aSt0Rq+w==}

  w3c-xmlserializer@4.0.0:
    resolution: {integrity: sha512-d+BFHzbiCx6zGfz0HyQ6Rg69w9k19nviJspaj4yNscGjrHu94sVP+aRm75yEbCh+r2/yR+7q6hux9LVtbuTGBw==}
    engines: {node: '>=14'}

  walker@1.0.8:
    resolution: {integrity: sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==}

  warn-once@0.1.1:
    resolution: {integrity: sha512-VkQZJbO8zVImzYFteBXvBOZEl1qL175WH8VmZcxF2fZAoudNhNDvHi+doCaAEdU2l2vtcIwa2zn0QK5+I1HQ3Q==}

  watchpack@2.4.2:
    resolution: {integrity: sha512-TnbFSbcOCcDgjZ4piURLCbJ3nJhznVh9kw6F6iokjiFPl8ONxe9A6nMDVXDiNbrSfLILs6vB07F7wLBrwPYzJw==}
    engines: {node: '>=10.13.0'}

  wcwidth@1.0.1:
    resolution: {integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==}

  webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==}

  webidl-conversions@5.0.0:
    resolution: {integrity: sha512-VlZwKPCkYKxQgeSbH5EyngOmRp7Ww7I9rQLERETtf5ofd9pGeswWiOtogpEO850jziPRarreGxn5QIiTqpb2wA==}
    engines: {node: '>=8'}

  webidl-conversions@7.0.0:
    resolution: {integrity: sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==}
    engines: {node: '>=12'}

  webpack-sources@3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    engines: {node: '>=10.13.0'}

  webpack@5.98.0:
    resolution: {integrity: sha512-UFynvx+gM44Gv9qFgj0acCQK2VE1CtdfwFdimkapco3hlPCJ/zeq73n2yVKimVbtm+TnApIugGhLJnkU6gjYXA==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true

  whatwg-encoding@2.0.0:
    resolution: {integrity: sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==}
    engines: {node: '>=12'}

  whatwg-fetch@3.6.20:
    resolution: {integrity: sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==}

  whatwg-mimetype@3.0.0:
    resolution: {integrity: sha512-nt+N2dzIutVRxARx1nghPKGv1xHikU7HKdfafKkLNLindmPU/ch3U31NOCGGA/dmPcmb1VlofO0vnKAcsm0o/Q==}
    engines: {node: '>=12'}

  whatwg-url-without-unicode@8.0.0-3:
    resolution: {integrity: sha512-HoKuzZrUlgpz35YO27XgD28uh/WJH4B0+3ttFqRo//lmq+9T/mIOJ6kqmINI9HpUpz1imRC/nR/lxKpJiv0uig==}
    engines: {node: '>=10'}

  whatwg-url@11.0.0:
    resolution: {integrity: sha512-RKT8HExMpoYx4igMiVMY83lN6UeITKJlBQ+vR/8ZJ8OCdSiN3RwCq+9gH0+Xzj0+5IrM6i4j/6LuvzbZIQgEcQ==}
    engines: {node: '>=12'}

  whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==}

  which-boxed-primitive@1.1.1:
    resolution: {integrity: sha512-TbX3mj8n0odCBFVlY8AxkqcHASw3L60jIuF8jFP78az3C2YhmGvqbHBpAjTRH2/xqYunrJ9g1jSyjCjpoWzIAA==}
    engines: {node: '>= 0.4'}

  which-builtin-type@1.2.1:
    resolution: {integrity: sha512-6iBczoX+kDQ7a3+YJBnh3T+KZRxM/iYNPXicqk66/Qfm1b93iu+yOImkg0zHbj5LNOcNv1TEADiZ0xa34B4q6Q==}
    engines: {node: '>= 0.4'}

  which-collection@1.0.2:
    resolution: {integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==}
    engines: {node: '>= 0.4'}

  which-typed-array@1.1.18:
    resolution: {integrity: sha512-qEcY+KJYlWyLH9vNbsr6/5j59AXk5ni5aakf8ldzBvGde6Iz4sxZGkJyWSAueTG7QhOvNRYb1lDdFmL5Td0QKA==}
    engines: {node: '>= 0.4'}

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  wonka@6.3.4:
    resolution: {integrity: sha512-CjpbqNtBGNAeyNS/9W6q3kSkKE52+FjIj7AkFlLr11s/VWGUu6a2CdYSdGxocIhIVjaW/zchesBQUKPVU69Cqg==}

  word-wrap@1.2.5:
    resolution: {integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==}
    engines: {node: '>=0.10.0'}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  write-file-atomic@4.0.2:
    resolution: {integrity: sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  ws@6.2.3:
    resolution: {integrity: sha512-jmTjYU0j60B+vHey6TfR3Z7RD61z/hmxBS3VMSGIrroOWXQEneK1zNuotOUrGyBHQj0yrpsLHPWtigEFd13ndA==}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@7.5.10:
    resolution: {integrity: sha512-+dbF1tHwZpXcbOJdVOkzLDxZP1ailvSxM6ZweXTegylPny803bFhA+vqBYw4s31NSAk4S2Qz+AKXK9a4wkdjcQ==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@8.18.1:
    resolution: {integrity: sha512-RKW2aJZMXeMxVpnZ6bck+RswznaxmzdULiBr6KY7XkTnW8uvt0iT9H5DkHUChXrc+uurzwa0rVI16n/Xzjdz1w==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xcode@3.0.1:
    resolution: {integrity: sha512-kCz5k7J7XbJtjABOvkc5lJmkiDh8VhjVCGNiqdKCscmVpdVUpEAyXv1xmCLkQJ5dsHqx3IPO4XW+NTDhU/fatA==}
    engines: {node: '>=10.0.0'}

  xml-name-validator@4.0.0:
    resolution: {integrity: sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==}
    engines: {node: '>=12'}

  xml2js@0.6.0:
    resolution: {integrity: sha512-eLTh0kA8uHceqesPqSE+VvO1CDDJWMwlQfB6LuN6T8w6MaDJ8Txm8P7s5cHD0miF0V+GGTZrDQfxPZQVsur33w==}
    engines: {node: '>=4.0.0'}

  xmlbuilder@11.0.1:
    resolution: {integrity: sha512-fDlsI/kFEx7gLvbecc0/ohLG50fugQp8ryHzMTuW9vSa1GJ0XYWKnhsUx7oie3G98+r56aTQIUB4kht42R3JvA==}
    engines: {node: '>=4.0'}

  xmlbuilder@15.1.1:
    resolution: {integrity: sha512-yMqGBqtXyeN1e3TGYvgNgDVZ3j84W4cwkOXQswghol6APgZWaff9lnbvN7MHYJOiXsvGPXtjTYJEiC9J2wv9Eg==}
    engines: {node: '>=8.0'}

  xmlchars@2.2.0:
    resolution: {integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==}

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@5.0.0:
    resolution: {integrity: sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw==}
    engines: {node: '>=18'}

  yaml@2.7.0:
    resolution: {integrity: sha512-+hSoy/QHluxmC9kCIJyL/uyFmLmc+e5CFR5Wa+bpIhIj85LVb9ZH2nVnqrHoSvKogwODv0ClqZkmiSSaIH5LTA==}
    engines: {node: '>= 14'}
    hasBin: true

  yargs-parser@20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}

  yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}

  yargs@16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==}
    engines: {node: '>=10'}

  yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}

  yn@3.1.1:
    resolution: {integrity: sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==}
    engines: {node: '>=6'}

  yocto-queue@0.1.0:
    resolution: {integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==}
    engines: {node: '>=10'}

snapshots:

  '@0no-co/graphql.web@1.1.1(graphql@15.10.1)':
    optionalDependencies:
      graphql: 15.10.1

  '@ampproject/remapping@2.3.0':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@aws-amplify/analytics@7.0.80(@aws-amplify/core@6.11.4)':
    dependencies:
      '@aws-amplify/core': 6.11.4
      '@aws-sdk/client-firehose': 3.621.0
      '@aws-sdk/client-kinesis': 3.621.0
      '@aws-sdk/client-personalize-events': 3.621.0
      '@smithy/util-utf8': 2.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-amplify/api-graphql@4.7.15':
    dependencies:
      '@aws-amplify/api-rest': 4.1.4(@aws-amplify/core@6.11.4)
      '@aws-amplify/core': 6.11.4
      '@aws-amplify/data-schema': 1.18.0
      '@aws-sdk/types': 3.387.0
      graphql: 15.8.0
      rxjs: 7.8.2
      tslib: 2.8.1
      uuid: 9.0.1

  '@aws-amplify/api-rest@4.1.4(@aws-amplify/core@6.11.4)':
    dependencies:
      '@aws-amplify/core': 6.11.4
      tslib: 2.8.1

  '@aws-amplify/api@6.3.11(@aws-amplify/core@6.11.4)':
    dependencies:
      '@aws-amplify/api-graphql': 4.7.15
      '@aws-amplify/api-rest': 4.1.4(@aws-amplify/core@6.11.4)
      '@aws-amplify/core': 6.11.4
      '@aws-amplify/data-schema': 1.18.0
      rxjs: 7.8.2
      tslib: 2.8.1

  '@aws-amplify/auth@6.12.4(@aws-amplify/core@6.11.4)':
    dependencies:
      '@aws-amplify/core': 6.11.4
      '@aws-crypto/sha256-js': 5.2.0
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@aws-amplify/core@6.11.4':
    dependencies:
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/types': 3.398.0
      '@smithy/util-hex-encoding': 2.0.0
      '@types/uuid': 9.0.8
      js-cookie: 3.0.5
      rxjs: 7.8.2
      tslib: 2.8.1
      uuid: 9.0.1

  '@aws-amplify/data-schema-types@1.2.0':
    dependencies:
      graphql: 15.8.0
      rxjs: 7.8.2

  '@aws-amplify/data-schema@1.18.0':
    dependencies:
      '@aws-amplify/data-schema-types': 1.2.0
      '@smithy/util-base64': 3.0.0
      '@types/aws-lambda': 8.10.147
      '@types/json-schema': 7.0.15
      rxjs: 7.8.2

  '@aws-amplify/datastore@5.0.82(@aws-amplify/core@6.11.4)':
    dependencies:
      '@aws-amplify/api': 6.3.11(@aws-amplify/core@6.11.4)
      '@aws-amplify/api-graphql': 4.7.15
      '@aws-amplify/core': 6.11.4
      buffer: 4.9.2
      idb: 5.0.6
      immer: 9.0.6
      rxjs: 7.8.2
      ulid: 2.3.0

  '@aws-amplify/notifications@2.0.80(@aws-amplify/core@6.11.4)':
    dependencies:
      '@aws-amplify/core': 6.11.4
      '@aws-sdk/types': 3.398.0
      lodash: 4.17.21
      tslib: 2.8.1

  '@aws-amplify/storage@6.8.4(@aws-amplify/core@6.11.4)':
    dependencies:
      '@aws-amplify/core': 6.11.4
      '@aws-sdk/types': 3.398.0
      '@smithy/md5-js': 2.0.7
      buffer: 4.9.2
      crc-32: 1.2.2
      fast-xml-parser: 4.5.3
      tslib: 2.8.1

  '@aws-crypto/crc32@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.734.0
      tslib: 2.8.1

  '@aws-crypto/sha256-browser@5.2.0':
    dependencies:
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-crypto/supports-web-crypto': 5.2.0
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.734.0
      '@aws-sdk/util-locate-window': 3.723.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.8.1

  '@aws-crypto/sha256-js@5.2.0':
    dependencies:
      '@aws-crypto/util': 5.2.0
      '@aws-sdk/types': 3.734.0
      tslib: 2.8.1

  '@aws-crypto/supports-web-crypto@5.2.0':
    dependencies:
      tslib: 2.8.1

  '@aws-crypto/util@5.2.0':
    dependencies:
      '@aws-sdk/types': 3.734.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.8.1

  '@aws-sdk/client-firehose@3.621.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sso-oidc': 3.621.0(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/client-sts': 3.621.0
      '@aws-sdk/core': 3.621.0
      '@aws-sdk/credential-provider-node': 3.621.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/middleware-host-header': 3.620.0
      '@aws-sdk/middleware-logger': 3.609.0
      '@aws-sdk/middleware-recursion-detection': 3.620.0
      '@aws-sdk/middleware-user-agent': 3.620.0
      '@aws-sdk/region-config-resolver': 3.614.0
      '@aws-sdk/types': 3.609.0
      '@aws-sdk/util-endpoints': 3.614.0
      '@aws-sdk/util-user-agent-browser': 3.609.0
      '@aws-sdk/util-user-agent-node': 3.614.0
      '@smithy/config-resolver': 3.0.13
      '@smithy/core': 2.5.7
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/hash-node': 3.0.11
      '@smithy/invalid-dependency': 3.0.11
      '@smithy/middleware-content-length': 3.0.13
      '@smithy/middleware-endpoint': 3.2.8
      '@smithy/middleware-retry': 3.0.34
      '@smithy/middleware-serde': 3.0.11
      '@smithy/middleware-stack': 3.0.11
      '@smithy/node-config-provider': 3.1.12
      '@smithy/node-http-handler': 3.3.3
      '@smithy/protocol-http': 4.1.8
      '@smithy/smithy-client': 3.7.0
      '@smithy/types': 3.7.2
      '@smithy/url-parser': 3.0.11
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.34
      '@smithy/util-defaults-mode-node': 3.0.34
      '@smithy/util-endpoints': 2.1.7
      '@smithy/util-middleware': 3.0.11
      '@smithy/util-retry': 3.0.11
      '@smithy/util-utf8': 3.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-kinesis@3.621.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sso-oidc': 3.621.0(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/client-sts': 3.621.0
      '@aws-sdk/core': 3.621.0
      '@aws-sdk/credential-provider-node': 3.621.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/middleware-host-header': 3.620.0
      '@aws-sdk/middleware-logger': 3.609.0
      '@aws-sdk/middleware-recursion-detection': 3.620.0
      '@aws-sdk/middleware-user-agent': 3.620.0
      '@aws-sdk/region-config-resolver': 3.614.0
      '@aws-sdk/types': 3.609.0
      '@aws-sdk/util-endpoints': 3.614.0
      '@aws-sdk/util-user-agent-browser': 3.609.0
      '@aws-sdk/util-user-agent-node': 3.614.0
      '@smithy/config-resolver': 3.0.13
      '@smithy/core': 2.5.7
      '@smithy/eventstream-serde-browser': 3.0.14
      '@smithy/eventstream-serde-config-resolver': 3.0.11
      '@smithy/eventstream-serde-node': 3.0.13
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/hash-node': 3.0.11
      '@smithy/invalid-dependency': 3.0.11
      '@smithy/middleware-content-length': 3.0.13
      '@smithy/middleware-endpoint': 3.2.8
      '@smithy/middleware-retry': 3.0.34
      '@smithy/middleware-serde': 3.0.11
      '@smithy/middleware-stack': 3.0.11
      '@smithy/node-config-provider': 3.1.12
      '@smithy/node-http-handler': 3.3.3
      '@smithy/protocol-http': 4.1.8
      '@smithy/smithy-client': 3.7.0
      '@smithy/types': 3.7.2
      '@smithy/url-parser': 3.0.11
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.34
      '@smithy/util-defaults-mode-node': 3.0.34
      '@smithy/util-endpoints': 2.1.7
      '@smithy/util-middleware': 3.0.11
      '@smithy/util-retry': 3.0.11
      '@smithy/util-utf8': 3.0.0
      '@smithy/util-waiter': 3.2.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-personalize-events@3.621.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sso-oidc': 3.621.0(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/client-sts': 3.621.0
      '@aws-sdk/core': 3.621.0
      '@aws-sdk/credential-provider-node': 3.621.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/middleware-host-header': 3.620.0
      '@aws-sdk/middleware-logger': 3.609.0
      '@aws-sdk/middleware-recursion-detection': 3.620.0
      '@aws-sdk/middleware-user-agent': 3.620.0
      '@aws-sdk/region-config-resolver': 3.614.0
      '@aws-sdk/types': 3.609.0
      '@aws-sdk/util-endpoints': 3.614.0
      '@aws-sdk/util-user-agent-browser': 3.609.0
      '@aws-sdk/util-user-agent-node': 3.614.0
      '@smithy/config-resolver': 3.0.13
      '@smithy/core': 2.5.7
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/hash-node': 3.0.11
      '@smithy/invalid-dependency': 3.0.11
      '@smithy/middleware-content-length': 3.0.13
      '@smithy/middleware-endpoint': 3.2.8
      '@smithy/middleware-retry': 3.0.34
      '@smithy/middleware-serde': 3.0.11
      '@smithy/middleware-stack': 3.0.11
      '@smithy/node-config-provider': 3.1.12
      '@smithy/node-http-handler': 3.3.3
      '@smithy/protocol-http': 4.1.8
      '@smithy/smithy-client': 3.7.0
      '@smithy/types': 3.7.2
      '@smithy/url-parser': 3.0.11
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.34
      '@smithy/util-defaults-mode-node': 3.0.34
      '@smithy/util-endpoints': 2.1.7
      '@smithy/util-middleware': 3.0.11
      '@smithy/util-retry': 3.0.11
      '@smithy/util-utf8': 3.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0)':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sts': 3.621.0
      '@aws-sdk/core': 3.621.0
      '@aws-sdk/credential-provider-node': 3.621.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/middleware-host-header': 3.620.0
      '@aws-sdk/middleware-logger': 3.609.0
      '@aws-sdk/middleware-recursion-detection': 3.620.0
      '@aws-sdk/middleware-user-agent': 3.620.0
      '@aws-sdk/region-config-resolver': 3.614.0
      '@aws-sdk/types': 3.609.0
      '@aws-sdk/util-endpoints': 3.614.0
      '@aws-sdk/util-user-agent-browser': 3.609.0
      '@aws-sdk/util-user-agent-node': 3.614.0
      '@smithy/config-resolver': 3.0.13
      '@smithy/core': 2.5.7
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/hash-node': 3.0.11
      '@smithy/invalid-dependency': 3.0.11
      '@smithy/middleware-content-length': 3.0.13
      '@smithy/middleware-endpoint': 3.2.8
      '@smithy/middleware-retry': 3.0.34
      '@smithy/middleware-serde': 3.0.11
      '@smithy/middleware-stack': 3.0.11
      '@smithy/node-config-provider': 3.1.12
      '@smithy/node-http-handler': 3.3.3
      '@smithy/protocol-http': 4.1.8
      '@smithy/smithy-client': 3.7.0
      '@smithy/types': 3.7.2
      '@smithy/url-parser': 3.0.11
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.34
      '@smithy/util-defaults-mode-node': 3.0.34
      '@smithy/util-endpoints': 2.1.7
      '@smithy/util-middleware': 3.0.11
      '@smithy/util-retry': 3.0.11
      '@smithy/util-utf8': 3.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sso@3.621.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/core': 3.621.0
      '@aws-sdk/middleware-host-header': 3.620.0
      '@aws-sdk/middleware-logger': 3.609.0
      '@aws-sdk/middleware-recursion-detection': 3.620.0
      '@aws-sdk/middleware-user-agent': 3.620.0
      '@aws-sdk/region-config-resolver': 3.614.0
      '@aws-sdk/types': 3.609.0
      '@aws-sdk/util-endpoints': 3.614.0
      '@aws-sdk/util-user-agent-browser': 3.609.0
      '@aws-sdk/util-user-agent-node': 3.614.0
      '@smithy/config-resolver': 3.0.13
      '@smithy/core': 2.5.7
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/hash-node': 3.0.11
      '@smithy/invalid-dependency': 3.0.11
      '@smithy/middleware-content-length': 3.0.13
      '@smithy/middleware-endpoint': 3.2.8
      '@smithy/middleware-retry': 3.0.34
      '@smithy/middleware-serde': 3.0.11
      '@smithy/middleware-stack': 3.0.11
      '@smithy/node-config-provider': 3.1.12
      '@smithy/node-http-handler': 3.3.3
      '@smithy/protocol-http': 4.1.8
      '@smithy/smithy-client': 3.7.0
      '@smithy/types': 3.7.2
      '@smithy/url-parser': 3.0.11
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.34
      '@smithy/util-defaults-mode-node': 3.0.34
      '@smithy/util-endpoints': 2.1.7
      '@smithy/util-middleware': 3.0.11
      '@smithy/util-retry': 3.0.11
      '@smithy/util-utf8': 3.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/client-sts@3.621.0':
    dependencies:
      '@aws-crypto/sha256-browser': 5.2.0
      '@aws-crypto/sha256-js': 5.2.0
      '@aws-sdk/client-sso-oidc': 3.621.0(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/core': 3.621.0
      '@aws-sdk/credential-provider-node': 3.621.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/middleware-host-header': 3.620.0
      '@aws-sdk/middleware-logger': 3.609.0
      '@aws-sdk/middleware-recursion-detection': 3.620.0
      '@aws-sdk/middleware-user-agent': 3.620.0
      '@aws-sdk/region-config-resolver': 3.614.0
      '@aws-sdk/types': 3.609.0
      '@aws-sdk/util-endpoints': 3.614.0
      '@aws-sdk/util-user-agent-browser': 3.609.0
      '@aws-sdk/util-user-agent-node': 3.614.0
      '@smithy/config-resolver': 3.0.13
      '@smithy/core': 2.5.7
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/hash-node': 3.0.11
      '@smithy/invalid-dependency': 3.0.11
      '@smithy/middleware-content-length': 3.0.13
      '@smithy/middleware-endpoint': 3.2.8
      '@smithy/middleware-retry': 3.0.34
      '@smithy/middleware-serde': 3.0.11
      '@smithy/middleware-stack': 3.0.11
      '@smithy/node-config-provider': 3.1.12
      '@smithy/node-http-handler': 3.3.3
      '@smithy/protocol-http': 4.1.8
      '@smithy/smithy-client': 3.7.0
      '@smithy/types': 3.7.2
      '@smithy/url-parser': 3.0.11
      '@smithy/util-base64': 3.0.0
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-body-length-node': 3.0.0
      '@smithy/util-defaults-mode-browser': 3.0.34
      '@smithy/util-defaults-mode-node': 3.0.34
      '@smithy/util-endpoints': 2.1.7
      '@smithy/util-middleware': 3.0.11
      '@smithy/util-retry': 3.0.11
      '@smithy/util-utf8': 3.0.0
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  '@aws-sdk/core@3.621.0':
    dependencies:
      '@smithy/core': 2.5.7
      '@smithy/node-config-provider': 3.1.12
      '@smithy/protocol-http': 4.1.8
      '@smithy/signature-v4': 4.2.4
      '@smithy/smithy-client': 3.7.0
      '@smithy/types': 3.7.2
      '@smithy/util-middleware': 3.0.11
      fast-xml-parser: 4.4.1
      tslib: 2.8.1

  '@aws-sdk/credential-provider-env@3.620.1':
    dependencies:
      '@aws-sdk/types': 3.609.0
      '@smithy/property-provider': 3.1.11
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@aws-sdk/credential-provider-http@3.621.0':
    dependencies:
      '@aws-sdk/types': 3.609.0
      '@smithy/fetch-http-handler': 3.2.9
      '@smithy/node-http-handler': 3.3.3
      '@smithy/property-provider': 3.1.11
      '@smithy/protocol-http': 4.1.8
      '@smithy/smithy-client': 3.7.0
      '@smithy/types': 3.7.2
      '@smithy/util-stream': 3.3.4
      tslib: 2.8.1

  '@aws-sdk/credential-provider-ini@3.621.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))(@aws-sdk/client-sts@3.621.0)':
    dependencies:
      '@aws-sdk/client-sts': 3.621.0
      '@aws-sdk/credential-provider-env': 3.620.1
      '@aws-sdk/credential-provider-http': 3.621.0
      '@aws-sdk/credential-provider-process': 3.620.1
      '@aws-sdk/credential-provider-sso': 3.621.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))
      '@aws-sdk/credential-provider-web-identity': 3.621.0(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/types': 3.609.0
      '@smithy/credential-provider-imds': 3.2.8
      '@smithy/property-provider': 3.1.11
      '@smithy/shared-ini-file-loader': 3.1.12
      '@smithy/types': 3.7.2
      tslib: 2.8.1
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - aws-crt

  '@aws-sdk/credential-provider-node@3.621.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))(@aws-sdk/client-sts@3.621.0)':
    dependencies:
      '@aws-sdk/credential-provider-env': 3.620.1
      '@aws-sdk/credential-provider-http': 3.621.0
      '@aws-sdk/credential-provider-ini': 3.621.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/credential-provider-process': 3.620.1
      '@aws-sdk/credential-provider-sso': 3.621.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))
      '@aws-sdk/credential-provider-web-identity': 3.621.0(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/types': 3.609.0
      '@smithy/credential-provider-imds': 3.2.8
      '@smithy/property-provider': 3.1.11
      '@smithy/shared-ini-file-loader': 3.1.12
      '@smithy/types': 3.7.2
      tslib: 2.8.1
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - '@aws-sdk/client-sts'
      - aws-crt

  '@aws-sdk/credential-provider-process@3.620.1':
    dependencies:
      '@aws-sdk/types': 3.609.0
      '@smithy/property-provider': 3.1.11
      '@smithy/shared-ini-file-loader': 3.1.12
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@aws-sdk/credential-provider-sso@3.621.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))':
    dependencies:
      '@aws-sdk/client-sso': 3.621.0
      '@aws-sdk/token-providers': 3.614.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))
      '@aws-sdk/types': 3.609.0
      '@smithy/property-provider': 3.1.11
      '@smithy/shared-ini-file-loader': 3.1.12
      '@smithy/types': 3.7.2
      tslib: 2.8.1
    transitivePeerDependencies:
      - '@aws-sdk/client-sso-oidc'
      - aws-crt

  '@aws-sdk/credential-provider-web-identity@3.621.0(@aws-sdk/client-sts@3.621.0)':
    dependencies:
      '@aws-sdk/client-sts': 3.621.0
      '@aws-sdk/types': 3.609.0
      '@smithy/property-provider': 3.1.11
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@aws-sdk/middleware-host-header@3.620.0':
    dependencies:
      '@aws-sdk/types': 3.609.0
      '@smithy/protocol-http': 4.1.8
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@aws-sdk/middleware-logger@3.609.0':
    dependencies:
      '@aws-sdk/types': 3.609.0
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@aws-sdk/middleware-recursion-detection@3.620.0':
    dependencies:
      '@aws-sdk/types': 3.609.0
      '@smithy/protocol-http': 4.1.8
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@aws-sdk/middleware-user-agent@3.620.0':
    dependencies:
      '@aws-sdk/types': 3.609.0
      '@aws-sdk/util-endpoints': 3.614.0
      '@smithy/protocol-http': 4.1.8
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@aws-sdk/region-config-resolver@3.614.0':
    dependencies:
      '@aws-sdk/types': 3.609.0
      '@smithy/node-config-provider': 3.1.12
      '@smithy/types': 3.7.2
      '@smithy/util-config-provider': 3.0.0
      '@smithy/util-middleware': 3.0.11
      tslib: 2.8.1

  '@aws-sdk/token-providers@3.614.0(@aws-sdk/client-sso-oidc@3.621.0(@aws-sdk/client-sts@3.621.0))':
    dependencies:
      '@aws-sdk/client-sso-oidc': 3.621.0(@aws-sdk/client-sts@3.621.0)
      '@aws-sdk/types': 3.609.0
      '@smithy/property-provider': 3.1.11
      '@smithy/shared-ini-file-loader': 3.1.12
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@aws-sdk/types@3.387.0':
    dependencies:
      '@smithy/types': 2.12.0
      tslib: 2.8.1

  '@aws-sdk/types@3.398.0':
    dependencies:
      '@smithy/types': 2.12.0
      tslib: 2.8.1

  '@aws-sdk/types@3.609.0':
    dependencies:
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@aws-sdk/types@3.734.0':
    dependencies:
      '@smithy/types': 4.1.0
      tslib: 2.8.1

  '@aws-sdk/util-endpoints@3.614.0':
    dependencies:
      '@aws-sdk/types': 3.609.0
      '@smithy/types': 3.7.2
      '@smithy/util-endpoints': 2.1.7
      tslib: 2.8.1

  '@aws-sdk/util-locate-window@3.723.0':
    dependencies:
      tslib: 2.8.1

  '@aws-sdk/util-user-agent-browser@3.609.0':
    dependencies:
      '@aws-sdk/types': 3.609.0
      '@smithy/types': 3.7.2
      bowser: 2.11.0
      tslib: 2.8.1

  '@aws-sdk/util-user-agent-node@3.614.0':
    dependencies:
      '@aws-sdk/types': 3.609.0
      '@smithy/node-config-provider': 3.1.12
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@babel/code-frame@7.10.4':
    dependencies:
      '@babel/highlight': 7.25.9

  '@babel/code-frame@7.26.2':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/compat-data@7.26.8': {}

  '@babel/core@7.26.9':
    dependencies:
      '@ampproject/remapping': 2.3.0
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.9
      '@babel/helper-compilation-targets': 7.26.5
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.9)
      '@babel/helpers': 7.26.9
      '@babel/parser': 7.26.9
      '@babel/template': 7.26.9
      '@babel/traverse': 7.26.9
      '@babel/types': 7.26.9
      convert-source-map: 2.0.0
      debug: 4.4.0
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.26.9':
    dependencies:
      '@babel/parser': 7.26.9
      '@babel/types': 7.26.9
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25
      jsesc: 3.1.0

  '@babel/helper-annotate-as-pure@7.25.9':
    dependencies:
      '@babel/types': 7.26.9

  '@babel/helper-compilation-targets@7.26.5':
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/helper-validator-option': 7.25.9
      browserslist: 4.24.4
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.26.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/helper-replace-supers': 7.26.5(@babel/core@7.26.9)
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/traverse': 7.26.9
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-create-regexp-features-plugin@7.26.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-annotate-as-pure': 7.25.9
      regexpu-core: 6.2.0
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.6.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-compilation-targets': 7.26.5
      '@babel/helper-plugin-utils': 7.26.5
      debug: 4.4.0
      lodash.debounce: 4.0.8
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-member-expression-to-functions@7.25.9':
    dependencies:
      '@babel/traverse': 7.26.9
      '@babel/types': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-imports@7.25.9':
    dependencies:
      '@babel/traverse': 7.26.9
      '@babel/types': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-module-transforms@7.26.0(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-optimise-call-expression@7.25.9':
    dependencies:
      '@babel/types': 7.26.9

  '@babel/helper-plugin-utils@7.26.5': {}

  '@babel/helper-remap-async-to-generator@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-wrap-function': 7.25.9
      '@babel/traverse': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-replace-supers@7.26.5(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-member-expression-to-functions': 7.25.9
      '@babel/helper-optimise-call-expression': 7.25.9
      '@babel/traverse': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-skip-transparent-expression-wrappers@7.25.9':
    dependencies:
      '@babel/traverse': 7.26.9
      '@babel/types': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-string-parser@7.25.9': {}

  '@babel/helper-validator-identifier@7.25.9': {}

  '@babel/helper-validator-option@7.25.9': {}

  '@babel/helper-wrap-function@7.25.9':
    dependencies:
      '@babel/template': 7.26.9
      '@babel/traverse': 7.26.9
      '@babel/types': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/helpers@7.26.9':
    dependencies:
      '@babel/template': 7.26.9
      '@babel/types': 7.26.9

  '@babel/highlight@7.25.9':
    dependencies:
      '@babel/helper-validator-identifier': 7.25.9
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.1.1

  '@babel/parser@7.26.9':
    dependencies:
      '@babel/types': 7.26.9

  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/traverse': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/plugin-transform-optional-chaining': 7.25.9(@babel/core@7.26.9)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/traverse': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-decorators@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-class-features-plugin': 7.26.9(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/plugin-syntax-decorators': 7.25.9(@babel/core@7.26.9)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-proposal-export-default-from@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9

  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-bigint@7.8.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-decorators@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-export-default-from@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-flow@7.26.0(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-import-assertions@7.26.0(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-import-attributes@7.26.0(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-jsx@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-typescript@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-regexp-features-plugin': 7.26.3(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-arrow-functions@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-async-generator-functions@7.26.8(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-remap-async-to-generator': 7.25.9(@babel/core@7.26.9)
      '@babel/traverse': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-async-to-generator@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-remap-async-to-generator': 7.25.9(@babel/core@7.26.9)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-block-scoped-functions@7.26.5(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-block-scoping@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-class-properties@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-class-features-plugin': 7.26.9(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-class-static-block@7.26.0(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-class-features-plugin': 7.26.9(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-classes@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-compilation-targets': 7.26.5
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-replace-supers': 7.26.5(@babel/core@7.26.9)
      '@babel/traverse': 7.26.9
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-computed-properties@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/template': 7.26.9

  '@babel/plugin-transform-destructuring@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-dotall-regex@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-regexp-features-plugin': 7.26.3(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-duplicate-keys@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-regexp-features-plugin': 7.26.3(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-dynamic-import@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-exponentiation-operator@7.26.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-export-namespace-from@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-flow-strip-types@7.26.5(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/plugin-syntax-flow': 7.26.0(@babel/core@7.26.9)

  '@babel/plugin-transform-for-of@7.26.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-function-name@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-compilation-targets': 7.26.5
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/traverse': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-json-strings@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-literals@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-logical-assignment-operators@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-member-expression-literals@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-modules-amd@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-commonjs@7.26.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-systemjs@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-validator-identifier': 7.25.9
      '@babel/traverse': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-modules-umd@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-module-transforms': 7.26.0(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-named-capturing-groups-regex@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-regexp-features-plugin': 7.26.3(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-new-target@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-nullish-coalescing-operator@7.26.6(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-numeric-separator@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-object-rest-spread@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-compilation-targets': 7.26.5
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/plugin-transform-parameters': 7.25.9(@babel/core@7.26.9)

  '@babel/plugin-transform-object-super@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-replace-supers': 7.26.5(@babel/core@7.26.9)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-optional-catch-binding@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-optional-chaining@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-parameters@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-private-methods@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-class-features-plugin': 7.26.9(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-private-property-in-object@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-create-class-features-plugin': 7.26.9(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-property-literals@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-react-display-name@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-react-jsx-development@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/plugin-transform-react-jsx': 7.25.9(@babel/core@7.26.9)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-react-jsx-self@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-react-jsx-source@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-react-jsx@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.9)
      '@babel/types': 7.26.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-react-pure-annotations@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-regenerator@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      regenerator-transform: 0.15.2

  '@babel/plugin-transform-regexp-modifiers@7.26.0(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-regexp-features-plugin': 7.26.3(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-reserved-words@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-runtime@7.26.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-module-imports': 7.25.9
      '@babel/helper-plugin-utils': 7.26.5
      babel-plugin-polyfill-corejs2: 0.4.12(@babel/core@7.26.9)
      babel-plugin-polyfill-corejs3: 0.10.6(@babel/core@7.26.9)
      babel-plugin-polyfill-regenerator: 0.6.3(@babel/core@7.26.9)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-shorthand-properties@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-spread@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-sticky-regex@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-template-literals@7.26.8(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-typeof-symbol@7.26.7(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-typescript@7.26.8(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-annotate-as-pure': 7.25.9
      '@babel/helper-create-class-features-plugin': 7.26.9(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.25.9
      '@babel/plugin-syntax-typescript': 7.25.9(@babel/core@7.26.9)
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-unicode-escapes@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-unicode-property-regex@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-regexp-features-plugin': 7.26.3(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-unicode-regex@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-regexp-features-plugin': 7.26.3(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/plugin-transform-unicode-sets-regex@7.25.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-create-regexp-features-plugin': 7.26.3(@babel/core@7.26.9)
      '@babel/helper-plugin-utils': 7.26.5

  '@babel/preset-env@7.26.9(@babel/core@7.26.9)':
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/core': 7.26.9
      '@babel/helper-compilation-targets': 7.26.5
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-validator-option': 7.25.9
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-bugfix-safari-class-field-initializer-scope': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2(@babel/core@7.26.9)
      '@babel/plugin-syntax-import-assertions': 7.26.0(@babel/core@7.26.9)
      '@babel/plugin-syntax-import-attributes': 7.26.0(@babel/core@7.26.9)
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6(@babel/core@7.26.9)
      '@babel/plugin-transform-arrow-functions': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-async-generator-functions': 7.26.8(@babel/core@7.26.9)
      '@babel/plugin-transform-async-to-generator': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-block-scoped-functions': 7.26.5(@babel/core@7.26.9)
      '@babel/plugin-transform-block-scoping': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-class-properties': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-class-static-block': 7.26.0(@babel/core@7.26.9)
      '@babel/plugin-transform-classes': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-computed-properties': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-destructuring': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-dotall-regex': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-duplicate-keys': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-duplicate-named-capturing-groups-regex': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-dynamic-import': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-exponentiation-operator': 7.26.3(@babel/core@7.26.9)
      '@babel/plugin-transform-export-namespace-from': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-for-of': 7.26.9(@babel/core@7.26.9)
      '@babel/plugin-transform-function-name': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-json-strings': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-literals': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-logical-assignment-operators': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-member-expression-literals': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-modules-amd': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-modules-commonjs': 7.26.3(@babel/core@7.26.9)
      '@babel/plugin-transform-modules-systemjs': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-modules-umd': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-new-target': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.26.6(@babel/core@7.26.9)
      '@babel/plugin-transform-numeric-separator': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-object-rest-spread': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-object-super': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-optional-catch-binding': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-optional-chaining': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-parameters': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-private-methods': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-private-property-in-object': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-property-literals': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-regenerator': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-regexp-modifiers': 7.26.0(@babel/core@7.26.9)
      '@babel/plugin-transform-reserved-words': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-shorthand-properties': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-spread': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-sticky-regex': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-template-literals': 7.26.8(@babel/core@7.26.9)
      '@babel/plugin-transform-typeof-symbol': 7.26.7(@babel/core@7.26.9)
      '@babel/plugin-transform-unicode-escapes': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-unicode-property-regex': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-unicode-regex': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-unicode-sets-regex': 7.25.9(@babel/core@7.26.9)
      '@babel/preset-modules': 0.1.6-no-external-plugins(@babel/core@7.26.9)
      babel-plugin-polyfill-corejs2: 0.4.12(@babel/core@7.26.9)
      babel-plugin-polyfill-corejs3: 0.11.1(@babel/core@7.26.9)
      babel-plugin-polyfill-regenerator: 0.6.3(@babel/core@7.26.9)
      core-js-compat: 3.40.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/types': 7.26.9
      esutils: 2.0.3

  '@babel/preset-react@7.26.3(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-validator-option': 7.25.9
      '@babel/plugin-transform-react-display-name': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-react-jsx': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-react-jsx-development': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-react-pure-annotations': 7.25.9(@babel/core@7.26.9)
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-typescript@7.26.0(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-plugin-utils': 7.26.5
      '@babel/helper-validator-option': 7.25.9
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-modules-commonjs': 7.26.3(@babel/core@7.26.9)
      '@babel/plugin-transform-typescript': 7.26.8(@babel/core@7.26.9)
    transitivePeerDependencies:
      - supports-color

  '@babel/runtime@7.26.9':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/runtime@8.0.0-alpha.17':
    dependencies:
      regenerator-runtime: 0.14.1

  '@babel/template@7.26.9':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/parser': 7.26.9
      '@babel/types': 7.26.9

  '@babel/traverse@7.26.9':
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/generator': 7.26.9
      '@babel/parser': 7.26.9
      '@babel/template': 7.26.9
      '@babel/types': 7.26.9
      debug: 4.4.0
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.26.9':
    dependencies:
      '@babel/helper-string-parser': 7.25.9
      '@babel/helper-validator-identifier': 7.25.9

  '@bcoe/v8-coverage@0.2.3': {}

  '@cspotcode/source-map-support@0.8.1':
    dependencies:
      '@jridgewell/trace-mapping': 0.3.9

  '@egjs/hammerjs@2.0.17':
    dependencies:
      '@types/hammerjs': 2.0.46

  '@eslint-community/eslint-utils@4.4.1(eslint@8.17.0)':
    dependencies:
      eslint: 8.17.0
      eslint-visitor-keys: 3.4.3

  '@eslint-community/regexpp@4.12.1': {}

  '@eslint/eslintrc@1.4.1':
    dependencies:
      ajv: 6.12.6
      debug: 4.4.0
      espree: 9.6.1
      globals: 13.24.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  '@expo-google-fonts/space-grotesk@0.2.3': {}

  '@expo/cli@0.24.13(graphql@15.10.1)':
    dependencies:
      '@0no-co/graphql.web': 1.1.1(graphql@15.10.1)
      '@babel/runtime': 7.26.9
      '@expo/code-signing-certificates': 0.0.5
      '@expo/config': 11.0.10
      '@expo/config-plugins': 10.0.2
      '@expo/devcert': 1.1.4
      '@expo/env': 1.0.5
      '@expo/image-utils': 0.7.4
      '@expo/json-file': 9.1.4
      '@expo/metro-config': 0.20.14
      '@expo/osascript': 2.2.4
      '@expo/package-manager': 1.8.4
      '@expo/plist': 0.3.4
      '@expo/prebuild-config': 9.0.6
      '@expo/spawn-async': 1.7.2
      '@expo/ws-tunnel': 1.0.6
      '@expo/xcpretty': 4.3.2
      '@react-native/dev-middleware': 0.79.2
      '@urql/core': 5.1.0(graphql@15.10.1)
      '@urql/exchange-retry': 1.3.1(@urql/core@5.1.0(graphql@15.10.1))
      accepts: 1.3.8
      arg: 5.0.2
      better-opn: 3.0.2
      bplist-creator: 0.1.0
      bplist-parser: 0.3.2
      chalk: 4.1.2
      ci-info: 3.9.0
      compression: 1.8.0
      connect: 3.7.0
      debug: 4.4.0
      env-editor: 0.4.2
      freeport-async: 2.0.0
      getenv: 1.0.0
      glob: 10.4.5
      lan-network: 0.1.6
      minimatch: 9.0.5
      node-forge: 1.3.1
      npm-package-arg: 11.0.3
      ora: 3.4.0
      picomatch: 3.0.1
      pretty-bytes: 5.6.0
      pretty-format: 29.7.0
      progress: 2.0.3
      prompts: 2.4.2
      qrcode-terminal: 0.11.0
      require-from-string: 2.0.2
      requireg: 0.2.2
      resolve: 1.22.10
      resolve-from: 5.0.0
      resolve.exports: 2.0.3
      semver: 7.7.1
      send: 0.19.0
      slugify: 1.6.6
      source-map-support: 0.5.21
      stacktrace-parser: 0.1.11
      structured-headers: 0.4.1
      tar: 7.4.3
      terminal-link: 2.1.1
      undici: 6.21.2
      wrap-ansi: 7.0.0
      ws: 8.18.1
    transitivePeerDependencies:
      - bufferutil
      - graphql
      - supports-color
      - utf-8-validate

  '@expo/code-signing-certificates@0.0.5':
    dependencies:
      node-forge: 1.3.1
      nullthrows: 1.1.1

  '@expo/config-plugins@10.0.2':
    dependencies:
      '@expo/config-types': 53.0.4
      '@expo/json-file': 9.1.4
      '@expo/plist': 0.3.4
      '@expo/sdk-runtime-versions': 1.0.0
      chalk: 4.1.2
      debug: 4.4.0
      getenv: 1.0.0
      glob: 10.4.5
      resolve-from: 5.0.0
      semver: 7.7.1
      slash: 3.0.0
      slugify: 1.6.6
      xcode: 3.0.1
      xml2js: 0.6.0
    transitivePeerDependencies:
      - supports-color

  '@expo/config-types@53.0.4': {}

  '@expo/config@11.0.10':
    dependencies:
      '@babel/code-frame': 7.10.4
      '@expo/config-plugins': 10.0.2
      '@expo/config-types': 53.0.4
      '@expo/json-file': 9.1.4
      deepmerge: 4.3.1
      getenv: 1.0.0
      glob: 10.4.5
      require-from-string: 2.0.2
      resolve-from: 5.0.0
      resolve-workspace-root: 2.0.0
      semver: 7.7.1
      slugify: 1.6.6
      sucrase: 3.35.0
    transitivePeerDependencies:
      - supports-color

  '@expo/devcert@1.1.4':
    dependencies:
      application-config-path: 0.1.1
      command-exists: 1.2.9
      debug: 3.2.7
      eol: 0.9.1
      get-port: 3.2.0
      glob: 10.4.5
      lodash: 4.17.21
      mkdirp: 0.5.6
      password-prompt: 1.1.3
      sudo-prompt: 8.2.5
      tmp: 0.0.33
      tslib: 2.8.1
    transitivePeerDependencies:
      - supports-color

  '@expo/env@1.0.5':
    dependencies:
      chalk: 4.1.2
      debug: 4.4.0
      dotenv: 16.4.7
      dotenv-expand: 11.0.7
      getenv: 1.0.0
    transitivePeerDependencies:
      - supports-color

  '@expo/fingerprint@0.12.4':
    dependencies:
      '@expo/spawn-async': 1.7.2
      arg: 5.0.2
      chalk: 4.1.2
      debug: 4.4.0
      find-up: 5.0.0
      getenv: 1.0.0
      minimatch: 9.0.5
      p-limit: 3.1.0
      resolve-from: 5.0.0
      semver: 7.7.1
    transitivePeerDependencies:
      - supports-color

  '@expo/image-utils@0.7.4':
    dependencies:
      '@expo/spawn-async': 1.7.2
      chalk: 4.1.2
      getenv: 1.0.0
      jimp-compact: 0.16.1
      parse-png: 2.1.0
      resolve-from: 5.0.0
      semver: 7.7.1
      temp-dir: 2.0.0
      unique-string: 2.0.0

  '@expo/json-file@9.1.4':
    dependencies:
      '@babel/code-frame': 7.10.4
      json5: 2.2.3

  '@expo/metro-config@0.20.14':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/generator': 7.26.9
      '@babel/parser': 7.26.9
      '@babel/types': 7.26.9
      '@expo/config': 11.0.10
      '@expo/env': 1.0.5
      '@expo/json-file': 9.1.4
      '@expo/spawn-async': 1.7.2
      chalk: 4.1.2
      debug: 4.4.0
      dotenv: 16.4.7
      dotenv-expand: 11.0.7
      getenv: 1.0.0
      glob: 10.4.5
      jsc-safe-url: 0.2.4
      lightningcss: 1.27.0
      minimatch: 9.0.5
      postcss: 8.4.49
      resolve-from: 5.0.0
    transitivePeerDependencies:
      - supports-color

  '@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))':
    dependencies:
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  '@expo/osascript@2.2.4':
    dependencies:
      '@expo/spawn-async': 1.7.2
      exec-async: 2.2.0

  '@expo/package-manager@1.8.4':
    dependencies:
      '@expo/json-file': 9.1.4
      '@expo/spawn-async': 1.7.2
      chalk: 4.1.2
      npm-package-arg: 11.0.3
      ora: 3.4.0
      resolve-workspace-root: 2.0.0

  '@expo/plist@0.3.4':
    dependencies:
      '@xmldom/xmldom': 0.8.10
      base64-js: 1.5.1
      xmlbuilder: 15.1.1

  '@expo/prebuild-config@9.0.6':
    dependencies:
      '@expo/config': 11.0.10
      '@expo/config-plugins': 10.0.2
      '@expo/config-types': 53.0.4
      '@expo/image-utils': 0.7.4
      '@expo/json-file': 9.1.4
      '@react-native/normalize-colors': 0.79.2
      debug: 4.4.0
      resolve-from: 5.0.0
      semver: 7.7.1
      xml2js: 0.6.0
    transitivePeerDependencies:
      - supports-color

  '@expo/sdk-runtime-versions@1.0.0': {}

  '@expo/spawn-async@1.7.2':
    dependencies:
      cross-spawn: 7.0.6

  '@expo/vector-icons@14.0.4':
    dependencies:
      prop-types: 15.8.1

  '@expo/ws-tunnel@1.0.6': {}

  '@expo/xcpretty@4.3.2':
    dependencies:
      '@babel/code-frame': 7.10.4
      chalk: 4.1.2
      find-up: 5.0.0
      js-yaml: 4.1.0

  '@fortawesome/fontawesome-common-types@6.7.2': {}

  '@fortawesome/fontawesome-svg-core@6.7.2':
    dependencies:
      '@fortawesome/fontawesome-common-types': 6.7.2

  '@fortawesome/free-brands-svg-icons@6.7.2':
    dependencies:
      '@fortawesome/fontawesome-common-types': 6.7.2

  '@fortawesome/free-regular-svg-icons@6.7.2':
    dependencies:
      '@fortawesome/fontawesome-common-types': 6.7.2

  '@fortawesome/free-solid-svg-icons@6.7.2':
    dependencies:
      '@fortawesome/fontawesome-common-types': 6.7.2

  '@fortawesome/react-native-fontawesome@0.3.2(@fortawesome/fontawesome-svg-core@6.7.2)(react-native-svg@15.11.2(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))':
    dependencies:
      '@fortawesome/fontawesome-svg-core': 6.7.2
      humps: 2.0.1
      prop-types: 15.8.1
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-native-svg: 15.11.2(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)

  '@humanwhocodes/config-array@0.9.5':
    dependencies:
      '@humanwhocodes/object-schema': 1.2.1
      debug: 4.4.0
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  '@humanwhocodes/object-schema@1.2.1': {}

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@isaacs/fs-minipass@4.0.1':
    dependencies:
      minipass: 7.1.2

  '@isaacs/ttlcache@1.4.1': {}

  '@istanbuljs/load-nyc-config@1.1.0':
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0

  '@istanbuljs/schema@0.1.3': {}

  '@jest/console@29.7.0':
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 22.13.5
      chalk: 4.1.2
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      slash: 3.0.0

  '@jest/core@29.7.0(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))':
    dependencies:
      '@jest/console': 29.7.0
      '@jest/reporters': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.13.5
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      ci-info: 3.9.0
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-changed-files: 29.7.0
      jest-config: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-resolve-dependencies: 29.7.0
      jest-runner: 29.7.0
      jest-runtime: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      jest-validate: 29.7.0
      jest-watcher: 29.7.0
      micromatch: 4.0.8
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-ansi: 6.0.1
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color
      - ts-node

  '@jest/create-cache-key-function@29.7.0':
    dependencies:
      '@jest/types': 29.6.3

  '@jest/environment@29.7.0':
    dependencies:
      '@jest/fake-timers': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.13.5
      jest-mock: 29.7.0

  '@jest/expect-utils@29.7.0':
    dependencies:
      jest-get-type: 29.6.3

  '@jest/expect@29.7.0':
    dependencies:
      expect: 29.7.0
      jest-snapshot: 29.7.0
    transitivePeerDependencies:
      - supports-color

  '@jest/fake-timers@29.7.0':
    dependencies:
      '@jest/types': 29.6.3
      '@sinonjs/fake-timers': 10.3.0
      '@types/node': 22.13.5
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-util: 29.7.0

  '@jest/globals@29.7.0':
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/expect': 29.7.0
      '@jest/types': 29.6.3
      jest-mock: 29.7.0
    transitivePeerDependencies:
      - supports-color

  '@jest/reporters@29.7.0':
    dependencies:
      '@bcoe/v8-coverage': 0.2.3
      '@jest/console': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@jridgewell/trace-mapping': 0.3.25
      '@types/node': 22.13.5
      chalk: 4.1.2
      collect-v8-coverage: 1.0.2
      exit: 0.1.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-instrument: 6.0.3
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.7
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      jest-worker: 29.7.0
      slash: 3.0.0
      string-length: 4.0.2
      strip-ansi: 6.0.1
      v8-to-istanbul: 9.3.0
    transitivePeerDependencies:
      - supports-color

  '@jest/schemas@29.6.3':
    dependencies:
      '@sinclair/typebox': 0.27.8

  '@jest/source-map@29.6.3':
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      callsites: 3.1.0
      graceful-fs: 4.2.11

  '@jest/test-result@29.7.0':
    dependencies:
      '@jest/console': 29.7.0
      '@jest/types': 29.6.3
      '@types/istanbul-lib-coverage': 2.0.6
      collect-v8-coverage: 1.0.2

  '@jest/test-sequencer@29.7.0':
    dependencies:
      '@jest/test-result': 29.7.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      slash: 3.0.0

  '@jest/transform@29.7.0':
    dependencies:
      '@babel/core': 7.26.9
      '@jest/types': 29.6.3
      '@jridgewell/trace-mapping': 0.3.25
      babel-plugin-istanbul: 6.1.1
      chalk: 4.1.2
      convert-source-map: 2.0.0
      fast-json-stable-stringify: 2.1.0
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      micromatch: 4.0.8
      pirates: 4.0.6
      slash: 3.0.0
      write-file-atomic: 4.0.2
    transitivePeerDependencies:
      - supports-color

  '@jest/types@29.6.3':
    dependencies:
      '@jest/schemas': 29.6.3
      '@types/istanbul-lib-coverage': 2.0.6
      '@types/istanbul-reports': 3.0.4
      '@types/node': 22.13.5
      '@types/yargs': 17.0.33
      chalk: 4.1.2

  '@jridgewell/gen-mapping@0.3.8':
    dependencies:
      '@jridgewell/set-array': 1.2.1
      '@jridgewell/sourcemap-codec': 1.5.0
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/resolve-uri@3.1.2': {}

  '@jridgewell/set-array@1.2.1': {}

  '@jridgewell/source-map@0.3.6':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      '@jridgewell/trace-mapping': 0.3.25

  '@jridgewell/sourcemap-codec@1.5.0': {}

  '@jridgewell/trace-mapping@0.3.25':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@jridgewell/trace-mapping@0.3.9':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.2
      '@jridgewell/sourcemap-codec': 1.5.0

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.19.1

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@react-native-async-storage/async-storage@2.1.2(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))':
    dependencies:
      merge-options: 3.0.4
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  '@react-native-community/datetimepicker@8.3.0(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)':
    dependencies:
      invariant: 2.2.4
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
    optionalDependencies:
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)

  '@react-native-community/netinfo@11.4.1(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))':
    dependencies:
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  '@react-native-google-signin/google-signin@13.2.0(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-dom@19.0.0(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)':
    dependencies:
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
    optionalDependencies:
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-dom: 19.0.0(react@19.0.0)

  '@react-native-picker/picker@2.11.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)':
    dependencies:
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  '@react-native/assets-registry@0.79.2': {}

  '@react-native/babel-plugin-codegen@0.79.2(@babel/core@7.26.9)':
    dependencies:
      '@babel/traverse': 7.26.9
      '@react-native/codegen': 0.79.2(@babel/core@7.26.9)
    transitivePeerDependencies:
      - '@babel/core'
      - supports-color

  '@react-native/babel-preset@0.79.2(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      '@babel/plugin-proposal-export-default-from': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.26.9)
      '@babel/plugin-syntax-export-default-from': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.26.9)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.26.9)
      '@babel/plugin-transform-arrow-functions': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-async-generator-functions': 7.26.8(@babel/core@7.26.9)
      '@babel/plugin-transform-async-to-generator': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-block-scoping': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-class-properties': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-classes': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-computed-properties': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-destructuring': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-flow-strip-types': 7.26.5(@babel/core@7.26.9)
      '@babel/plugin-transform-for-of': 7.26.9(@babel/core@7.26.9)
      '@babel/plugin-transform-function-name': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-literals': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-logical-assignment-operators': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-modules-commonjs': 7.26.3(@babel/core@7.26.9)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.26.6(@babel/core@7.26.9)
      '@babel/plugin-transform-numeric-separator': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-object-rest-spread': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-optional-catch-binding': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-optional-chaining': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-parameters': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-private-methods': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-private-property-in-object': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-react-display-name': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-react-jsx': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-react-jsx-self': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-react-jsx-source': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-regenerator': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-runtime': 7.26.9(@babel/core@7.26.9)
      '@babel/plugin-transform-shorthand-properties': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-spread': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-sticky-regex': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-typescript': 7.26.8(@babel/core@7.26.9)
      '@babel/plugin-transform-unicode-regex': 7.25.9(@babel/core@7.26.9)
      '@babel/template': 7.26.9
      '@react-native/babel-plugin-codegen': 0.79.2(@babel/core@7.26.9)
      babel-plugin-syntax-hermes-parser: 0.25.1
      babel-plugin-transform-flow-enums: 0.0.2(@babel/core@7.26.9)
      react-refresh: 0.14.2
    transitivePeerDependencies:
      - supports-color

  '@react-native/codegen@0.79.2(@babel/core@7.26.9)':
    dependencies:
      '@babel/core': 7.26.9
      glob: 7.2.3
      hermes-parser: 0.25.1
      invariant: 2.2.4
      nullthrows: 1.1.1
      yargs: 17.7.2

  '@react-native/community-cli-plugin@0.79.2':
    dependencies:
      '@react-native/dev-middleware': 0.79.2
      chalk: 4.1.2
      debug: 2.6.9
      invariant: 2.2.4
      metro: 0.82.3
      metro-config: 0.82.3
      metro-core: 0.82.3
      semver: 7.7.1
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  '@react-native/debugger-frontend@0.79.2': {}

  '@react-native/dev-middleware@0.79.2':
    dependencies:
      '@isaacs/ttlcache': 1.4.1
      '@react-native/debugger-frontend': 0.79.2
      chrome-launcher: 0.15.2
      chromium-edge-launcher: 0.2.0
      connect: 3.7.0
      debug: 2.6.9
      invariant: 2.2.4
      nullthrows: 1.1.1
      open: 7.4.2
      serve-static: 1.16.2
      ws: 6.2.3
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  '@react-native/gradle-plugin@0.79.2': {}

  '@react-native/js-polyfills@0.79.2': {}

  '@react-native/normalize-colors@0.74.89': {}

  '@react-native/normalize-colors@0.79.2': {}

  '@react-native/virtualized-lists@0.79.2(@types/react@19.0.14)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)':
    dependencies:
      invariant: 2.2.4
      nullthrows: 1.1.1
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
    optionalDependencies:
      '@types/react': 19.0.14

  '@react-navigation/bottom-tabs@7.3.13(@react-navigation/native@7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-screens@4.10.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@react-navigation/elements': 2.4.2(@react-navigation/native@7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      '@react-navigation/native': 7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      color: 4.2.3
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-native-safe-area-context: 5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-screens: 4.10.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
    transitivePeerDependencies:
      - '@react-native-masked-view/masked-view'

  '@react-navigation/core@7.9.2(react@19.0.0)':
    dependencies:
      '@react-navigation/routers': 7.3.7
      escape-string-regexp: 4.0.0
      nanoid: 3.3.11
      query-string: 7.1.3
      react: 19.0.0
      react-is: 19.1.0
      use-latest-callback: 0.2.3(react@19.0.0)
      use-sync-external-store: 1.5.0(react@19.0.0)

  '@react-navigation/elements@2.4.2(@react-navigation/native@7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@react-navigation/native': 7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      color: 4.2.3
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-native-safe-area-context: 5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)

  '@react-navigation/native-stack@7.3.13(@react-navigation/native@7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-screens@4.10.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@react-navigation/elements': 2.4.2(@react-navigation/native@7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-safe-area-context@5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      '@react-navigation/native': 7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-native-safe-area-context: 5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-screens: 4.10.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      warn-once: 0.1.1
    transitivePeerDependencies:
      - '@react-native-masked-view/masked-view'

  '@react-navigation/native@7.1.9(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@react-navigation/core': 7.9.2(react@19.0.0)
      escape-string-regexp: 4.0.0
      fast-deep-equal: 3.1.3
      nanoid: 3.3.11
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      use-latest-callback: 0.2.3(react@19.0.0)

  '@react-navigation/routers@7.3.7':
    dependencies:
      nanoid: 3.3.11

  '@sentry-internal/browser-utils@8.54.0':
    dependencies:
      '@sentry/core': 8.54.0

  '@sentry-internal/feedback@8.54.0':
    dependencies:
      '@sentry/core': 8.54.0

  '@sentry-internal/replay-canvas@8.54.0':
    dependencies:
      '@sentry-internal/replay': 8.54.0
      '@sentry/core': 8.54.0

  '@sentry-internal/replay@8.54.0':
    dependencies:
      '@sentry-internal/browser-utils': 8.54.0
      '@sentry/core': 8.54.0

  '@sentry/babel-plugin-component-annotate@3.2.2': {}

  '@sentry/browser@8.54.0':
    dependencies:
      '@sentry-internal/browser-utils': 8.54.0
      '@sentry-internal/feedback': 8.54.0
      '@sentry-internal/replay': 8.54.0
      '@sentry-internal/replay-canvas': 8.54.0
      '@sentry/core': 8.54.0

  '@sentry/cli-darwin@2.42.4':
    optional: true

  '@sentry/cli-linux-arm64@2.42.4':
    optional: true

  '@sentry/cli-linux-arm@2.42.4':
    optional: true

  '@sentry/cli-linux-i686@2.42.4':
    optional: true

  '@sentry/cli-linux-x64@2.42.4':
    optional: true

  '@sentry/cli-win32-i686@2.42.4':
    optional: true

  '@sentry/cli-win32-x64@2.42.4':
    optional: true

  '@sentry/cli@2.42.4':
    dependencies:
      https-proxy-agent: 5.0.1
      node-fetch: 2.7.0
      progress: 2.0.3
      proxy-from-env: 1.1.0
      which: 2.0.2
    optionalDependencies:
      '@sentry/cli-darwin': 2.42.4
      '@sentry/cli-linux-arm': 2.42.4
      '@sentry/cli-linux-arm64': 2.42.4
      '@sentry/cli-linux-i686': 2.42.4
      '@sentry/cli-linux-x64': 2.42.4
      '@sentry/cli-win32-i686': 2.42.4
      '@sentry/cli-win32-x64': 2.42.4
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@sentry/core@8.54.0': {}

  '@sentry/react-native@6.10.0(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@sentry/babel-plugin-component-annotate': 3.2.2
      '@sentry/browser': 8.54.0
      '@sentry/cli': 2.42.4
      '@sentry/core': 8.54.0
      '@sentry/react': 8.54.0(react@19.0.0)
      '@sentry/types': 8.54.0
      '@sentry/utils': 8.54.0
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
    optionalDependencies:
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
    transitivePeerDependencies:
      - encoding
      - supports-color

  '@sentry/react@8.54.0(react@19.0.0)':
    dependencies:
      '@sentry/browser': 8.54.0
      '@sentry/core': 8.54.0
      hoist-non-react-statics: 3.3.2
      react: 19.0.0

  '@sentry/types@8.54.0':
    dependencies:
      '@sentry/core': 8.54.0

  '@sentry/utils@8.54.0':
    dependencies:
      '@sentry/core': 8.54.0

  '@shopify/flash-list@1.7.6(@babel/runtime@8.0.0-alpha.17)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)':
    dependencies:
      '@babel/runtime': 8.0.0-alpha.17
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      recyclerlistview: 4.2.3(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      tslib: 2.8.1

  '@shopify/react-native-skia@2.0.0-next.4(react-native-reanimated@3.17.5(@babel/core@7.26.9)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)':
    dependencies:
      canvaskit-wasm: 0.40.0
      react: 19.0.0
      react-reconciler: 0.31.0(react@19.0.0)
    optionalDependencies:
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-native-reanimated: 3.17.5(@babel/core@7.26.9)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)

  '@sinclair/typebox@0.27.8': {}

  '@sinonjs/commons@3.0.1':
    dependencies:
      type-detect: 4.0.8

  '@sinonjs/fake-timers@10.3.0':
    dependencies:
      '@sinonjs/commons': 3.0.1

  '@smithy/abort-controller@3.1.9':
    dependencies:
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/config-resolver@3.0.13':
    dependencies:
      '@smithy/node-config-provider': 3.1.12
      '@smithy/types': 3.7.2
      '@smithy/util-config-provider': 3.0.0
      '@smithy/util-middleware': 3.0.11
      tslib: 2.8.1

  '@smithy/core@2.5.7':
    dependencies:
      '@smithy/middleware-serde': 3.0.11
      '@smithy/protocol-http': 4.1.8
      '@smithy/types': 3.7.2
      '@smithy/util-body-length-browser': 3.0.0
      '@smithy/util-middleware': 3.0.11
      '@smithy/util-stream': 3.3.4
      '@smithy/util-utf8': 3.0.0
      tslib: 2.8.1

  '@smithy/credential-provider-imds@3.2.8':
    dependencies:
      '@smithy/node-config-provider': 3.1.12
      '@smithy/property-provider': 3.1.11
      '@smithy/types': 3.7.2
      '@smithy/url-parser': 3.0.11
      tslib: 2.8.1

  '@smithy/eventstream-codec@3.1.10':
    dependencies:
      '@aws-crypto/crc32': 5.2.0
      '@smithy/types': 3.7.2
      '@smithy/util-hex-encoding': 3.0.0
      tslib: 2.8.1

  '@smithy/eventstream-serde-browser@3.0.14':
    dependencies:
      '@smithy/eventstream-serde-universal': 3.0.13
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/eventstream-serde-config-resolver@3.0.11':
    dependencies:
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/eventstream-serde-node@3.0.13':
    dependencies:
      '@smithy/eventstream-serde-universal': 3.0.13
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/eventstream-serde-universal@3.0.13':
    dependencies:
      '@smithy/eventstream-codec': 3.1.10
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/fetch-http-handler@3.2.9':
    dependencies:
      '@smithy/protocol-http': 4.1.8
      '@smithy/querystring-builder': 3.0.11
      '@smithy/types': 3.7.2
      '@smithy/util-base64': 3.0.0
      tslib: 2.8.1

  '@smithy/fetch-http-handler@4.1.3':
    dependencies:
      '@smithy/protocol-http': 4.1.8
      '@smithy/querystring-builder': 3.0.11
      '@smithy/types': 3.7.2
      '@smithy/util-base64': 3.0.0
      tslib: 2.8.1

  '@smithy/hash-node@3.0.11':
    dependencies:
      '@smithy/types': 3.7.2
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.8.1

  '@smithy/invalid-dependency@3.0.11':
    dependencies:
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/is-array-buffer@2.2.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/is-array-buffer@3.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/md5-js@2.0.7':
    dependencies:
      '@smithy/types': 2.12.0
      '@smithy/util-utf8': 2.3.0
      tslib: 2.8.1

  '@smithy/middleware-content-length@3.0.13':
    dependencies:
      '@smithy/protocol-http': 4.1.8
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/middleware-endpoint@3.2.8':
    dependencies:
      '@smithy/core': 2.5.7
      '@smithy/middleware-serde': 3.0.11
      '@smithy/node-config-provider': 3.1.12
      '@smithy/shared-ini-file-loader': 3.1.12
      '@smithy/types': 3.7.2
      '@smithy/url-parser': 3.0.11
      '@smithy/util-middleware': 3.0.11
      tslib: 2.8.1

  '@smithy/middleware-retry@3.0.34':
    dependencies:
      '@smithy/node-config-provider': 3.1.12
      '@smithy/protocol-http': 4.1.8
      '@smithy/service-error-classification': 3.0.11
      '@smithy/smithy-client': 3.7.0
      '@smithy/types': 3.7.2
      '@smithy/util-middleware': 3.0.11
      '@smithy/util-retry': 3.0.11
      tslib: 2.8.1
      uuid: 9.0.1

  '@smithy/middleware-serde@3.0.11':
    dependencies:
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/middleware-stack@3.0.11':
    dependencies:
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/node-config-provider@3.1.12':
    dependencies:
      '@smithy/property-provider': 3.1.11
      '@smithy/shared-ini-file-loader': 3.1.12
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/node-http-handler@3.3.3':
    dependencies:
      '@smithy/abort-controller': 3.1.9
      '@smithy/protocol-http': 4.1.8
      '@smithy/querystring-builder': 3.0.11
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/property-provider@3.1.11':
    dependencies:
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/protocol-http@4.1.8':
    dependencies:
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/querystring-builder@3.0.11':
    dependencies:
      '@smithy/types': 3.7.2
      '@smithy/util-uri-escape': 3.0.0
      tslib: 2.8.1

  '@smithy/querystring-parser@3.0.11':
    dependencies:
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/service-error-classification@3.0.11':
    dependencies:
      '@smithy/types': 3.7.2

  '@smithy/shared-ini-file-loader@3.1.12':
    dependencies:
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/signature-v4@4.2.4':
    dependencies:
      '@smithy/is-array-buffer': 3.0.0
      '@smithy/protocol-http': 4.1.8
      '@smithy/types': 3.7.2
      '@smithy/util-hex-encoding': 3.0.0
      '@smithy/util-middleware': 3.0.11
      '@smithy/util-uri-escape': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.8.1

  '@smithy/smithy-client@3.7.0':
    dependencies:
      '@smithy/core': 2.5.7
      '@smithy/middleware-endpoint': 3.2.8
      '@smithy/middleware-stack': 3.0.11
      '@smithy/protocol-http': 4.1.8
      '@smithy/types': 3.7.2
      '@smithy/util-stream': 3.3.4
      tslib: 2.8.1

  '@smithy/types@2.12.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/types@3.7.2':
    dependencies:
      tslib: 2.8.1

  '@smithy/types@4.1.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/url-parser@3.0.11':
    dependencies:
      '@smithy/querystring-parser': 3.0.11
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/util-base64@3.0.0':
    dependencies:
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.8.1

  '@smithy/util-body-length-browser@3.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-body-length-node@3.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-buffer-from@2.2.0':
    dependencies:
      '@smithy/is-array-buffer': 2.2.0
      tslib: 2.8.1

  '@smithy/util-buffer-from@3.0.0':
    dependencies:
      '@smithy/is-array-buffer': 3.0.0
      tslib: 2.8.1

  '@smithy/util-config-provider@3.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-defaults-mode-browser@3.0.34':
    dependencies:
      '@smithy/property-provider': 3.1.11
      '@smithy/smithy-client': 3.7.0
      '@smithy/types': 3.7.2
      bowser: 2.11.0
      tslib: 2.8.1

  '@smithy/util-defaults-mode-node@3.0.34':
    dependencies:
      '@smithy/config-resolver': 3.0.13
      '@smithy/credential-provider-imds': 3.2.8
      '@smithy/node-config-provider': 3.1.12
      '@smithy/property-provider': 3.1.11
      '@smithy/smithy-client': 3.7.0
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/util-endpoints@2.1.7':
    dependencies:
      '@smithy/node-config-provider': 3.1.12
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/util-hex-encoding@2.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-hex-encoding@3.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-middleware@3.0.11':
    dependencies:
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/util-retry@3.0.11':
    dependencies:
      '@smithy/service-error-classification': 3.0.11
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@smithy/util-stream@3.3.4':
    dependencies:
      '@smithy/fetch-http-handler': 4.1.3
      '@smithy/node-http-handler': 3.3.3
      '@smithy/types': 3.7.2
      '@smithy/util-base64': 3.0.0
      '@smithy/util-buffer-from': 3.0.0
      '@smithy/util-hex-encoding': 3.0.0
      '@smithy/util-utf8': 3.0.0
      tslib: 2.8.1

  '@smithy/util-uri-escape@3.0.0':
    dependencies:
      tslib: 2.8.1

  '@smithy/util-utf8@2.0.0':
    dependencies:
      '@smithy/util-buffer-from': 2.2.0
      tslib: 2.8.1

  '@smithy/util-utf8@2.3.0':
    dependencies:
      '@smithy/util-buffer-from': 2.2.0
      tslib: 2.8.1

  '@smithy/util-utf8@3.0.0':
    dependencies:
      '@smithy/util-buffer-from': 3.0.0
      tslib: 2.8.1

  '@smithy/util-waiter@3.2.0':
    dependencies:
      '@smithy/abort-controller': 3.1.9
      '@smithy/types': 3.7.2
      tslib: 2.8.1

  '@supabase/auth-js@2.69.1':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/functions-js@2.4.4':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/node-fetch@2.6.15':
    dependencies:
      whatwg-url: 5.0.0

  '@supabase/postgrest-js@1.19.4':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/realtime-js@2.11.8-next.4':
    dependencies:
      '@supabase/node-fetch': 2.6.15
      '@types/phoenix': 1.6.6
      '@types/ws': 8.18.1
      ws: 8.18.1
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  '@supabase/storage-js@2.7.1':
    dependencies:
      '@supabase/node-fetch': 2.6.15

  '@supabase/supabase-js@2.49.5-next.5':
    dependencies:
      '@supabase/auth-js': 2.69.1
      '@supabase/functions-js': 2.4.4
      '@supabase/node-fetch': 2.6.15
      '@supabase/postgrest-js': 1.19.4
      '@supabase/realtime-js': 2.11.8-next.4
      '@supabase/storage-js': 2.7.1
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  '@testing-library/react-native@12.9.0(jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3)))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react-test-renderer@19.0.0(react@19.0.0))(react@19.0.0)':
    dependencies:
      jest-matcher-utils: 29.7.0
      pretty-format: 29.7.0
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-test-renderer: 19.0.0(react@19.0.0)
      redent: 3.0.0
    optionalDependencies:
      jest: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))

  '@tootallnate/once@2.0.0': {}

  '@tsconfig/node10@1.0.11': {}

  '@tsconfig/node12@1.0.11': {}

  '@tsconfig/node14@1.0.3': {}

  '@tsconfig/node16@1.0.4': {}

  '@types/aws-lambda@8.10.147': {}

  '@types/babel__core@7.20.5':
    dependencies:
      '@babel/parser': 7.26.9
      '@babel/types': 7.26.9
      '@types/babel__generator': 7.6.8
      '@types/babel__template': 7.4.4
      '@types/babel__traverse': 7.20.6

  '@types/babel__generator@7.6.8':
    dependencies:
      '@babel/types': 7.26.9

  '@types/babel__template@7.4.4':
    dependencies:
      '@babel/parser': 7.26.9
      '@babel/types': 7.26.9

  '@types/babel__traverse@7.20.6':
    dependencies:
      '@babel/types': 7.26.9

  '@types/eslint-scope@3.7.7':
    dependencies:
      '@types/eslint': 9.6.1
      '@types/estree': 1.0.6

  '@types/eslint@9.6.1':
    dependencies:
      '@types/estree': 1.0.6
      '@types/json-schema': 7.0.15

  '@types/estree@1.0.6': {}

  '@types/graceful-fs@4.1.9':
    dependencies:
      '@types/node': 22.13.5

  '@types/hammerjs@2.0.46': {}

  '@types/i18n-js@3.8.2': {}

  '@types/istanbul-lib-coverage@2.0.6': {}

  '@types/istanbul-lib-report@3.0.3':
    dependencies:
      '@types/istanbul-lib-coverage': 2.0.6

  '@types/istanbul-reports@3.0.4':
    dependencies:
      '@types/istanbul-lib-report': 3.0.3

  '@types/jest@29.5.14':
    dependencies:
      expect: 29.7.0
      pretty-format: 29.7.0

  '@types/jsdom@20.0.1':
    dependencies:
      '@types/node': 22.13.5
      '@types/tough-cookie': 4.0.5
      parse5: 7.2.1

  '@types/json-schema@7.0.15': {}

  '@types/json5@0.0.29': {}

  '@types/node@22.13.5':
    dependencies:
      undici-types: 6.20.0

  '@types/phoenix@1.6.6': {}

  '@types/react-native-onboarding-swiper@1.1.9(@babel/core@7.26.9)(react@19.0.0)':
    dependencies:
      '@types/react': 19.0.14
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
    transitivePeerDependencies:
      - '@babel/core'
      - '@react-native-community/cli'
      - bufferutil
      - react
      - supports-color
      - utf-8-validate

  '@types/react-native-vector-icons@6.4.18':
    dependencies:
      '@types/react': 19.0.14
      '@types/react-native': 0.70.19

  '@types/react-native@0.70.19':
    dependencies:
      '@types/react': 19.0.14

  '@types/react-reconciler@0.28.9(@types/react@19.0.14)':
    dependencies:
      '@types/react': 19.0.14

  '@types/react@19.0.14':
    dependencies:
      csstype: 3.1.3

  '@types/semver@7.5.8': {}

  '@types/stack-utils@2.0.3': {}

  '@types/tough-cookie@4.0.5': {}

  '@types/uuid@9.0.8': {}

  '@types/ws@8.18.1':
    dependencies:
      '@types/node': 22.13.5

  '@types/yargs-parser@21.0.3': {}

  '@types/yargs@17.0.33':
    dependencies:
      '@types/yargs-parser': 21.0.3

  '@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.17.0)(typescript@5.8.3))(eslint@8.17.0)(typescript@5.8.3)':
    dependencies:
      '@eslint-community/regexpp': 4.12.1
      '@typescript-eslint/parser': 5.62.0(eslint@8.17.0)(typescript@5.8.3)
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/type-utils': 5.62.0(eslint@8.17.0)(typescript@5.8.3)
      '@typescript-eslint/utils': 5.62.0(eslint@8.17.0)(typescript@5.8.3)
      debug: 4.4.0
      eslint: 8.17.0
      graphemer: 1.4.0
      ignore: 5.3.2
      natural-compare-lite: 1.4.0
      semver: 7.7.1
      tsutils: 3.21.0(typescript@5.8.3)
    optionalDependencies:
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/parser@5.62.0(eslint@8.17.0)(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@5.8.3)
      debug: 4.4.0
      eslint: 8.17.0
    optionalDependencies:
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/scope-manager@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0

  '@typescript-eslint/type-utils@5.62.0(eslint@8.17.0)(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@5.8.3)
      '@typescript-eslint/utils': 5.62.0(eslint@8.17.0)(typescript@5.8.3)
      debug: 4.4.0
      eslint: 8.17.0
      tsutils: 3.21.0(typescript@5.8.3)
    optionalDependencies:
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/types@5.62.0': {}

  '@typescript-eslint/typescript-estree@5.62.0(typescript@5.8.3)':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/visitor-keys': 5.62.0
      debug: 4.4.0
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.7.1
      tsutils: 3.21.0(typescript@5.8.3)
    optionalDependencies:
      typescript: 5.8.3
    transitivePeerDependencies:
      - supports-color

  '@typescript-eslint/utils@5.62.0(eslint@8.17.0)(typescript@5.8.3)':
    dependencies:
      '@eslint-community/eslint-utils': 4.4.1(eslint@8.17.0)
      '@types/json-schema': 7.0.15
      '@types/semver': 7.5.8
      '@typescript-eslint/scope-manager': 5.62.0
      '@typescript-eslint/types': 5.62.0
      '@typescript-eslint/typescript-estree': 5.62.0(typescript@5.8.3)
      eslint: 8.17.0
      eslint-scope: 5.1.1
      semver: 7.7.1
    transitivePeerDependencies:
      - supports-color
      - typescript

  '@typescript-eslint/visitor-keys@5.62.0':
    dependencies:
      '@typescript-eslint/types': 5.62.0
      eslint-visitor-keys: 3.4.3

  '@urql/core@5.1.0(graphql@15.10.1)':
    dependencies:
      '@0no-co/graphql.web': 1.1.1(graphql@15.10.1)
      wonka: 6.3.4
    transitivePeerDependencies:
      - graphql

  '@urql/exchange-retry@1.3.1(@urql/core@5.1.0(graphql@15.10.1))':
    dependencies:
      '@urql/core': 5.1.0(graphql@15.10.1)
      wonka: 6.3.4

  '@webassemblyjs/ast@1.14.1':
    dependencies:
      '@webassemblyjs/helper-numbers': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2

  '@webassemblyjs/floating-point-hex-parser@1.13.2': {}

  '@webassemblyjs/helper-api-error@1.13.2': {}

  '@webassemblyjs/helper-buffer@1.14.1': {}

  '@webassemblyjs/helper-numbers@1.13.2':
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.13.2
      '@webassemblyjs/helper-api-error': 1.13.2
      '@xtuc/long': 4.2.2

  '@webassemblyjs/helper-wasm-bytecode@1.13.2': {}

  '@webassemblyjs/helper-wasm-section@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/wasm-gen': 1.14.1

  '@webassemblyjs/ieee754@1.13.2':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/leb128@1.13.2':
    dependencies:
      '@xtuc/long': 4.2.2

  '@webassemblyjs/utf8@1.13.2': {}

  '@webassemblyjs/wasm-edit@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/helper-wasm-section': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-opt': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      '@webassemblyjs/wast-printer': 1.14.1

  '@webassemblyjs/wasm-gen@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wasm-opt@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-buffer': 1.14.1
      '@webassemblyjs/wasm-gen': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1

  '@webassemblyjs/wasm-parser@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/helper-api-error': 1.13.2
      '@webassemblyjs/helper-wasm-bytecode': 1.13.2
      '@webassemblyjs/ieee754': 1.13.2
      '@webassemblyjs/leb128': 1.13.2
      '@webassemblyjs/utf8': 1.13.2

  '@webassemblyjs/wast-printer@1.14.1':
    dependencies:
      '@webassemblyjs/ast': 1.14.1
      '@xtuc/long': 4.2.2

  '@webgpu/types@0.1.21': {}

  '@xmldom/xmldom@0.8.10': {}

  '@xtuc/ieee754@1.2.0': {}

  '@xtuc/long@4.2.2': {}

  '@yarnpkg/lockfile@1.1.0': {}

  abab@2.0.6: {}

  abort-controller@3.0.0:
    dependencies:
      event-target-shim: 5.0.1

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  acorn-globals@7.0.1:
    dependencies:
      acorn: 8.14.0
      acorn-walk: 8.3.4

  acorn-jsx@5.3.2(acorn@8.14.0):
    dependencies:
      acorn: 8.14.0

  acorn-loose@8.4.0:
    dependencies:
      acorn: 8.14.0

  acorn-walk@8.3.4:
    dependencies:
      acorn: 8.14.0

  acorn@8.14.0: {}

  aes-js@3.1.2: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  agent-base@7.1.3: {}

  ajv-formats@2.1.1(ajv@8.17.1):
    optionalDependencies:
      ajv: 8.17.1

  ajv-keywords@5.1.0(ajv@8.17.1):
    dependencies:
      ajv: 8.17.1
      fast-deep-equal: 3.1.3

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.17.1:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-uri: 3.0.6
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2

  anser@1.4.10: {}

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-escapes@6.2.1: {}

  ansi-regex@4.1.1: {}

  ansi-regex@5.0.1: {}

  ansi-regex@6.1.0: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  apisauce@3.0.1:
    dependencies:
      axios: 1.8.1
    transitivePeerDependencies:
      - debug

  application-config-path@0.1.1: {}

  arg@4.1.3: {}

  arg@5.0.2: {}

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  array-buffer-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.3
      is-array-buffer: 3.0.5

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      is-string: 1.1.1

  array-union@2.1.0: {}

  array.prototype.flat@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.1.0

  array.prototype.flatmap@1.3.3:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-shim-unscopables: 1.1.0

  arraybuffer.prototype.slice@1.0.4:
    dependencies:
      array-buffer-byte-length: 1.0.2
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      is-array-buffer: 3.0.5

  asap@2.0.6: {}

  async-function@1.0.0: {}

  async-limiter@1.0.1: {}

  async@3.2.6: {}

  asynckit@0.4.0: {}

  at-least-node@1.0.0: {}

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.1.0

  aws-amplify@6.14.4:
    dependencies:
      '@aws-amplify/analytics': 7.0.80(@aws-amplify/core@6.11.4)
      '@aws-amplify/api': 6.3.11(@aws-amplify/core@6.11.4)
      '@aws-amplify/auth': 6.12.4(@aws-amplify/core@6.11.4)
      '@aws-amplify/core': 6.11.4
      '@aws-amplify/datastore': 5.0.82(@aws-amplify/core@6.11.4)
      '@aws-amplify/notifications': 2.0.80(@aws-amplify/core@6.11.4)
      '@aws-amplify/storage': 6.8.4(@aws-amplify/core@6.11.4)
      tslib: 2.8.1
    transitivePeerDependencies:
      - aws-crt

  axios@1.8.1:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.2
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-jest@29.7.0(@babel/core@7.26.9):
    dependencies:
      '@babel/core': 7.26.9
      '@jest/transform': 29.7.0
      '@types/babel__core': 7.20.5
      babel-plugin-istanbul: 6.1.1
      babel-preset-jest: 29.6.3(@babel/core@7.26.9)
      chalk: 4.1.2
      graceful-fs: 4.2.11
      slash: 3.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-istanbul@6.1.1:
    dependencies:
      '@babel/helper-plugin-utils': 7.26.5
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-instrument: 5.2.1
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-jest-hoist@29.6.3:
    dependencies:
      '@babel/template': 7.26.9
      '@babel/types': 7.26.9
      '@types/babel__core': 7.20.5
      '@types/babel__traverse': 7.20.6

  babel-plugin-polyfill-corejs2@0.4.12(@babel/core@7.26.9):
    dependencies:
      '@babel/compat-data': 7.26.8
      '@babel/core': 7.26.9
      '@babel/helper-define-polyfill-provider': 0.6.3(@babel/core@7.26.9)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.10.6(@babel/core@7.26.9):
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-define-polyfill-provider': 0.6.3(@babel/core@7.26.9)
      core-js-compat: 3.40.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.11.1(@babel/core@7.26.9):
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-define-polyfill-provider': 0.6.3(@babel/core@7.26.9)
      core-js-compat: 3.40.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.6.3(@babel/core@7.26.9):
    dependencies:
      '@babel/core': 7.26.9
      '@babel/helper-define-polyfill-provider': 0.6.3(@babel/core@7.26.9)
    transitivePeerDependencies:
      - supports-color

  babel-plugin-react-native-web@0.19.13: {}

  babel-plugin-syntax-hermes-parser@0.25.1:
    dependencies:
      hermes-parser: 0.25.1

  babel-plugin-transform-flow-enums@0.0.2(@babel/core@7.26.9):
    dependencies:
      '@babel/plugin-syntax-flow': 7.26.0(@babel/core@7.26.9)
    transitivePeerDependencies:
      - '@babel/core'

  babel-preset-current-node-syntax@1.1.0(@babel/core@7.26.9):
    dependencies:
      '@babel/core': 7.26.9
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.26.9)
      '@babel/plugin-syntax-bigint': 7.8.3(@babel/core@7.26.9)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.26.9)
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.26.9)
      '@babel/plugin-syntax-import-attributes': 7.26.0(@babel/core@7.26.9)
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.26.9)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.26.9)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.26.9)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.26.9)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.26.9)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.26.9)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.26.9)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.26.9)
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.26.9)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.26.9)

  babel-preset-expo@13.1.11(@babel/core@7.26.9):
    dependencies:
      '@babel/helper-module-imports': 7.25.9
      '@babel/plugin-proposal-decorators': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-proposal-export-default-from': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-syntax-export-default-from': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-export-namespace-from': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-flow-strip-types': 7.26.5(@babel/core@7.26.9)
      '@babel/plugin-transform-modules-commonjs': 7.26.3(@babel/core@7.26.9)
      '@babel/plugin-transform-object-rest-spread': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-parameters': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-private-methods': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-private-property-in-object': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-runtime': 7.26.9(@babel/core@7.26.9)
      '@babel/preset-react': 7.26.3(@babel/core@7.26.9)
      '@babel/preset-typescript': 7.26.0(@babel/core@7.26.9)
      '@react-native/babel-preset': 0.79.2(@babel/core@7.26.9)
      babel-plugin-react-native-web: 0.19.13
      babel-plugin-syntax-hermes-parser: 0.25.1
      babel-plugin-transform-flow-enums: 0.0.2(@babel/core@7.26.9)
      debug: 4.4.0
      react-refresh: 0.14.2
      resolve-from: 5.0.0
    transitivePeerDependencies:
      - '@babel/core'
      - supports-color

  babel-preset-jest@29.6.3(@babel/core@7.26.9):
    dependencies:
      '@babel/core': 7.26.9
      babel-plugin-jest-hoist: 29.6.3
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.26.9)

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  better-opn@3.0.2:
    dependencies:
      open: 8.4.2

  big-integer@1.6.52: {}

  bignumber.js@9.1.2: {}

  boolbase@1.0.0: {}

  bowser@2.11.0: {}

  bplist-creator@0.1.0:
    dependencies:
      stream-buffers: 2.2.0

  bplist-parser@0.3.1:
    dependencies:
      big-integer: 1.6.52

  bplist-parser@0.3.2:
    dependencies:
      big-integer: 1.6.52

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.24.4:
    dependencies:
      caniuse-lite: 1.0.30001701
      electron-to-chromium: 1.5.109
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.24.4)

  bs-logger@0.2.6:
    dependencies:
      fast-json-stable-stringify: 2.1.0

  bser@2.1.1:
    dependencies:
      node-int64: 0.4.0

  buffer-from@1.1.2: {}

  buffer@4.9.2:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1
      isarray: 1.0.0

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  builtins@5.1.0:
    dependencies:
      semver: 7.7.1

  bytes@3.1.2: {}

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  call-bind@1.0.8:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      get-intrinsic: 1.3.0
      set-function-length: 1.2.2

  call-bound@1.0.3:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      get-intrinsic: 1.3.0

  caller-callsite@2.0.0:
    dependencies:
      callsites: 2.0.0

  caller-path@2.0.0:
    dependencies:
      caller-callsite: 2.0.0

  callsites@2.0.0: {}

  callsites@3.1.0: {}

  camelcase@5.3.1: {}

  camelcase@6.3.0: {}

  caniuse-lite@1.0.30001701: {}

  canvaskit-wasm@0.40.0:
    dependencies:
      '@webgpu/types': 0.1.21

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@3.0.0:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  char-regex@1.0.2: {}

  char-regex@2.0.2: {}

  chownr@3.0.0: {}

  chrome-launcher@0.15.2:
    dependencies:
      '@types/node': 22.13.5
      escape-string-regexp: 4.0.0
      is-wsl: 2.2.0
      lighthouse-logger: 1.4.2
    transitivePeerDependencies:
      - supports-color

  chrome-trace-event@1.0.4: {}

  chromium-edge-launcher@0.2.0:
    dependencies:
      '@types/node': 22.13.5
      escape-string-regexp: 4.0.0
      is-wsl: 2.2.0
      lighthouse-logger: 1.4.2
      mkdirp: 1.0.4
      rimraf: 3.0.2
    transitivePeerDependencies:
      - supports-color

  ci-info@2.0.0: {}

  ci-info@3.9.0: {}

  cjs-module-lexer@1.4.3: {}

  cli-cursor@2.1.0:
    dependencies:
      restore-cursor: 2.0.0

  cli-spinners@2.9.2: {}

  cliui@7.0.4:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone@1.0.4: {}

  co@4.6.0: {}

  collect-v8-coverage@1.0.2: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  color-string@1.9.1:
    dependencies:
      color-name: 1.1.4
      simple-swizzle: 0.2.2

  color@3.2.1:
    dependencies:
      color-convert: 1.9.3
      color-string: 1.9.1

  color@4.2.3:
    dependencies:
      color-convert: 2.0.1
      color-string: 1.9.1

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  command-exists@1.2.9: {}

  commander@12.1.0: {}

  commander@2.20.3: {}

  commander@4.1.1: {}

  commander@7.2.0: {}

  compressible@2.0.18:
    dependencies:
      mime-db: 1.53.0

  compression@1.8.0:
    dependencies:
      bytes: 3.1.2
      compressible: 2.0.18
      debug: 2.6.9
      negotiator: 0.6.4
      on-headers: 1.0.2
      safe-buffer: 5.2.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  concat-map@0.0.1: {}

  connect@3.7.0:
    dependencies:
      debug: 2.6.9
      finalhandler: 1.1.2
      parseurl: 1.3.3
      utils-merge: 1.0.1
    transitivePeerDependencies:
      - supports-color

  convert-source-map@2.0.0: {}

  core-js-compat@3.40.0:
    dependencies:
      browserslist: 4.24.4

  cosmiconfig@5.2.1:
    dependencies:
      import-fresh: 2.0.0
      is-directory: 0.3.1
      js-yaml: 3.14.1
      parse-json: 4.0.0

  crc-32@1.2.2: {}

  create-jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3)):
    dependencies:
      '@jest/types': 29.6.3
      chalk: 4.1.2
      exit: 0.1.2
      graceful-fs: 4.2.11
      jest-config: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))
      jest-util: 29.7.0
      prompts: 2.4.2
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  create-require@1.1.1: {}

  cross-fetch@3.2.0:
    dependencies:
      node-fetch: 2.7.0
    transitivePeerDependencies:
      - encoding

  cross-spawn@7.0.6:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  crypto-random-string@2.0.0: {}

  css-in-js-utils@3.1.0:
    dependencies:
      hyphenate-style-name: 1.1.0

  css-select@5.1.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 5.0.3
      domutils: 3.2.2
      nth-check: 2.1.1

  css-tree@1.1.3:
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1

  css-what@6.1.0: {}

  cssom@0.3.8: {}

  cssom@0.5.0: {}

  cssstyle@2.3.0:
    dependencies:
      cssom: 0.3.8

  csstype@3.1.3: {}

  d3-array@3.2.4:
    dependencies:
      internmap: 2.0.3

  d3-color@3.1.0: {}

  d3-dispatch@3.0.1: {}

  d3-drag@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-selection: 3.0.0

  d3-ease@3.0.1: {}

  d3-format@3.1.0: {}

  d3-interpolate@3.0.1:
    dependencies:
      d3-color: 3.1.0

  d3-path@3.1.0: {}

  d3-scale@4.0.2:
    dependencies:
      d3-array: 3.2.4
      d3-format: 3.1.0
      d3-interpolate: 3.0.1
      d3-time: 3.1.0
      d3-time-format: 4.1.0

  d3-selection@3.0.0: {}

  d3-shape@3.2.0:
    dependencies:
      d3-path: 3.1.0

  d3-time-format@4.1.0:
    dependencies:
      d3-time: 3.1.0

  d3-time@3.1.0:
    dependencies:
      d3-array: 3.2.4

  d3-timer@3.0.1: {}

  d3-transition@3.0.1(d3-selection@3.0.0):
    dependencies:
      d3-color: 3.1.0
      d3-dispatch: 3.0.1
      d3-ease: 3.0.1
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-timer: 3.0.1

  d3-zoom@3.0.0:
    dependencies:
      d3-dispatch: 3.0.1
      d3-drag: 3.0.0
      d3-interpolate: 3.0.1
      d3-selection: 3.0.0
      d3-transition: 3.0.1(d3-selection@3.0.0)

  data-urls@3.0.2:
    dependencies:
      abab: 2.0.6
      whatwg-mimetype: 3.0.0
      whatwg-url: 11.0.0

  data-view-buffer@1.0.2:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-length@1.0.2:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2

  data-view-byte-offset@1.0.1:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-data-view: 1.0.2

  date-fns@2.30.0:
    dependencies:
      '@babel/runtime': 7.26.9

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@3.2.7:
    dependencies:
      ms: 2.1.3

  debug@4.4.0:
    dependencies:
      ms: 2.1.3

  decimal.js@10.5.0: {}

  decode-uri-component@0.2.2: {}

  dedent@1.5.3: {}

  deep-extend@0.6.0: {}

  deep-is@0.1.4: {}

  deepmerge@4.3.1: {}

  defaults@1.0.4:
    dependencies:
      clone: 1.0.4

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.1
      es-errors: 1.3.0
      gopd: 1.2.0

  define-lazy-prop@2.0.0: {}

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  delayed-stream@1.0.0: {}

  denque@2.1.0: {}

  depd@2.0.0: {}

  destroy@1.2.0: {}

  detect-libc@1.0.3: {}

  detect-newline@3.1.0: {}

  diff-sequences@29.6.3: {}

  diff@4.0.2: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  domelementtype@2.3.0: {}

  domexception@4.0.0:
    dependencies:
      webidl-conversions: 7.0.0

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  domutils@3.2.2:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  dotenv-expand@11.0.7:
    dependencies:
      dotenv: 16.4.7

  dotenv@16.4.7: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  eastasianwidth@0.2.0: {}

  ee-first@1.1.1: {}

  ejs@3.1.10:
    dependencies:
      jake: 10.9.2

  electron-to-chromium@1.5.109: {}

  emittery@0.13.1: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  encodeurl@1.0.2: {}

  encodeurl@2.0.0: {}

  enhanced-resolve@5.18.1:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  entities@4.5.0: {}

  env-editor@0.4.2: {}

  eol@0.9.1: {}

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  error-stack-parser@2.1.4:
    dependencies:
      stackframe: 1.3.4

  es-abstract@1.23.9:
    dependencies:
      array-buffer-byte-length: 1.0.2
      arraybuffer.prototype.slice: 1.0.4
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.3
      data-view-buffer: 1.0.2
      data-view-byte-length: 1.0.2
      data-view-byte-offset: 1.0.1
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      es-set-tostringtag: 2.1.0
      es-to-primitive: 1.3.0
      function.prototype.name: 1.1.8
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      get-symbol-description: 1.1.0
      globalthis: 1.0.4
      gopd: 1.2.0
      has-property-descriptors: 1.0.2
      has-proto: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      internal-slot: 1.1.0
      is-array-buffer: 3.0.5
      is-callable: 1.2.7
      is-data-view: 1.0.2
      is-regex: 1.2.1
      is-shared-array-buffer: 1.0.4
      is-string: 1.1.1
      is-typed-array: 1.1.15
      is-weakref: 1.1.1
      math-intrinsics: 1.1.0
      object-inspect: 1.13.4
      object-keys: 1.1.1
      object.assign: 4.1.7
      own-keys: 1.0.1
      regexp.prototype.flags: 1.5.4
      safe-array-concat: 1.1.3
      safe-push-apply: 1.0.0
      safe-regex-test: 1.1.0
      set-proto: 1.0.0
      string.prototype.trim: 1.2.10
      string.prototype.trimend: 1.0.9
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.3
      typed-array-byte-length: 1.0.3
      typed-array-byte-offset: 1.0.4
      typed-array-length: 1.0.7
      unbox-primitive: 1.1.0
      which-typed-array: 1.1.18

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-module-lexer@1.6.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.1.0:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.3.0:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.1.0
      is-symbol: 1.1.1

  escalade@3.2.0: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@2.0.0: {}

  escape-string-regexp@4.0.0: {}

  escodegen@2.1.0:
    dependencies:
      esprima: 4.0.1
      estraverse: 5.3.0
      esutils: 2.0.3
    optionalDependencies:
      source-map: 0.6.1

  eslint-config-prettier@8.5.0(eslint@8.17.0):
    dependencies:
      eslint: 8.17.0

  eslint-config-standard@17.0.0(eslint-plugin-import@2.26.0(@typescript-eslint/parser@5.62.0(eslint@8.17.0)(typescript@5.8.3))(eslint@8.17.0))(eslint-plugin-n@15.7.0(eslint@8.17.0))(eslint-plugin-promise@6.0.0(eslint@8.17.0))(eslint@8.17.0):
    dependencies:
      eslint: 8.17.0
      eslint-plugin-import: 2.26.0(@typescript-eslint/parser@5.62.0(eslint@8.17.0)(typescript@5.8.3))(eslint@8.17.0)
      eslint-plugin-n: 15.7.0(eslint@8.17.0)
      eslint-plugin-promise: 6.0.0(eslint@8.17.0)

  eslint-import-resolver-node@0.3.9:
    dependencies:
      debug: 3.2.7
      is-core-module: 2.16.1
      resolve: 1.22.10
    transitivePeerDependencies:
      - supports-color

  eslint-module-utils@2.12.0(@typescript-eslint/parser@5.62.0(eslint@8.17.0)(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint@8.17.0):
    dependencies:
      debug: 3.2.7
    optionalDependencies:
      '@typescript-eslint/parser': 5.62.0(eslint@8.17.0)(typescript@5.8.3)
      eslint: 8.17.0
      eslint-import-resolver-node: 0.3.9
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-es@4.1.0(eslint@8.17.0):
    dependencies:
      eslint: 8.17.0
      eslint-utils: 2.1.0
      regexpp: 3.2.0

  eslint-plugin-import@2.26.0(@typescript-eslint/parser@5.62.0(eslint@8.17.0)(typescript@5.8.3))(eslint@8.17.0):
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.3
      debug: 2.6.9
      doctrine: 2.1.0
      eslint: 8.17.0
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.12.0(@typescript-eslint/parser@5.62.0(eslint@8.17.0)(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint@8.17.0)
      has: 1.0.4
      is-core-module: 2.16.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.values: 1.2.1
      resolve: 1.22.10
      tsconfig-paths: 3.15.0
    optionalDependencies:
      '@typescript-eslint/parser': 5.62.0(eslint@8.17.0)(typescript@5.8.3)
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  eslint-plugin-n@15.7.0(eslint@8.17.0):
    dependencies:
      builtins: 5.1.0
      eslint: 8.17.0
      eslint-plugin-es: 4.1.0(eslint@8.17.0)
      eslint-utils: 3.0.0(eslint@8.17.0)
      ignore: 5.3.2
      is-core-module: 2.16.1
      minimatch: 3.1.2
      resolve: 1.22.10
      semver: 7.7.1

  eslint-plugin-promise@6.0.0(eslint@8.17.0):
    dependencies:
      eslint: 8.17.0

  eslint-plugin-react-native-globals@0.1.2: {}

  eslint-plugin-react-native@4.0.0(eslint@8.17.0):
    dependencies:
      '@babel/traverse': 7.26.9
      eslint: 8.17.0
      eslint-plugin-react-native-globals: 0.1.2
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-react@7.30.0(eslint@8.17.0):
    dependencies:
      array-includes: 3.1.8
      array.prototype.flatmap: 1.3.3
      doctrine: 2.1.0
      eslint: 8.17.0
      estraverse: 5.3.0
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.8
      object.fromentries: 2.0.8
      object.hasown: 1.1.4
      object.values: 1.2.1
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.12

  eslint-plugin-reactotron@0.1.6(reactotron-core-client@2.9.7):
    dependencies:
      reactotron-core-client: 2.9.7

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@7.2.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-utils@2.1.0:
    dependencies:
      eslint-visitor-keys: 1.3.0

  eslint-utils@3.0.0(eslint@8.17.0):
    dependencies:
      eslint: 8.17.0
      eslint-visitor-keys: 2.1.0

  eslint-visitor-keys@1.3.0: {}

  eslint-visitor-keys@2.1.0: {}

  eslint-visitor-keys@3.4.3: {}

  eslint@8.17.0:
    dependencies:
      '@eslint/eslintrc': 1.4.1
      '@humanwhocodes/config-array': 0.9.5
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.6
      debug: 4.4.0
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-utils: 3.0.0(eslint@8.17.0)
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.6.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      functional-red-black-tree: 1.0.1
      glob-parent: 6.0.2
      globals: 13.24.0
      ignore: 5.3.2
      import-fresh: 3.3.1
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      regexpp: 3.2.0
      strip-ansi: 6.0.1
      strip-json-comments: 3.1.1
      text-table: 0.2.0
      v8-compile-cache: 2.4.0
    transitivePeerDependencies:
      - supports-color

  espree@9.6.1:
    dependencies:
      acorn: 8.14.0
      acorn-jsx: 5.3.2(acorn@8.14.0)
      eslint-visitor-keys: 3.4.3

  esprima@4.0.1: {}

  esquery@1.6.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  etag@1.8.1: {}

  event-target-shim@5.0.1: {}

  events@3.3.0: {}

  exec-async@2.2.0: {}

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.6
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  exit@0.1.2: {}

  expect@29.7.0:
    dependencies:
      '@jest/expect-utils': 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0

  expo-application@6.1.4(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)):
    dependencies:
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)

  expo-asset@11.1.5(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      '@expo/image-utils': 0.7.4
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      expo-constants: 17.1.6(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
    transitivePeerDependencies:
      - supports-color

  expo-auth-session@6.1.5(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      expo-application: 6.1.4(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))
      expo-constants: 17.1.6(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      expo-crypto: 14.1.4(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))
      expo-linking: 7.1.5(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      expo-web-browser: 14.1.6(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      invariant: 2.2.4
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
    transitivePeerDependencies:
      - expo
      - supports-color

  expo-build-properties@0.14.6(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)):
    dependencies:
      ajv: 8.17.1
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      semver: 7.7.1

  expo-constants@17.1.6(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)):
    dependencies:
      '@expo/config': 11.0.10
      '@expo/env': 1.0.5
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
    transitivePeerDependencies:
      - supports-color

  expo-crypto@14.1.4(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)):
    dependencies:
      base64-js: 1.5.1
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)

  expo-file-system@18.1.10(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)):
    dependencies:
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  expo-font@13.3.1(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react@19.0.0):
    dependencies:
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      fontfaceobserver: 2.3.0
      react: 19.0.0

  expo-keep-awake@14.1.4(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react@19.0.0):
    dependencies:
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react: 19.0.0

  expo-linking@7.1.5(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      expo-constants: 17.1.6(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      invariant: 2.2.4
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
    transitivePeerDependencies:
      - expo
      - supports-color

  expo-localization@16.1.5(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react@19.0.0):
    dependencies:
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react: 19.0.0
      rtl-detect: 1.1.2

  expo-modules-autolinking@2.1.10:
    dependencies:
      '@expo/spawn-async': 1.7.2
      chalk: 4.1.2
      commander: 7.2.0
      find-up: 5.0.0
      glob: 10.4.5
      require-from-string: 2.0.2
      resolve-from: 5.0.0

  expo-modules-core@2.3.13:
    dependencies:
      invariant: 2.2.4

  expo-secure-store@14.2.3(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)):
    dependencies:
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)

  expo-splash-screen@0.30.8(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)):
    dependencies:
      '@expo/prebuild-config': 9.0.6
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
    transitivePeerDependencies:
      - supports-color

  expo-status-bar@2.2.3(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-native-edge-to-edge: 1.6.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-is-edge-to-edge: 1.1.7(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)

  expo-web-browser@14.1.6(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)):
    dependencies:
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      '@babel/runtime': 7.26.9
      '@expo/cli': 0.24.13(graphql@15.10.1)
      '@expo/config': 11.0.10
      '@expo/config-plugins': 10.0.2
      '@expo/fingerprint': 0.12.4
      '@expo/metro-config': 0.20.14
      '@expo/vector-icons': 14.0.4
      babel-preset-expo: 13.1.11(@babel/core@7.26.9)
      expo-asset: 11.1.5(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      expo-constants: 17.1.6(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      expo-file-system: 18.1.10(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      expo-font: 13.3.1(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react@19.0.0)
      expo-keep-awake: 14.1.4(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react@19.0.0)
      expo-modules-autolinking: 2.1.10
      expo-modules-core: 2.3.13
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-native-edge-to-edge: 1.6.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      whatwg-url-without-unicode: 8.0.0-3
    optionalDependencies:
      '@expo/metro-runtime': 5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      react-native-webview: 13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
    transitivePeerDependencies:
      - '@babel/core'
      - babel-plugin-react-compiler
      - bufferutil
      - graphql
      - supports-color
      - utf-8-validate

  exponential-backoff@3.1.2: {}

  fast-base64-decode@1.0.0: {}

  fast-deep-equal@3.1.3: {}

  fast-fuzzy@1.12.0:
    dependencies:
      graphemesplit: 2.6.0

  fast-glob@3.3.3:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.8

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-uri@3.0.6: {}

  fast-xml-parser@4.4.1:
    dependencies:
      strnum: 1.1.2

  fast-xml-parser@4.5.3:
    dependencies:
      strnum: 1.1.2

  fastq@1.19.1:
    dependencies:
      reusify: 1.1.0

  fb-watchman@2.0.2:
    dependencies:
      bser: 2.1.1

  fbjs-css-vars@1.0.2: {}

  fbjs@3.0.5:
    dependencies:
      cross-fetch: 3.2.0
      fbjs-css-vars: 1.0.2
      loose-envify: 1.4.0
      object-assign: 4.1.1
      promise: 7.3.1
      setimmediate: 1.0.5
      ua-parser-js: 1.0.40
    transitivePeerDependencies:
      - encoding

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.2.0

  filelist@1.0.4:
    dependencies:
      minimatch: 5.1.6

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  filter-obj@1.1.0: {}

  finalhandler@1.1.2:
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.3.0
      parseurl: 1.3.3
      statuses: 1.5.0
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  find-yarn-workspace-root@2.0.0:
    dependencies:
      micromatch: 4.0.8

  flat-cache@3.2.0:
    dependencies:
      flatted: 3.3.3
      keyv: 4.5.4
      rimraf: 3.0.2

  flatted@3.3.3: {}

  flow-enums-runtime@0.0.6: {}

  follow-redirects@1.15.9: {}

  fontfaceobserver@2.3.0: {}

  for-each@0.3.5:
    dependencies:
      is-callable: 1.2.7

  foreground-child@3.3.1:
    dependencies:
      cross-spawn: 7.0.6
      signal-exit: 4.1.0

  form-data@4.0.2:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35

  freeport-async@2.0.0: {}

  fresh@0.5.2: {}

  fs-extra@9.1.0:
    dependencies:
      at-least-node: 1.0.0
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.8:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      functions-have-names: 1.2.3
      hasown: 2.0.2
      is-callable: 1.2.7

  functional-red-black-tree@1.0.1: {}

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-package-type@0.1.0: {}

  get-port@3.2.0: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  get-stream@6.0.1: {}

  get-symbol-description@1.1.0:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.3.0

  getenv@1.0.0: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.4.1: {}

  glob@10.4.5:
    dependencies:
      foreground-child: 3.3.1
      jackspeak: 3.4.3
      minimatch: 9.0.5
      minipass: 7.1.2
      package-json-from-dist: 1.0.1
      path-scurry: 1.11.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  globals@11.12.0: {}

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.2.0

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.3
      ignore: 5.3.2
      merge2: 1.4.1
      slash: 3.0.0

  gopd@1.2.0: {}

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  graphemesplit@2.6.0:
    dependencies:
      js-base64: 3.7.7
      unicode-trie: 2.0.0

  graphql@15.10.1:
    optional: true

  graphql@15.8.0: {}

  has-bigints@1.1.0: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.1

  has-proto@1.2.0:
    dependencies:
      dunder-proto: 1.0.1

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  has@1.0.4: {}

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  hermes-estree@0.25.1: {}

  hermes-estree@0.28.1: {}

  hermes-parser@0.25.1:
    dependencies:
      hermes-estree: 0.25.1

  hermes-parser@0.28.1:
    dependencies:
      hermes-estree: 0.28.1

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  hosted-git-info@7.0.2:
    dependencies:
      lru-cache: 10.4.3

  html-encoding-sniffer@3.0.0:
    dependencies:
      whatwg-encoding: 2.0.0

  html-escaper@2.0.2: {}

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-proxy-agent@5.0.0:
    dependencies:
      '@tootallnate/once': 2.0.0
      agent-base: 6.0.2
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@7.0.6:
    dependencies:
      agent-base: 7.1.3
      debug: 4.4.0
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  humps@2.0.1: {}

  hyphenate-style-name@1.1.0: {}

  i18n-js@4.5.1:
    dependencies:
      bignumber.js: 9.1.2
      lodash: 4.17.21
      make-plural: 7.4.0

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  idb@5.0.6: {}

  ieee754@1.2.1: {}

  ignore@5.3.2: {}

  image-size@1.2.0:
    dependencies:
      queue: 6.0.2

  immer@9.0.6: {}

  import-fresh@2.0.0:
    dependencies:
      caller-path: 2.0.0
      resolve-from: 3.0.0

  import-fresh@3.3.1:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  import-local@3.2.0:
    dependencies:
      pkg-dir: 4.2.0
      resolve-cwd: 3.0.0

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  ini@1.3.8: {}

  inline-style-prefixer@7.0.1:
    dependencies:
      css-in-js-utils: 3.1.0

  internal-slot@1.1.0:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.1.0

  internmap@2.0.3: {}

  invariant@2.2.4:
    dependencies:
      loose-envify: 1.4.0

  is-array-buffer@3.0.5:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      get-intrinsic: 1.3.0

  is-arrayish@0.2.1: {}

  is-arrayish@0.3.2: {}

  is-async-function@2.1.1:
    dependencies:
      async-function: 1.0.0
      call-bound: 1.0.3
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-bigint@1.1.0:
    dependencies:
      has-bigints: 1.1.0

  is-boolean-object@1.2.2:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-callable@1.2.7: {}

  is-core-module@2.16.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.2:
    dependencies:
      call-bound: 1.0.3
      get-intrinsic: 1.3.0
      is-typed-array: 1.1.15

  is-date-object@1.1.0:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-directory@0.3.1: {}

  is-docker@2.2.1: {}

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.1.1:
    dependencies:
      call-bound: 1.0.3

  is-fullwidth-code-point@3.0.0: {}

  is-generator-fn@2.1.0: {}

  is-generator-function@1.1.0:
    dependencies:
      call-bound: 1.0.3
      get-proto: 1.0.1
      has-tostringtag: 1.0.2
      safe-regex-test: 1.1.0

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-map@2.0.3: {}

  is-number-object@1.1.1:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-plain-obj@2.1.0: {}

  is-potential-custom-element-name@1.0.1: {}

  is-regex@1.2.1:
    dependencies:
      call-bound: 1.0.3
      gopd: 1.2.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.4:
    dependencies:
      call-bound: 1.0.3

  is-stream@2.0.1: {}

  is-string@1.1.1:
    dependencies:
      call-bound: 1.0.3
      has-tostringtag: 1.0.2

  is-symbol@1.1.1:
    dependencies:
      call-bound: 1.0.3
      has-symbols: 1.1.0
      safe-regex-test: 1.1.0

  is-typed-array@1.1.15:
    dependencies:
      which-typed-array: 1.1.18

  is-weakmap@2.0.2: {}

  is-weakref@1.1.1:
    dependencies:
      call-bound: 1.0.3

  is-weakset@2.0.4:
    dependencies:
      call-bound: 1.0.3
      get-intrinsic: 1.3.0

  is-wsl@2.2.0:
    dependencies:
      is-docker: 2.2.1

  isarray@1.0.0: {}

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-instrument@5.2.1:
    dependencies:
      '@babel/core': 7.26.9
      '@babel/parser': 7.26.9
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-instrument@6.0.3:
    dependencies:
      '@babel/core': 7.26.9
      '@babel/parser': 7.26.9
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 7.7.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@4.0.1:
    dependencies:
      debug: 4.4.0
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.7:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  its-fine@1.2.5(@types/react@19.0.14)(react@19.0.0):
    dependencies:
      '@types/react-reconciler': 0.28.9(@types/react@19.0.14)
      react: 19.0.0
    transitivePeerDependencies:
      - '@types/react'

  jackspeak@3.4.3:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jake@10.9.2:
    dependencies:
      async: 3.2.6
      chalk: 4.1.2
      filelist: 1.0.4
      minimatch: 3.1.2

  jest-changed-files@29.7.0:
    dependencies:
      execa: 5.1.1
      jest-util: 29.7.0
      p-limit: 3.1.0

  jest-circus@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/expect': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.13.5
      chalk: 4.1.2
      co: 4.6.0
      dedent: 1.5.3
      is-generator-fn: 2.1.0
      jest-each: 29.7.0
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-runtime: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      p-limit: 3.1.0
      pretty-format: 29.7.0
      pure-rand: 6.1.0
      slash: 3.0.0
      stack-utils: 2.0.6
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-cli@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3)):
    dependencies:
      '@jest/core': 29.7.0(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      chalk: 4.1.2
      create-jest: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))
      exit: 0.1.2
      import-local: 3.2.0
      jest-config: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))
      jest-util: 29.7.0
      jest-validate: 29.7.0
      yargs: 17.7.2
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  jest-config@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3)):
    dependencies:
      '@babel/core': 7.26.9
      '@jest/test-sequencer': 29.7.0
      '@jest/types': 29.6.3
      babel-jest: 29.7.0(@babel/core@7.26.9)
      chalk: 4.1.2
      ci-info: 3.9.0
      deepmerge: 4.3.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-circus: 29.7.0
      jest-environment-node: 29.7.0
      jest-get-type: 29.6.3
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-runner: 29.7.0
      jest-util: 29.7.0
      jest-validate: 29.7.0
      micromatch: 4.0.8
      parse-json: 5.2.0
      pretty-format: 29.7.0
      slash: 3.0.0
      strip-json-comments: 3.1.1
    optionalDependencies:
      '@types/node': 22.13.5
      ts-node: 10.9.2(@types/node@22.13.5)(typescript@5.8.3)
    transitivePeerDependencies:
      - babel-plugin-macros
      - supports-color

  jest-diff@29.7.0:
    dependencies:
      chalk: 4.1.2
      diff-sequences: 29.6.3
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-docblock@29.7.0:
    dependencies:
      detect-newline: 3.1.0

  jest-each@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      chalk: 4.1.2
      jest-get-type: 29.6.3
      jest-util: 29.7.0
      pretty-format: 29.7.0

  jest-environment-jsdom@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/fake-timers': 29.7.0
      '@jest/types': 29.6.3
      '@types/jsdom': 20.0.1
      '@types/node': 22.13.5
      jest-mock: 29.7.0
      jest-util: 29.7.0
      jsdom: 20.0.3
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jest-environment-node@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/fake-timers': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.13.5
      jest-mock: 29.7.0
      jest-util: 29.7.0

  jest-expo@53.0.5(@babel/core@7.26.9)(expo@53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3)))(react-dom@19.0.0(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)(webpack@5.98.0):
    dependencies:
      '@expo/config': 11.0.10
      '@expo/json-file': 9.1.4
      '@jest/create-cache-key-function': 29.7.0
      '@jest/globals': 29.7.0
      babel-jest: 29.7.0(@babel/core@7.26.9)
      expo: 53.0.9(@babel/core@7.26.9)(@expo/metro-runtime@5.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)))(graphql@15.10.1)(react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      find-up: 5.0.0
      jest-environment-jsdom: 29.7.0
      jest-snapshot: 29.7.0
      jest-watch-select-projects: 2.0.0
      jest-watch-typeahead: 2.2.1(jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3)))
      json5: 2.2.3
      lodash: 4.17.21
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-server-dom-webpack: 19.0.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(webpack@5.98.0)
      react-test-renderer: 19.0.0(react@19.0.0)
      server-only: 0.0.1
      stacktrace-js: 2.0.2
    transitivePeerDependencies:
      - '@babel/core'
      - bufferutil
      - canvas
      - jest
      - react
      - react-dom
      - supports-color
      - utf-8-validate
      - webpack

  jest-get-type@29.6.3: {}

  jest-haste-map@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/graceful-fs': 4.1.9
      '@types/node': 22.13.5
      anymatch: 3.1.3
      fb-watchman: 2.0.2
      graceful-fs: 4.2.11
      jest-regex-util: 29.6.3
      jest-util: 29.7.0
      jest-worker: 29.7.0
      micromatch: 4.0.8
      walker: 1.0.8
    optionalDependencies:
      fsevents: 2.3.3

  jest-leak-detector@29.7.0:
    dependencies:
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-matcher-utils@29.7.0:
    dependencies:
      chalk: 4.1.2
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      pretty-format: 29.7.0

  jest-message-util@29.7.0:
    dependencies:
      '@babel/code-frame': 7.26.2
      '@jest/types': 29.6.3
      '@types/stack-utils': 2.0.3
      chalk: 4.1.2
      graceful-fs: 4.2.11
      micromatch: 4.0.8
      pretty-format: 29.7.0
      slash: 3.0.0
      stack-utils: 2.0.6

  jest-mock@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 22.13.5
      jest-util: 29.7.0

  jest-pnp-resolver@1.2.3(jest-resolve@29.7.0):
    optionalDependencies:
      jest-resolve: 29.7.0

  jest-regex-util@29.6.3: {}

  jest-resolve-dependencies@29.7.0:
    dependencies:
      jest-regex-util: 29.6.3
      jest-snapshot: 29.7.0
    transitivePeerDependencies:
      - supports-color

  jest-resolve@29.7.0:
    dependencies:
      chalk: 4.1.2
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-pnp-resolver: 1.2.3(jest-resolve@29.7.0)
      jest-util: 29.7.0
      jest-validate: 29.7.0
      resolve: 1.22.10
      resolve.exports: 2.0.3
      slash: 3.0.0

  jest-runner@29.7.0:
    dependencies:
      '@jest/console': 29.7.0
      '@jest/environment': 29.7.0
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.13.5
      chalk: 4.1.2
      emittery: 0.13.1
      graceful-fs: 4.2.11
      jest-docblock: 29.7.0
      jest-environment-node: 29.7.0
      jest-haste-map: 29.7.0
      jest-leak-detector: 29.7.0
      jest-message-util: 29.7.0
      jest-resolve: 29.7.0
      jest-runtime: 29.7.0
      jest-util: 29.7.0
      jest-watcher: 29.7.0
      jest-worker: 29.7.0
      p-limit: 3.1.0
      source-map-support: 0.5.13
    transitivePeerDependencies:
      - supports-color

  jest-runtime@29.7.0:
    dependencies:
      '@jest/environment': 29.7.0
      '@jest/fake-timers': 29.7.0
      '@jest/globals': 29.7.0
      '@jest/source-map': 29.6.3
      '@jest/test-result': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.13.5
      chalk: 4.1.2
      cjs-module-lexer: 1.4.3
      collect-v8-coverage: 1.0.2
      glob: 7.2.3
      graceful-fs: 4.2.11
      jest-haste-map: 29.7.0
      jest-message-util: 29.7.0
      jest-mock: 29.7.0
      jest-regex-util: 29.6.3
      jest-resolve: 29.7.0
      jest-snapshot: 29.7.0
      jest-util: 29.7.0
      slash: 3.0.0
      strip-bom: 4.0.0
    transitivePeerDependencies:
      - supports-color

  jest-snapshot@29.7.0:
    dependencies:
      '@babel/core': 7.26.9
      '@babel/generator': 7.26.9
      '@babel/plugin-syntax-jsx': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-syntax-typescript': 7.25.9(@babel/core@7.26.9)
      '@babel/types': 7.26.9
      '@jest/expect-utils': 29.7.0
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      babel-preset-current-node-syntax: 1.1.0(@babel/core@7.26.9)
      chalk: 4.1.2
      expect: 29.7.0
      graceful-fs: 4.2.11
      jest-diff: 29.7.0
      jest-get-type: 29.6.3
      jest-matcher-utils: 29.7.0
      jest-message-util: 29.7.0
      jest-util: 29.7.0
      natural-compare: 1.4.0
      pretty-format: 29.7.0
      semver: 7.7.1
    transitivePeerDependencies:
      - supports-color

  jest-util@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      '@types/node': 22.13.5
      chalk: 4.1.2
      ci-info: 3.9.0
      graceful-fs: 4.2.11
      picomatch: 2.3.1

  jest-validate@29.7.0:
    dependencies:
      '@jest/types': 29.6.3
      camelcase: 6.3.0
      chalk: 4.1.2
      jest-get-type: 29.6.3
      leven: 3.1.0
      pretty-format: 29.7.0

  jest-watch-select-projects@2.0.0:
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 3.0.0
      prompts: 2.4.2

  jest-watch-typeahead@2.2.1(jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))):
    dependencies:
      ansi-escapes: 6.2.1
      chalk: 4.1.2
      jest: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))
      jest-regex-util: 29.6.3
      jest-watcher: 29.7.0
      slash: 5.1.0
      string-length: 5.0.1
      strip-ansi: 7.1.0

  jest-watcher@29.7.0:
    dependencies:
      '@jest/test-result': 29.7.0
      '@jest/types': 29.6.3
      '@types/node': 22.13.5
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      emittery: 0.13.1
      jest-util: 29.7.0
      string-length: 4.0.2

  jest-worker@27.5.1:
    dependencies:
      '@types/node': 22.13.5
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest-worker@29.7.0:
    dependencies:
      '@types/node': 22.13.5
      jest-util: 29.7.0
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3)):
    dependencies:
      '@jest/core': 29.7.0(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))
      '@jest/types': 29.6.3
      import-local: 3.2.0
      jest-cli: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))
    transitivePeerDependencies:
      - '@types/node'
      - babel-plugin-macros
      - supports-color
      - ts-node

  jimp-compact@0.16.1: {}

  js-base64@3.7.7: {}

  js-cookie@3.0.5: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsc-safe-url@0.2.4: {}

  jsdom@20.0.3:
    dependencies:
      abab: 2.0.6
      acorn: 8.14.0
      acorn-globals: 7.0.1
      cssom: 0.5.0
      cssstyle: 2.3.0
      data-urls: 3.0.2
      decimal.js: 10.5.0
      domexception: 4.0.0
      escodegen: 2.1.0
      form-data: 4.0.2
      html-encoding-sniffer: 3.0.0
      http-proxy-agent: 5.0.0
      https-proxy-agent: 5.0.1
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.16
      parse5: 7.2.1
      saxes: 6.0.0
      symbol-tree: 3.2.4
      tough-cookie: 4.1.4
      w3c-xmlserializer: 4.0.0
      webidl-conversions: 7.0.0
      whatwg-encoding: 2.0.0
      whatwg-mimetype: 3.0.0
      whatwg-url: 11.0.0
      ws: 8.18.1
      xml-name-validator: 4.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@3.0.2: {}

  jsesc@3.1.0: {}

  json-buffer@3.0.1: {}

  json-parse-better-errors@1.0.2: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json-stable-stringify@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      isarray: 2.0.5
      jsonify: 0.0.1
      object-keys: 1.1.1

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  json5@2.2.3: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonify@0.0.1: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.3
      object.assign: 4.1.7
      object.values: 1.2.1

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  klaw-sync@6.0.0:
    dependencies:
      graceful-fs: 4.2.11

  kleur@3.0.3: {}

  lan-network@0.1.6: {}

  leven@3.1.0: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lighthouse-logger@1.4.2:
    dependencies:
      debug: 2.6.9
      marky: 1.2.5
    transitivePeerDependencies:
      - supports-color

  lightningcss-darwin-arm64@1.27.0:
    optional: true

  lightningcss-darwin-x64@1.27.0:
    optional: true

  lightningcss-freebsd-x64@1.27.0:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.27.0:
    optional: true

  lightningcss-linux-arm64-gnu@1.27.0:
    optional: true

  lightningcss-linux-arm64-musl@1.27.0:
    optional: true

  lightningcss-linux-x64-gnu@1.27.0:
    optional: true

  lightningcss-linux-x64-musl@1.27.0:
    optional: true

  lightningcss-win32-arm64-msvc@1.27.0:
    optional: true

  lightningcss-win32-x64-msvc@1.27.0:
    optional: true

  lightningcss@1.27.0:
    dependencies:
      detect-libc: 1.0.3
    optionalDependencies:
      lightningcss-darwin-arm64: 1.27.0
      lightningcss-darwin-x64: 1.27.0
      lightningcss-freebsd-x64: 1.27.0
      lightningcss-linux-arm-gnueabihf: 1.27.0
      lightningcss-linux-arm64-gnu: 1.27.0
      lightningcss-linux-arm64-musl: 1.27.0
      lightningcss-linux-x64-gnu: 1.27.0
      lightningcss-linux-x64-musl: 1.27.0
      lightningcss-win32-arm64-msvc: 1.27.0
      lightningcss-win32-x64-msvc: 1.27.0

  lines-and-columns@1.2.4: {}

  loader-runner@4.3.0: {}

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash.debounce@4.0.8: {}

  lodash.isequal@4.5.0: {}

  lodash.memoize@4.1.2: {}

  lodash.merge@4.6.2: {}

  lodash.throttle@4.1.1: {}

  lodash@4.17.21: {}

  log-symbols@2.2.0:
    dependencies:
      chalk: 2.4.2

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  lru-cache@10.4.3: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  make-dir@4.0.0:
    dependencies:
      semver: 7.7.1

  make-error@1.3.6: {}

  make-plural@7.4.0: {}

  makeerror@1.0.12:
    dependencies:
      tmpl: 1.0.5

  marky@1.2.5: {}

  math-intrinsics@1.1.0: {}

  mdn-data@2.0.14: {}

  memoize-one@5.2.1: {}

  memoize-one@6.0.0: {}

  merge-options@3.0.4:
    dependencies:
      is-plain-obj: 2.1.0

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  metro-babel-transformer@0.82.3:
    dependencies:
      '@babel/core': 7.26.9
      flow-enums-runtime: 0.0.6
      hermes-parser: 0.28.1
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - supports-color

  metro-cache-key@0.82.3:
    dependencies:
      flow-enums-runtime: 0.0.6

  metro-cache@0.82.3:
    dependencies:
      exponential-backoff: 3.1.2
      flow-enums-runtime: 0.0.6
      https-proxy-agent: 7.0.6
      metro-core: 0.82.3
    transitivePeerDependencies:
      - supports-color

  metro-config@0.82.3:
    dependencies:
      connect: 3.7.0
      cosmiconfig: 5.2.1
      flow-enums-runtime: 0.0.6
      jest-validate: 29.7.0
      metro: 0.82.3
      metro-cache: 0.82.3
      metro-core: 0.82.3
      metro-runtime: 0.82.3
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  metro-core@0.82.3:
    dependencies:
      flow-enums-runtime: 0.0.6
      lodash.throttle: 4.1.1
      metro-resolver: 0.82.3

  metro-file-map@0.82.3:
    dependencies:
      debug: 4.4.0
      fb-watchman: 2.0.2
      flow-enums-runtime: 0.0.6
      graceful-fs: 4.2.11
      invariant: 2.2.4
      jest-worker: 29.7.0
      micromatch: 4.0.8
      nullthrows: 1.1.1
      walker: 1.0.8
    transitivePeerDependencies:
      - supports-color

  metro-minify-terser@0.82.3:
    dependencies:
      flow-enums-runtime: 0.0.6
      terser: 5.39.0

  metro-resolver@0.82.3:
    dependencies:
      flow-enums-runtime: 0.0.6

  metro-runtime@0.82.3:
    dependencies:
      '@babel/runtime': 7.26.9
      flow-enums-runtime: 0.0.6

  metro-source-map@0.82.3:
    dependencies:
      '@babel/traverse': 7.26.9
      '@babel/traverse--for-generate-function-map': '@babel/traverse@7.26.9'
      '@babel/types': 7.26.9
      flow-enums-runtime: 0.0.6
      invariant: 2.2.4
      metro-symbolicate: 0.82.3
      nullthrows: 1.1.1
      ob1: 0.82.3
      source-map: 0.5.7
      vlq: 1.0.1
    transitivePeerDependencies:
      - supports-color

  metro-symbolicate@0.82.3:
    dependencies:
      flow-enums-runtime: 0.0.6
      invariant: 2.2.4
      metro-source-map: 0.82.3
      nullthrows: 1.1.1
      source-map: 0.5.7
      vlq: 1.0.1
    transitivePeerDependencies:
      - supports-color

  metro-transform-plugins@0.82.3:
    dependencies:
      '@babel/core': 7.26.9
      '@babel/generator': 7.26.9
      '@babel/template': 7.26.9
      '@babel/traverse': 7.26.9
      flow-enums-runtime: 0.0.6
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - supports-color

  metro-transform-worker@0.82.3:
    dependencies:
      '@babel/core': 7.26.9
      '@babel/generator': 7.26.9
      '@babel/parser': 7.26.9
      '@babel/types': 7.26.9
      flow-enums-runtime: 0.0.6
      metro: 0.82.3
      metro-babel-transformer: 0.82.3
      metro-cache: 0.82.3
      metro-cache-key: 0.82.3
      metro-minify-terser: 0.82.3
      metro-source-map: 0.82.3
      metro-transform-plugins: 0.82.3
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  metro@0.82.3:
    dependencies:
      '@babel/code-frame': 7.26.2
      '@babel/core': 7.26.9
      '@babel/generator': 7.26.9
      '@babel/parser': 7.26.9
      '@babel/template': 7.26.9
      '@babel/traverse': 7.26.9
      '@babel/types': 7.26.9
      accepts: 1.3.8
      chalk: 4.1.2
      ci-info: 2.0.0
      connect: 3.7.0
      debug: 4.4.0
      error-stack-parser: 2.1.4
      flow-enums-runtime: 0.0.6
      graceful-fs: 4.2.11
      hermes-parser: 0.28.1
      image-size: 1.2.0
      invariant: 2.2.4
      jest-worker: 29.7.0
      jsc-safe-url: 0.2.4
      lodash.throttle: 4.1.1
      metro-babel-transformer: 0.82.3
      metro-cache: 0.82.3
      metro-cache-key: 0.82.3
      metro-config: 0.82.3
      metro-core: 0.82.3
      metro-file-map: 0.82.3
      metro-resolver: 0.82.3
      metro-runtime: 0.82.3
      metro-source-map: 0.82.3
      metro-symbolicate: 0.82.3
      metro-transform-plugins: 0.82.3
      metro-transform-worker: 0.82.3
      mime-types: 2.1.35
      nullthrows: 1.1.1
      serialize-error: 2.1.0
      source-map: 0.5.7
      throat: 5.0.0
      ws: 7.5.10
      yargs: 17.7.2
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-db@1.53.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.6.0: {}

  mimic-fn@1.2.0: {}

  mimic-fn@2.1.0: {}

  min-indent@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.5:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass@7.1.2: {}

  minizlib@3.0.2:
    dependencies:
      minipass: 7.1.2

  mitt@3.0.1: {}

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mkdirp@1.0.4: {}

  mkdirp@3.0.1: {}

  mobx-react-lite@4.0.5(mobx@6.10.2)(react-dom@19.0.0(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      mobx: 6.10.2
      react: 19.0.0
      use-sync-external-store: 1.4.0(react@19.0.0)
    optionalDependencies:
      react-dom: 19.0.0(react@19.0.0)
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  mobx-state-tree@5.3.0(mobx@6.10.2):
    dependencies:
      mobx: 6.10.2

  mobx@6.10.2: {}

  ms@2.0.0: {}

  ms@2.1.3: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nanoid@3.3.11: {}

  nanoid@3.3.8: {}

  natural-compare-lite@1.4.0: {}

  natural-compare@1.4.0: {}

  negotiator@0.6.3: {}

  negotiator@0.6.4: {}

  neo-async@2.6.2: {}

  nested-error-stacks@2.0.1: {}

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  node-forge@1.3.1: {}

  node-int64@0.4.0: {}

  node-releases@2.0.19: {}

  normalize-path@3.0.0: {}

  npm-package-arg@11.0.3:
    dependencies:
      hosted-git-info: 7.0.2
      proc-log: 4.2.0
      semver: 7.7.1
      validate-npm-package-name: 5.0.1

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  nullthrows@1.1.1: {}

  nwsapi@2.2.16: {}

  ob1@0.82.3:
    dependencies:
      flow-enums-runtime: 0.0.6

  object-assign@4.1.1: {}

  object-inspect@1.13.4: {}

  object-keys@1.1.1: {}

  object.assign@4.1.7:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.1.1
      has-symbols: 1.1.0
      object-keys: 1.1.1

  object.entries@1.1.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1

  object.hasown@1.1.4:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1

  object.values@1.2.1:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  on-finished@2.3.0:
    dependencies:
      ee-first: 1.1.1

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  on-headers@1.0.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@2.0.1:
    dependencies:
      mimic-fn: 1.2.0

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  open@7.4.2:
    dependencies:
      is-docker: 2.2.1
      is-wsl: 2.2.0

  open@8.4.2:
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0

  opencollective-postinstall@2.0.3: {}

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  ora@3.4.0:
    dependencies:
      chalk: 2.4.2
      cli-cursor: 2.1.0
      cli-spinners: 2.9.2
      log-symbols: 2.2.0
      strip-ansi: 5.2.0
      wcwidth: 1.0.1

  os-tmpdir@1.0.2: {}

  own-keys@1.0.1:
    dependencies:
      get-intrinsic: 1.3.0
      object-keys: 1.1.1
      safe-push-apply: 1.0.0

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-try@2.2.0: {}

  package-json-from-dist@1.0.1: {}

  pako@0.2.9: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@4.0.0:
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.26.2
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse-png@2.1.0:
    dependencies:
      pngjs: 3.4.0

  parse5@7.2.1:
    dependencies:
      entities: 4.5.0

  parseurl@1.3.3: {}

  password-prompt@1.1.3:
    dependencies:
      ansi-escapes: 4.3.2
      cross-spawn: 7.0.6

  patch-package@8.0.0:
    dependencies:
      '@yarnpkg/lockfile': 1.1.0
      chalk: 4.1.2
      ci-info: 3.9.0
      cross-spawn: 7.0.6
      find-yarn-workspace-root: 2.0.0
      fs-extra: 9.1.0
      json-stable-stringify: 1.2.1
      klaw-sync: 6.0.0
      minimist: 1.2.8
      open: 7.4.2
      rimraf: 2.7.1
      semver: 7.7.1
      slash: 2.0.0
      tmp: 0.0.33
      yaml: 2.7.0

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.4.3
      minipass: 7.1.2

  path-type@4.0.0: {}

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  picomatch@3.0.1: {}

  pirates@4.0.6: {}

  pkg-dir@4.2.0:
    dependencies:
      find-up: 4.1.0

  plist@3.1.0:
    dependencies:
      '@xmldom/xmldom': 0.8.10
      base64-js: 1.5.1
      xmlbuilder: 15.1.1

  pngjs@3.4.0: {}

  possible-typed-array-names@1.1.0: {}

  postcss-value-parser@4.2.0: {}

  postcss@8.4.49:
    dependencies:
      nanoid: 3.3.8
      picocolors: 1.1.1
      source-map-js: 1.2.1

  postinstall-prepare@1.0.1: {}

  prelude-ls@1.2.1: {}

  prettier@2.8.8: {}

  pretty-bytes@5.6.0: {}

  pretty-format@29.7.0:
    dependencies:
      '@jest/schemas': 29.6.3
      ansi-styles: 5.2.0
      react-is: 18.3.1

  proc-log@4.2.0: {}

  progress@2.0.3: {}

  promise@7.3.1:
    dependencies:
      asap: 2.0.6

  promise@8.3.0:
    dependencies:
      asap: 2.0.6

  prompts@2.4.2:
    dependencies:
      kleur: 3.0.3
      sisteransi: 1.0.5

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  proxy-from-env@1.1.0: {}

  psl@1.15.0:
    dependencies:
      punycode: 2.3.1

  punycode@2.3.1: {}

  pure-rand@6.1.0: {}

  qrcode-terminal@0.11.0: {}

  query-string@7.1.3:
    dependencies:
      decode-uri-component: 0.2.2
      filter-obj: 1.1.0
      split-on-first: 1.1.0
      strict-uri-encode: 2.0.0

  querystringify@2.2.0: {}

  queue-microtask@1.2.3: {}

  queue@6.0.2:
    dependencies:
      inherits: 2.0.4

  ramda@0.28.0: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  range-parser@1.2.1: {}

  rc@1.2.8:
    dependencies:
      deep-extend: 0.6.0
      ini: 1.3.8
      minimist: 1.2.8
      strip-json-comments: 2.0.1

  react-devtools-core@6.1.1:
    dependencies:
      shell-quote: 1.8.2
      ws: 7.5.10
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  react-dom@19.0.0(react@19.0.0):
    dependencies:
      react: 19.0.0
      scheduler: 0.25.0

  react-fast-compare@3.2.2: {}

  react-freeze@1.0.4(react@19.0.0):
    dependencies:
      react: 19.0.0

  react-is@16.13.1: {}

  react-is@18.3.1: {}

  react-is@19.1.0: {}

  react-native-edge-to-edge@1.6.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  react-native-elements@3.4.3(react-native-safe-area-context@5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-vector-icons@10.2.0)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      '@types/react-native-vector-icons': 6.4.18
      color: 3.2.1
      deepmerge: 4.3.1
      hoist-non-react-statics: 3.3.2
      lodash.isequal: 4.5.0
      opencollective-postinstall: 2.0.3
      react-native-ratings: 8.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-safe-area-context: 5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-size-matters: 0.3.1(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))
      react-native-vector-icons: 10.2.0
    transitivePeerDependencies:
      - react
      - react-native

  react-native-email-link@1.16.1(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  react-native-gesture-handler@2.24.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      '@egjs/hammerjs': 2.0.17
      hoist-non-react-statics: 3.3.2
      invariant: 2.2.4
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  react-native-get-random-values@1.11.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)):
    dependencies:
      fast-base64-decode: 1.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  react-native-is-edge-to-edge@1.1.7(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  react-native-onboarding-swiper@1.3.0(prop-types@15.8.1)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      prop-types: 15.8.1
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      tinycolor2: 1.6.0

  react-native-prompt-android@1.1.0: {}

  react-native-ratings@8.0.4(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      lodash: 4.17.21
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  react-native-reanimated@3.17.5(@babel/core@7.26.9)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      '@babel/core': 7.26.9
      '@babel/plugin-transform-arrow-functions': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-class-properties': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-classes': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.26.6(@babel/core@7.26.9)
      '@babel/plugin-transform-optional-chaining': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-shorthand-properties': 7.25.9(@babel/core@7.26.9)
      '@babel/plugin-transform-template-literals': 7.26.8(@babel/core@7.26.9)
      '@babel/plugin-transform-unicode-regex': 7.25.9(@babel/core@7.26.9)
      '@babel/preset-typescript': 7.26.0(@babel/core@7.26.9)
      convert-source-map: 2.0.0
      invariant: 2.2.4
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-native-is-edge-to-edge: 1.1.7(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
    transitivePeerDependencies:
      - supports-color

  react-native-safe-area-context@5.4.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  react-native-screens@4.10.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      react: 19.0.0
      react-freeze: 1.0.4(react@19.0.0)
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      warn-once: 0.1.1

  react-native-size-matters@0.3.1(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)):
    dependencies:
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  react-native-svg@15.11.2(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      css-select: 5.1.0
      css-tree: 1.1.3
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      warn-once: 0.1.1

  react-native-url-polyfill@2.0.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)):
    dependencies:
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      whatwg-url-without-unicode: 8.0.0-3

  react-native-vector-icons@10.2.0:
    dependencies:
      prop-types: 15.8.1
      yargs: 16.2.0

  react-native-web@0.20.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    dependencies:
      '@babel/runtime': 7.26.9
      '@react-native/normalize-colors': 0.74.89
      fbjs: 3.0.5
      inline-style-prefixer: 7.0.1
      memoize-one: 6.0.0
      nullthrows: 1.1.1
      postcss-value-parser: 4.2.0
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      styleq: 0.1.3
    transitivePeerDependencies:
      - encoding

  react-native-webview@13.13.5(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      escape-string-regexp: 4.0.0
      invariant: 2.2.4
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)

  react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0):
    dependencies:
      '@jest/create-cache-key-function': 29.7.0
      '@react-native/assets-registry': 0.79.2
      '@react-native/codegen': 0.79.2(@babel/core@7.26.9)
      '@react-native/community-cli-plugin': 0.79.2
      '@react-native/gradle-plugin': 0.79.2
      '@react-native/js-polyfills': 0.79.2
      '@react-native/normalize-colors': 0.79.2
      '@react-native/virtualized-lists': 0.79.2(@types/react@19.0.14)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      abort-controller: 3.0.0
      anser: 1.4.10
      ansi-regex: 5.0.1
      babel-jest: 29.7.0(@babel/core@7.26.9)
      babel-plugin-syntax-hermes-parser: 0.25.1
      base64-js: 1.5.1
      chalk: 4.1.2
      commander: 12.1.0
      event-target-shim: 5.0.1
      flow-enums-runtime: 0.0.6
      glob: 7.2.3
      invariant: 2.2.4
      jest-environment-node: 29.7.0
      memoize-one: 5.2.1
      metro-runtime: 0.82.3
      metro-source-map: 0.82.3
      nullthrows: 1.1.1
      pretty-format: 29.7.0
      promise: 8.3.0
      react: 19.0.0
      react-devtools-core: 6.1.1
      react-refresh: 0.14.2
      regenerator-runtime: 0.13.11
      scheduler: 0.25.0
      semver: 7.7.1
      stacktrace-parser: 0.1.11
      whatwg-fetch: 3.6.20
      ws: 6.2.3
      yargs: 17.7.2
    optionalDependencies:
      '@types/react': 19.0.14
    transitivePeerDependencies:
      - '@babel/core'
      - '@react-native-community/cli'
      - bufferutil
      - supports-color
      - utf-8-validate

  react-reconciler@0.31.0(react@19.0.0):
    dependencies:
      react: 19.0.0
      scheduler: 0.25.0

  react-refresh@0.14.2: {}

  react-server-dom-webpack@19.0.0(react-dom@19.0.0(react@19.0.0))(react@19.0.0)(webpack@5.98.0):
    dependencies:
      acorn-loose: 8.4.0
      neo-async: 2.6.2
      react: 19.0.0
      react-dom: 19.0.0(react@19.0.0)
      webpack: 5.98.0
      webpack-sources: 3.2.3

  react-test-renderer@19.0.0(react@19.0.0):
    dependencies:
      react: 19.0.0
      react-is: 19.1.0
      scheduler: 0.25.0

  react@19.0.0: {}

  reactotron-core-client@2.9.7:
    dependencies:
      reactotron-core-contract: 0.2.5

  reactotron-core-contract@0.2.5: {}

  reactotron-mst@3.1.11(mobx-state-tree@5.3.0(mobx@6.10.2))(mobx@6.10.2)(reactotron-core-client@2.9.7):
    dependencies:
      mobx: 6.10.2
      mobx-state-tree: 5.3.0(mobx@6.10.2)
      ramda: 0.28.0
      reactotron-core-client: 2.9.7

  reactotron-react-js@3.3.16:
    dependencies:
      reactotron-core-client: 2.9.7
      stacktrace-js: 2.0.1

  reactotron-react-native@5.1.13(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)):
    dependencies:
      mitt: 3.0.1
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      reactotron-core-client: 2.9.7

  recyclerlistview@4.2.3(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      lodash.debounce: 4.0.8
      prop-types: 15.8.1
      react: 19.0.0
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      ts-object-utils: 0.0.5

  redent@3.0.0:
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0

  reflect.getprototypeof@1.0.10:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      get-proto: 1.0.1
      which-builtin-type: 1.2.1

  regenerate-unicode-properties@10.2.0:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.13.11: {}

  regenerator-runtime@0.14.1: {}

  regenerator-transform@0.15.2:
    dependencies:
      '@babel/runtime': 7.26.9

  regexp.prototype.flags@1.5.4:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-errors: 1.3.0
      get-proto: 1.0.1
      gopd: 1.2.0
      set-function-name: 2.0.2

  regexpp@3.2.0: {}

  regexpu-core@6.2.0:
    dependencies:
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.2.0
      regjsgen: 0.8.0
      regjsparser: 0.12.0
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.2.0

  regjsgen@0.8.0: {}

  regjsparser@0.12.0:
    dependencies:
      jsesc: 3.0.2

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  requireg@0.2.2:
    dependencies:
      nested-error-stacks: 2.0.1
      rc: 1.2.8
      resolve: 1.7.1

  requires-port@1.0.0: {}

  resolve-cwd@3.0.0:
    dependencies:
      resolve-from: 5.0.0

  resolve-from@3.0.0: {}

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve-workspace-root@2.0.0: {}

  resolve.exports@2.0.3: {}

  resolve@1.22.10:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@1.7.1:
    dependencies:
      path-parse: 1.0.7

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.16.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@2.0.0:
    dependencies:
      onetime: 2.0.1
      signal-exit: 3.0.7

  reusify@1.1.0: {}

  rimraf@2.7.1:
    dependencies:
      glob: 7.2.3

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  rtl-detect@1.1.2: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rxjs@7.8.2:
    dependencies:
      tslib: 2.8.1

  safe-array-concat@1.1.3:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      get-intrinsic: 1.3.0
      has-symbols: 1.1.0
      isarray: 2.0.5

  safe-buffer@5.2.1: {}

  safe-push-apply@1.0.0:
    dependencies:
      es-errors: 1.3.0
      isarray: 2.0.5

  safe-regex-test@1.1.0:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-regex: 1.2.1

  safer-buffer@2.1.2: {}

  sax@1.4.1: {}

  saxes@6.0.0:
    dependencies:
      xmlchars: 2.2.0

  scheduler@0.25.0: {}

  schema-utils@4.3.0:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.17.1
      ajv-formats: 2.1.1(ajv@8.17.1)
      ajv-keywords: 5.1.0(ajv@8.17.1)

  semver@6.3.1: {}

  semver@7.7.1: {}

  send@0.19.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serialize-error@2.1.0: {}

  serialize-javascript@6.0.2:
    dependencies:
      randombytes: 2.1.0

  serve-static@1.16.2:
    dependencies:
      encodeurl: 2.0.0
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.19.0
    transitivePeerDependencies:
      - supports-color

  server-only@0.0.1: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-proto@1.0.0:
    dependencies:
      dunder-proto: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1

  setimmediate@1.0.5: {}

  setprototypeof@1.2.0: {}

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shell-quote@1.8.2: {}

  side-channel-list@1.0.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4

  side-channel-map@1.0.1:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4

  side-channel-weakmap@1.0.2:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      object-inspect: 1.13.4
      side-channel-map: 1.0.1

  side-channel@1.1.0:
    dependencies:
      es-errors: 1.3.0
      object-inspect: 1.13.4
      side-channel-list: 1.0.0
      side-channel-map: 1.0.1
      side-channel-weakmap: 1.0.2

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  simple-plist@1.3.1:
    dependencies:
      bplist-creator: 0.1.0
      bplist-parser: 0.3.1
      plist: 3.1.0

  simple-swizzle@0.2.2:
    dependencies:
      is-arrayish: 0.3.2

  sisteransi@1.0.5: {}

  slash@2.0.0: {}

  slash@3.0.0: {}

  slash@5.1.0: {}

  slugify@1.6.6: {}

  source-map-js@1.2.1: {}

  source-map-support@0.5.13:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.5.6: {}

  source-map@0.5.7: {}

  source-map@0.6.1: {}

  split-on-first@1.1.0: {}

  sprintf-js@1.0.3: {}

  stack-generator@2.0.10:
    dependencies:
      stackframe: 1.3.4

  stack-utils@2.0.6:
    dependencies:
      escape-string-regexp: 2.0.0

  stackframe@1.3.4: {}

  stacktrace-gps@3.1.2:
    dependencies:
      source-map: 0.5.6
      stackframe: 1.3.4

  stacktrace-js@2.0.1:
    dependencies:
      error-stack-parser: 2.1.4
      stack-generator: 2.0.10
      stacktrace-gps: 3.1.2

  stacktrace-js@2.0.2:
    dependencies:
      error-stack-parser: 2.1.4
      stack-generator: 2.0.10
      stacktrace-gps: 3.1.2

  stacktrace-parser@0.1.11:
    dependencies:
      type-fest: 0.7.1

  statuses@1.5.0: {}

  statuses@2.0.1: {}

  stream-buffers@2.2.0: {}

  strict-uri-encode@2.0.0: {}

  string-length@4.0.2:
    dependencies:
      char-regex: 1.0.2
      strip-ansi: 6.0.1

  string-length@5.0.1:
    dependencies:
      char-regex: 2.0.2
      strip-ansi: 7.1.0

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string.prototype.matchall@4.0.12:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      get-intrinsic: 1.3.0
      gopd: 1.2.0
      has-symbols: 1.1.0
      internal-slot: 1.1.0
      regexp.prototype.flags: 1.5.4
      set-function-name: 2.0.2
      side-channel: 1.1.0

  string.prototype.trim@1.2.10:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-data-property: 1.1.4
      define-properties: 1.2.1
      es-abstract: 1.23.9
      es-object-atoms: 1.1.1
      has-property-descriptors: 1.0.2

  string.prototype.trimend@1.0.9:
    dependencies:
      call-bind: 1.0.8
      call-bound: 1.0.3
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.8
      define-properties: 1.2.1
      es-object-atoms: 1.1.1

  strip-ansi@5.2.0:
    dependencies:
      ansi-regex: 4.1.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.1.0

  strip-bom@3.0.0: {}

  strip-bom@4.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-indent@3.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-json-comments@2.0.1: {}

  strip-json-comments@3.1.1: {}

  strnum@1.1.2: {}

  structured-headers@0.4.1: {}

  styleq@0.1.3: {}

  sucrase@3.35.0:
    dependencies:
      '@jridgewell/gen-mapping': 0.3.8
      commander: 4.1.1
      glob: 10.4.5
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13

  sudo-prompt@8.2.5: {}

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-hyperlinks@2.3.0:
    dependencies:
      has-flag: 4.0.0
      supports-color: 7.2.0

  supports-preserve-symlinks-flag@1.0.0: {}

  symbol-tree@3.2.4: {}

  tapable@2.2.1: {}

  tar@7.4.3:
    dependencies:
      '@isaacs/fs-minipass': 4.0.1
      chownr: 3.0.0
      minipass: 7.1.2
      minizlib: 3.0.2
      mkdirp: 3.0.1
      yallist: 5.0.0

  temp-dir@2.0.0: {}

  terminal-link@2.1.1:
    dependencies:
      ansi-escapes: 4.3.2
      supports-hyperlinks: 2.3.0

  terser-webpack-plugin@5.3.14(webpack@5.98.0):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      jest-worker: 27.5.1
      schema-utils: 4.3.0
      serialize-javascript: 6.0.2
      terser: 5.39.0
      webpack: 5.98.0

  terser@5.39.0:
    dependencies:
      '@jridgewell/source-map': 0.3.6
      acorn: 8.14.0
      commander: 2.20.3
      source-map-support: 0.5.21

  test-exclude@6.0.0:
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2

  text-table@0.2.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  throat@5.0.0: {}

  tiny-inflate@1.0.3: {}

  tinycolor2@1.6.0: {}

  tmp@0.0.33:
    dependencies:
      os-tmpdir: 1.0.2

  tmpl@1.0.5: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toidentifier@1.0.1: {}

  tough-cookie@4.1.4:
    dependencies:
      psl: 1.15.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10

  tr46@0.0.3: {}

  tr46@3.0.0:
    dependencies:
      punycode: 2.3.1

  ts-interface-checker@0.1.13: {}

  ts-jest@29.2.6(@babel/core@7.26.9)(@jest/transform@29.7.0)(@jest/types@29.6.3)(babel-jest@29.7.0(@babel/core@7.26.9))(jest@29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3)))(typescript@5.8.3):
    dependencies:
      bs-logger: 0.2.6
      ejs: 3.1.10
      fast-json-stable-stringify: 2.1.0
      jest: 29.7.0(@types/node@22.13.5)(ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3))
      jest-util: 29.7.0
      json5: 2.2.3
      lodash.memoize: 4.1.2
      make-error: 1.3.6
      semver: 7.7.1
      typescript: 5.8.3
      yargs-parser: 21.1.1
    optionalDependencies:
      '@babel/core': 7.26.9
      '@jest/transform': 29.7.0
      '@jest/types': 29.6.3
      babel-jest: 29.7.0(@babel/core@7.26.9)

  ts-node@10.9.2(@types/node@22.13.5)(typescript@5.8.3):
    dependencies:
      '@cspotcode/source-map-support': 0.8.1
      '@tsconfig/node10': 1.0.11
      '@tsconfig/node12': 1.0.11
      '@tsconfig/node14': 1.0.3
      '@tsconfig/node16': 1.0.4
      '@types/node': 22.13.5
      acorn: 8.14.0
      acorn-walk: 8.3.4
      arg: 4.1.3
      create-require: 1.1.1
      diff: 4.0.2
      make-error: 1.3.6
      typescript: 5.8.3
      v8-compile-cache-lib: 3.0.1
      yn: 3.1.1

  ts-object-utils@0.0.5: {}

  tsconfig-paths@3.15.0:
    dependencies:
      '@types/json5': 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@1.14.1: {}

  tslib@2.8.1: {}

  tsutils@3.21.0(typescript@5.8.3):
    dependencies:
      tslib: 1.14.1
      typescript: 5.8.3

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-detect@4.0.8: {}

  type-fest@0.20.2: {}

  type-fest@0.21.3: {}

  type-fest@0.7.1: {}

  typed-array-buffer@1.0.3:
    dependencies:
      call-bound: 1.0.3
      es-errors: 1.3.0
      is-typed-array: 1.1.15

  typed-array-byte-length@1.0.3:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15

  typed-array-byte-offset@1.0.4:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      has-proto: 1.2.0
      is-typed-array: 1.1.15
      reflect.getprototypeof: 1.0.10

  typed-array-length@1.0.7:
    dependencies:
      call-bind: 1.0.8
      for-each: 0.3.5
      gopd: 1.2.0
      is-typed-array: 1.1.15
      possible-typed-array-names: 1.1.0
      reflect.getprototypeof: 1.0.10

  typescript@5.8.3: {}

  ua-parser-js@1.0.40: {}

  ulid@2.3.0: {}

  unbox-primitive@1.1.0:
    dependencies:
      call-bound: 1.0.3
      has-bigints: 1.1.0
      has-symbols: 1.1.0
      which-boxed-primitive: 1.1.1

  undici-types@6.20.0: {}

  undici@6.21.2: {}

  unicode-canonical-property-names-ecmascript@2.0.1: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.1
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.2.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  unicode-trie@2.0.0:
    dependencies:
      pako: 0.2.9
      tiny-inflate: 1.0.3

  unique-string@2.0.0:
    dependencies:
      crypto-random-string: 2.0.0

  universalify@0.2.0: {}

  universalify@2.0.1: {}

  unpipe@1.0.0: {}

  update-browserslist-db@1.1.3(browserslist@4.24.4):
    dependencies:
      browserslist: 4.24.4
      escalade: 3.2.0
      picocolors: 1.1.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  use-latest-callback@0.2.3(react@19.0.0):
    dependencies:
      react: 19.0.0

  use-sync-external-store@1.4.0(react@19.0.0):
    dependencies:
      react: 19.0.0

  use-sync-external-store@1.5.0(react@19.0.0):
    dependencies:
      react: 19.0.0

  utils-merge@1.0.1: {}

  uuid@7.0.3: {}

  uuid@9.0.1: {}

  v8-compile-cache-lib@3.0.1: {}

  v8-compile-cache@2.4.0: {}

  v8-to-istanbul@9.3.0:
    dependencies:
      '@jridgewell/trace-mapping': 0.3.25
      '@types/istanbul-lib-coverage': 2.0.6
      convert-source-map: 2.0.0

  validate-npm-package-name@5.0.1: {}

  vary@1.1.2: {}

  victory-native@41.16.1(@shopify/react-native-skia@2.0.0-next.4(react-native-reanimated@3.17.5(@babel/core@7.26.9)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(@types/react@19.0.14)(react-native-gesture-handler@2.24.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native-reanimated@3.17.5(@babel/core@7.26.9)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0):
    dependencies:
      '@shopify/react-native-skia': 2.0.0-next.4(react-native-reanimated@3.17.5(@babel/core@7.26.9)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0))(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      d3-scale: 4.0.2
      d3-shape: 3.2.0
      d3-zoom: 3.0.0
      its-fine: 1.2.5(@types/react@19.0.14)(react@19.0.0)
      react: 19.0.0
      react-fast-compare: 3.2.2
      react-native: 0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0)
      react-native-gesture-handler: 2.24.0(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
      react-native-reanimated: 3.17.5(@babel/core@7.26.9)(react-native@0.79.2(@babel/core@7.26.9)(@types/react@19.0.14)(react@19.0.0))(react@19.0.0)
    transitivePeerDependencies:
      - '@types/react'

  vlq@1.0.1: {}

  w3c-xmlserializer@4.0.0:
    dependencies:
      xml-name-validator: 4.0.0

  walker@1.0.8:
    dependencies:
      makeerror: 1.0.12

  warn-once@0.1.1: {}

  watchpack@2.4.2:
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11

  wcwidth@1.0.1:
    dependencies:
      defaults: 1.0.4

  webidl-conversions@3.0.1: {}

  webidl-conversions@5.0.0: {}

  webidl-conversions@7.0.0: {}

  webpack-sources@3.2.3: {}

  webpack@5.98.0:
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.6
      '@webassemblyjs/ast': 1.14.1
      '@webassemblyjs/wasm-edit': 1.14.1
      '@webassemblyjs/wasm-parser': 1.14.1
      acorn: 8.14.0
      browserslist: 4.24.4
      chrome-trace-event: 1.0.4
      enhanced-resolve: 5.18.1
      es-module-lexer: 1.6.0
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 4.3.0
      tapable: 2.2.1
      terser-webpack-plugin: 5.3.14(webpack@5.98.0)
      watchpack: 2.4.2
      webpack-sources: 3.2.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  whatwg-encoding@2.0.0:
    dependencies:
      iconv-lite: 0.6.3

  whatwg-fetch@3.6.20: {}

  whatwg-mimetype@3.0.0: {}

  whatwg-url-without-unicode@8.0.0-3:
    dependencies:
      buffer: 5.7.1
      punycode: 2.3.1
      webidl-conversions: 5.0.0

  whatwg-url@11.0.0:
    dependencies:
      tr46: 3.0.0
      webidl-conversions: 7.0.0

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which-boxed-primitive@1.1.1:
    dependencies:
      is-bigint: 1.1.0
      is-boolean-object: 1.2.2
      is-number-object: 1.1.1
      is-string: 1.1.1
      is-symbol: 1.1.1

  which-builtin-type@1.2.1:
    dependencies:
      call-bound: 1.0.3
      function.prototype.name: 1.1.8
      has-tostringtag: 1.0.2
      is-async-function: 2.1.1
      is-date-object: 1.1.0
      is-finalizationregistry: 1.1.1
      is-generator-function: 1.1.0
      is-regex: 1.2.1
      is-weakref: 1.1.1
      isarray: 2.0.5
      which-boxed-primitive: 1.1.1
      which-collection: 1.0.2
      which-typed-array: 1.1.18

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.4

  which-typed-array@1.1.18:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.8
      call-bound: 1.0.3
      for-each: 0.3.5
      gopd: 1.2.0
      has-tostringtag: 1.0.2

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  wonka@6.3.4: {}

  word-wrap@1.2.5: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  write-file-atomic@4.0.2:
    dependencies:
      imurmurhash: 0.1.4
      signal-exit: 3.0.7

  ws@6.2.3:
    dependencies:
      async-limiter: 1.0.1

  ws@7.5.10: {}

  ws@8.18.1: {}

  xcode@3.0.1:
    dependencies:
      simple-plist: 1.3.1
      uuid: 7.0.3

  xml-name-validator@4.0.0: {}

  xml2js@0.6.0:
    dependencies:
      sax: 1.4.1
      xmlbuilder: 11.0.1

  xmlbuilder@11.0.1: {}

  xmlbuilder@15.1.1: {}

  xmlchars@2.2.0: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@5.0.0: {}

  yaml@2.7.0: {}

  yargs-parser@20.2.9: {}

  yargs-parser@21.1.1: {}

  yargs@16.2.0:
    dependencies:
      cliui: 7.0.4
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.2.0
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yn@3.1.1: {}

  yocto-queue@0.1.0: {}
