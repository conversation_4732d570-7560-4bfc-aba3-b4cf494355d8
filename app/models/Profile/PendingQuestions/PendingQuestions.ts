import {types} from "mobx-state-tree"
import {ProfileQuestion, ProfileQuestionModel} from "app/models/Profile/ProfileQuestion"
import {supabase} from "app/config/config.base"
import Config from "app/config"
import {ErrorExperience, ErrorType, reportSentryError} from "app/utils/crashReporting"

function getQueueKey(userOrHousehold: string, targetId: string): string {
  return `${userOrHousehold}-${targetId}`
}

/**
 * PendingQuestionsStore - Manages the pending questions queue.
 */
export const PendingQuestionsStoreModel = types
  .model("PendingQuestionsStore", {
    lastRefreshTime: types.maybeNull(types.Date),
    pendingQuestions: types.array(types.late(() => ProfileQuestionModel))
  })
  .views((self) => ({
    /**
     * Get the count of pending questions for a specific user/household and targetId.
     */
    getPendingQuestionCount(userOrHousehold: string, targetId: string): number {
      // const key = getQueue<PERSON>ey(userOrHousehold, targetId)
      const queue = self.pendingQuestions
      return queue?.length || 0
    },

    /**
     * List pending questions for a specific user/household and targetId.
     */
    listPendingQuestions(
      userOrHousehold: string,
      targetId: string,
    ): ProfileQuestion[] {
      // const key = getQueueKey(userOrHousehold, targetId)
      return self.pendingQuestions || []
    },

    /**
     * Peek at the oldest question for a given user/household and targetId without removing it.
     */
    peekOldestPendingQuestion(
      userOrHousehold: string,
      targetId: string,
    ): ProfileQuestion | undefined {
      // const key = getQueueKey(userOrHousehold, targetId)
      const questions = self.pendingQuestions
      return questions?.[0]
    },

    /**
     * Peek at the newest question for a given user/household and targetId without removing it.
     */
    peekNewestPendingQuestion(
      userOrHousehold: string,
      targetId: string,
    ): ProfileQuestion | undefined {
      // const key = getQueueKey(userOrHousehold, targetId)
      const questions = self.pendingQuestions
      return questions?.[questions.length - 1]
    }
  }))
  .actions((self) => ({
    updatePendingQuestions(questions: ProfileQuestion[]){
      // @ts-ignore
      self.pendingQuestions = questions
      self.lastRefreshTime = new Date()
    },
    /**
     * Add (enqueue) a question to the queue for the given user/household and targetId.
     */
    addPendingQuestion(
      question: ProfileQuestion,
      userOrHousehold: string,
      targetId: string,
    ) {
      // const key = getQueueKey(userOrHousehold, targetId)
      // if (!self.pendingQuestions.has(key)) {
      //   self.pendingQuestions.set(key, [])
      // }
      self.pendingQuestions?.push(question)
    },

    /**
     * Add a question to the front of the queue for the given user/household and targetId.
     */
    addPendingQuestionToFront(
      question: ProfileQuestion,
      userOrHousehold: string,
      targetId: string,
    ) {
      // const key = getQueueKey(userOrHousehold, targetId)
      // if (!self.pendingQuestions.has(key)) {
      //   self.pendingQuestions.set(key, [])
      // }
      self.pendingQuestions.unshift(question)
    },

    /**
     * Remove (dequeue) the oldest question for a given user/household and targetId.
     */
    removeOldestPendingQuestion(
      userOrHousehold: string,
      targetId: string,
    ): ProfileQuestion | undefined {
      // const key = getQueueKey(userOrHousehold, targetId)
      return self.pendingQuestions?.shift()
    },

    /**
     * Remove (dequeue) the newest question for a given user/household and targetId.
     */
    removeNewestPendingQuestion(
      userOrHousehold: string,
      targetId: string,
    ): ProfileQuestion | undefined {
      const key = getQueueKey(userOrHousehold, targetId)
      return self.pendingQuestions.get(key)?.pop()
    },
    async updateFromServer(force: boolean){
      const now = new Date();
      const nowMinusMinRefreshTime = new Date(now.getTime() - Config.minPendingQuestionsRefreshTime);
      const shouldUpdate = force || nowMinusMinRefreshTime > self.lastRefreshTime
      // Only update if forced or if the min refresh time has elapsed
      if (!shouldUpdate){
        return
      }
      const { data, error } = await supabase
          .from('pending_questions')
          .select(`
    *,
    profile_questions(
      question_text,
      allow_multi_select,
        profile_question_options(answer_text,
          question_option_descriptors(descriptor_id))
    )
  `)
          .order('position', { ascending: true });

      if(error){
        reportSentryError(error, ErrorType.HANDLED, ErrorExperience.PendingQuestions)
        return
      }
      data?.map((item)=>{
        item.allow_multi_select = item.profile_questions.allow_multi_select
        item.question_text = item.profile_questions.question_text
        item.options = item.profile_questions.profile_question_options
        item.profile_questions = null
        return item
      })
      this.updatePendingQuestions(data)
    }
  }))
